<div class="undo-redo-toolbar" (keydown)="onKeyDown($event)" tabindex="0">
  <div class="toolbar-group">
    <!-- Undo Button -->
    <button
      mat-icon-button
      [ngClass]="getUndoButtonClasses()"
      [disabled]="!undoRedoState.canUndo"
      [matTooltip]="getUndoTooltip() | translate"
      matTooltipClass="tooltip-custom"
      (click)="onUndo()"
      aria-label="Undo"
    >
      <mat-icon>undo</mat-icon>
    </button>

    <!-- Redo Button -->
    <button
      mat-icon-button
      [ngClass]="getRedoButtonClasses()"
      [disabled]="!undoRedoState.canRedo"
      [matTooltip]="getRedoTooltip() | translate"
      matTooltipClass="tooltip-custom"
      (click)="onRedo()"
      aria-label="Redo"
    >
      <mat-icon>redo</mat-icon>
    </button>
  </div>

  <!-- Optional: Display current operation info -->
  <div class="operation-info" *ngIf="undoRedoState.undoText || undoRedoState.redoText">
    <span class="undo-info" *ngIf="undoRedoState.undoText">
      {{ 'toolbar.undo.next' | translate }}: {{ undoRedoState.undoText }}
    </span>
    <span class="redo-info" *ngIf="undoRedoState.redoText">
      {{ 'toolbar.redo.next' | translate }}: {{ undoRedoState.redoText }}
    </span>
  </div>
</div>
