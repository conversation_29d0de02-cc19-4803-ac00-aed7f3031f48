{"ast": null, "code": "import { catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AttributeApiService {\n  constructor(http) {\n    this.http = http;\n    this.backendUrl = environment.backEndUrl;\n  }\n  /**\n   * Create a new attribute in the database\n   *\n   * @param {Attribute} attribute The attribute to create\n   * @returns {Observable<Attribute>} An Observable that emits an object representing the created attribute, or an error\n   *\n   * @memberOf AttributeApiService\n   */\n  createAttribute(attribute) {\n    return this.http.post(this.backendUrl + '/Attribute', attribute).pipe(catchError(error => {\n      console.error('Error creating Attribute:', error);\n      throw error;\n    }));\n  }\n  /**\n   * Update  an existing attribute in the database\n   *\n   * @param {Attribute} attribute  The attribute with updated information\n   * @returns {Observable<Attribute>}  An observable that resolves to the updated attribute\n   *\n   * @memberOf AttributeApiService\n   */\n  updateAttribute(attribute) {\n    return this.http.patch(this.backendUrl + '/Attribute', attribute).pipe(catchError(error => {\n      console.error('Error updating attribute:', error);\n      throw error;\n    }));\n  }\n  /**\n   * Delete an attribute from the database\n   *\n   * @param {number} attributeIds The ID of the attribute to delete\n   * @returns {Observable<Attribute>} An observable that resolves to the deleted attribute\n   *\n   * @memberOf AttributeApiService\n   */\n  deleteAttributes(attributeIds) {\n    return this.http.post(this.backendUrl + `/Attribute/deleteMultiple`, attributeIds).pipe(catchError(error => {\n      console.error('Error deleting attribute:', error);\n      throw error;\n    }));\n  }\n  /**\n   * Alias for deleteAttributes to match the interface expected by DatabaseSyncService\n   */\n  deleteAttribute(attributeIds) {\n    return this.deleteAttributes(attributeIds);\n  }\n  /**\n   * Undo attribute deletion\n   *\n   * @param {number} idAttribute attribute id\n   * @returns {Observable<Attribute>}\n   *\n   * @memberOf AttributeApiService\n   */\n  undoAttributeDelete(idAttribute) {\n    return this.http.patch(this.backendUrl + `/Attribute/${idAttribute}`, {}).pipe(catchError(error => {\n      console.error('Error undo attribute deletion:', error);\n      throw error;\n    }));\n  }\n  static #_ = this.ɵfac = function AttributeApiService_Factory(t) {\n    return new (t || AttributeApiService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AttributeApiService,\n    factory: AttributeApiService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["catchError", "environment", "AttributeApiService", "constructor", "http", "backendUrl", "backEndUrl", "createAttribute", "attribute", "post", "pipe", "error", "console", "updateAttribute", "patch", "deleteAttributes", "attributeIds", "deleteAttribute", "undoAttributeDelete", "idAttribute", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\api\\attribute-api.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { Attribute, AttributeDTO } from 'src/app/shared/model/attribute';\r\nimport { environment } from 'src/environments/environment';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AttributeApiService {\r\n  private backendUrl: string = environment.backEndUrl;\r\n  constructor(private http: HttpClient) {}\r\n\r\n  /**\r\n   * Create a new attribute in the database\r\n   *\r\n   * @param {Attribute} attribute The attribute to create\r\n   * @returns {Observable<Attribute>} An Observable that emits an object representing the created attribute, or an error\r\n   *\r\n   * @memberOf AttributeApiService\r\n   */\r\n  createAttribute(attribute: AttributeDTO): Observable<AttributeDTO> {\r\n    return this.http\r\n      .post<AttributeDTO>(this.backendUrl + '/Attribute', attribute)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error creating Attribute:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Update  an existing attribute in the database\r\n   *\r\n   * @param {Attribute} attribute  The attribute with updated information\r\n   * @returns {Observable<Attribute>}  An observable that resolves to the updated attribute\r\n   *\r\n   * @memberOf AttributeApiService\r\n   */\r\n  updateAttribute(attribute: Attribute): Observable<Attribute> {\r\n    return this.http\r\n      .patch<Attribute>(this.backendUrl + '/Attribute', attribute)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error updating attribute:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Delete an attribute from the database\r\n   *\r\n   * @param {number} attributeIds The ID of the attribute to delete\r\n   * @returns {Observable<Attribute>} An observable that resolves to the deleted attribute\r\n   *\r\n   * @memberOf AttributeApiService\r\n   */\r\n  deleteAttributes(attributeIds: number[]): Observable<void> {\r\n    return this.http\r\n      .post<void>(this.backendUrl + `/Attribute/deleteMultiple`, attributeIds)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error deleting attribute:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Alias for deleteAttributes to match the interface expected by DatabaseSyncService\r\n   */\r\n  deleteAttribute(attributeIds: number[]): Observable<void> {\r\n    return this.deleteAttributes(attributeIds);\r\n  }\r\n\r\n  /**\r\n   * Undo attribute deletion\r\n   *\r\n   * @param {number} idAttribute attribute id\r\n   * @returns {Observable<Attribute>}\r\n   *\r\n   * @memberOf AttributeApiService\r\n   */\r\n  undoAttributeDelete(idAttribute: number): Observable<Attribute> {\r\n    return this.http\r\n      .patch<Attribute>(this.backendUrl + `/Attribute/${idAttribute}`, {})\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error undo attribute deletion:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,UAAU,QAAQ,gBAAgB;AAE3C,SAASC,WAAW,QAAQ,8BAA8B;;;AAI1D,OAAM,MAAOC,mBAAmB;EAE9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IADhB,KAAAC,UAAU,GAAWJ,WAAW,CAACK,UAAU;EACZ;EAEvC;;;;;;;;EAQAC,eAAeA,CAACC,SAAuB;IACrC,OAAO,IAAI,CAACJ,IAAI,CACbK,IAAI,CAAe,IAAI,CAACJ,UAAU,GAAG,YAAY,EAAEG,SAAS,CAAC,CAC7DE,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;EAQAE,eAAeA,CAACL,SAAoB;IAClC,OAAO,IAAI,CAACJ,IAAI,CACbU,KAAK,CAAY,IAAI,CAACT,UAAU,GAAG,YAAY,EAAEG,SAAS,CAAC,CAC3DE,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;EAQAI,gBAAgBA,CAACC,YAAsB;IACrC,OAAO,IAAI,CAACZ,IAAI,CACbK,IAAI,CAAO,IAAI,CAACJ,UAAU,GAAG,2BAA2B,EAAEW,YAAY,CAAC,CACvEN,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAM,eAAeA,CAACD,YAAsB;IACpC,OAAO,IAAI,CAACD,gBAAgB,CAACC,YAAY,CAAC;EAC5C;EAEA;;;;;;;;EAQAE,mBAAmBA,CAACC,WAAmB;IACrC,OAAO,IAAI,CAACf,IAAI,CACbU,KAAK,CAAY,IAAI,CAACT,UAAU,GAAG,cAAcc,WAAW,EAAE,EAAE,EAAE,CAAC,CACnET,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAAC,QAAAS,CAAA,G;qBArFUlB,mBAAmB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAnBvB,mBAAmB;IAAAwB,OAAA,EAAnBxB,mBAAmB,CAAAyB,IAAA;IAAAC,UAAA,EAFlB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}