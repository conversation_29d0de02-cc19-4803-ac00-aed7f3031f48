import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, forkJoin, of, throwError } from 'rxjs';
import { catchError, map, retry, timeout } from 'rxjs/operators';
import { AttributeApiService } from '../api/attribute-api.service';
import { CardinalityApiService } from '../api/cardinality-api.service';
import { ClassApiService } from '../api/class-api.service';
import { CommentApiService } from '../api/comment-api.service';
import { EnumerationApiService } from '../api/enumeration-api.service';
import { FolderApiService } from '../api/folder-api.service';
import { LiteralApiService } from '../api/literal-api.service';

export interface DatabaseSyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete' | 'restore';
  entityType:
    | 'class'
    | 'enumeration'
    | 'comment'
    | 'link'
    | 'attribute'
    | 'literal'
    | 'folder';
  entityId: number;
  data?: any;
  rollbackData?: any;
  timestamp: number;
}

export interface SyncResult {
  success: boolean;
  operation: DatabaseSyncOperation;
  result?: any;
  error?: any;
}

export interface BatchSyncResult {
  success: boolean;
  results: SyncResult[];
  errors: SyncResult[];
  totalOperations: number;
}

@Injectable({
  providedIn: 'root',
})
export class DatabaseSyncService {
  private _pendingOperations = new BehaviorSubject<DatabaseSyncOperation[]>([]);
  private _syncInProgress = new BehaviorSubject<boolean>(false);
  private _operationCounter = 0;

  constructor(
    private classApiService: ClassApiService,
    private enumerationApiService: EnumerationApiService,
    private commentApiService: CommentApiService,
    private cardinalityApiService: CardinalityApiService,
    private attributeApiService: AttributeApiService,
    private literalApiService: LiteralApiService,
    private folderApiService: FolderApiService
  ) {}

  /**
   * Observable for pending operations
   */
  get pendingOperations$(): Observable<DatabaseSyncOperation[]> {
    return this._pendingOperations.asObservable();
  }

  /**
   * Observable for sync status
   */
  get syncInProgress$(): Observable<boolean> {
    return this._syncInProgress.asObservable();
  }

  /**
   * Create a database sync operation for class entities
   */
  createClassOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation('class', type, entityId, data, rollbackData);
  }

  /**
   * Create a database sync operation for enumeration entities
   */
  createEnumerationOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation(
      'enumeration',
      type,
      entityId,
      data,
      rollbackData
    );
  }

  /**
   * Create a database sync operation for comment entities
   */
  createCommentOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation('comment', type, entityId, data, rollbackData);
  }

  /**
   * Create a database sync operation for link entities
   */
  createLinkOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation('link', type, entityId, data, rollbackData);
  }

  /**
   * Create a database sync operation for attribute entities
   */
  createAttributeOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation(
      'attribute',
      type,
      entityId,
      data,
      rollbackData
    );
  }

  /**
   * Create a database sync operation for literal entities
   */
  createLiteralOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation('literal', type, entityId, data, rollbackData);
  }

  /**
   * Create a database sync operation for folder entities
   */
  createFolderOperation(
    type: 'create' | 'update' | 'delete' | 'restore',
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return this.createOperation('folder', type, entityId, data, rollbackData);
  }

  /**
   * Execute a single database operation
   */
  executeOperation(operation: DatabaseSyncOperation): Observable<SyncResult> {
    return this.getApiCall(operation).pipe(
      timeout(30000), // 30 second timeout
      retry(2), // Retry up to 2 times
      map(
        (result) =>
          ({
            success: true,
            operation,
            result,
          } as SyncResult)
      ),
      catchError((error) =>
        of({
          success: false,
          operation,
          error,
        } as SyncResult)
      )
    );
  }

  /**
   * Execute multiple operations in batch
   */
  executeBatch(
    operations: DatabaseSyncOperation[]
  ): Observable<BatchSyncResult> {
    if (operations.length === 0) {
      return of({
        success: true,
        results: [],
        errors: [],
        totalOperations: 0,
      });
    }

    this._syncInProgress.next(true);

    const operationObservables = operations.map((op) =>
      this.executeOperation(op)
    );

    return forkJoin(operationObservables).pipe(
      map((results) => {
        const successful = results.filter((r) => r.success);
        const failed = results.filter((r) => !r.success);

        return {
          success: failed.length === 0,
          results: successful,
          errors: failed,
          totalOperations: operations.length,
        } as BatchSyncResult;
      }),
      catchError((error) =>
        of({
          success: false,
          results: [],
          errors: [{ success: false, operation: operations[0], error }],
          totalOperations: operations.length,
        } as BatchSyncResult)
      ),
      map((result) => {
        this._syncInProgress.next(false);
        return result;
      })
    );
  }

  /**
   * Execute rollback operation
   */
  executeRollback(operation: DatabaseSyncOperation): Observable<SyncResult> {
    const rollbackOp: DatabaseSyncOperation = {
      ...operation,
      id: this.generateOperationId(),
      type: this.getRollbackType(operation.type),
      data: operation.rollbackData,
      rollbackData: operation.data,
      timestamp: Date.now(),
    };

    return this.executeOperation(rollbackOp);
  }

  /**
   * Add operation to pending queue
   */
  addToPendingQueue(operation: DatabaseSyncOperation): void {
    const current = this._pendingOperations.value;
    this._pendingOperations.next([...current, operation]);
  }

  /**
   * Remove operation from pending queue
   */
  removeFromPendingQueue(operationId: string): void {
    const current = this._pendingOperations.value;
    const filtered = current.filter((op) => op.id !== operationId);
    this._pendingOperations.next(filtered);
  }

  /**
   * Clear pending queue
   */
  clearPendingQueue(): void {
    this._pendingOperations.next([]);
  }

  /**
   * Get pending operations count
   */
  getPendingCount(): number {
    return this._pendingOperations.value.length;
  }

  /**
   * Create a generic database operation
   */
  private createOperation(
    entityType: DatabaseSyncOperation['entityType'],
    type: DatabaseSyncOperation['type'],
    entityId: number,
    data?: any,
    rollbackData?: any
  ): DatabaseSyncOperation {
    return {
      id: this.generateOperationId(),
      type,
      entityType,
      entityId,
      data,
      rollbackData,
      timestamp: Date.now(),
    };
  }

  /**
   * Get the appropriate API call for an operation
   */
  private getApiCall(operation: DatabaseSyncOperation): Observable<any> {
    const { entityType, type, entityId, data } = operation;

    switch (entityType) {
      case 'class':
        return this.getClassApiCall(type, entityId, data);
      case 'enumeration':
        return this.getEnumerationApiCall(type, entityId, data);
      case 'comment':
        return this.getCommentApiCall(type, entityId, data);
      case 'link':
        return this.getLinkApiCall(type, entityId, data);
      case 'attribute':
        return this.getAttributeApiCall(type, entityId, data);
      case 'literal':
        return this.getLiteralApiCall(type, entityId, data);
      case 'folder':
        return this.getFolderApiCall(type, entityId, data);
      default:
        return throwError(
          () => new Error(`Unknown entity type: ${entityType}`)
        );
    }
  }

  /**
   * Get class API call based on operation type
   */
  private getClassApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.classApiService.createClass(data);
      case 'update':
        return this.classApiService.updateClass(data);
      case 'delete':
        return this.classApiService.deleteClass([entityId]);
      case 'restore':
        return this.classApiService.undoClassDelete(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get enumeration API call based on operation type
   */
  private getEnumerationApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.enumerationApiService.createEnumeration(data);
      case 'update':
        return this.enumerationApiService.updateEnumeration(data);
      case 'delete':
        return this.enumerationApiService.deleteEnumerations([entityId]);
      case 'restore':
        return this.enumerationApiService.undoEnumDelete(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get comment API call based on operation type
   */
  private getCommentApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.commentApiService.createComment(data);
      case 'update':
        return this.commentApiService.updateComment(data);
      case 'delete':
        return this.commentApiService.deleteComment([entityId]);
      case 'restore':
        return this.commentApiService.undoComment(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get link API call based on operation type
   */
  private getLinkApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.cardinalityApiService.createLink(data);
      case 'update':
        return this.cardinalityApiService.updateLink(data);
      case 'delete':
        return this.cardinalityApiService.deleteLink(entityId);
      case 'restore':
        return this.cardinalityApiService.undoLinkDelete(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get attribute API call based on operation type
   */
  private getAttributeApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.attributeApiService.createAttribute(data);
      case 'update':
        return this.attributeApiService.updateAttribute(data);
      case 'delete':
        return this.attributeApiService.deleteAttribute([entityId]);
      case 'restore':
        return this.attributeApiService.undoAttributeDelete(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get literal API call based on operation type
   */
  private getLiteralApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.literalApiService.createLiteral(data);
      case 'update':
        return this.literalApiService.updateLiteral(data);
      case 'delete':
        return this.literalApiService.deleteLiteral([entityId]);
      case 'restore':
        return this.literalApiService.undoLiteralDelete(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get folder API call based on operation type
   */
  private getFolderApiCall(
    type: string,
    entityId: number,
    data?: any
  ): Observable<any> {
    switch (type) {
      case 'create':
        return this.folderApiService.createFolder(data);
      case 'update':
        return this.folderApiService.updateFolder(data);
      case 'delete':
        return this.folderApiService.deleteFolders([entityId]);
      case 'restore':
        return this.folderApiService.undoFolderDelete(entityId);
      default:
        return throwError(() => new Error(`Unknown operation type: ${type}`));
    }
  }

  /**
   * Get rollback operation type
   */
  private getRollbackType(originalType: string): DatabaseSyncOperation['type'] {
    switch (originalType) {
      case 'create':
        return 'delete';
      case 'delete':
        return 'restore';
      case 'update':
        return 'update'; // Use rollback data
      case 'restore':
        return 'delete';
      default:
        return 'update';
    }
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `dbop_${++this._operationCounter}_${Date.now()}`;
  }
}
