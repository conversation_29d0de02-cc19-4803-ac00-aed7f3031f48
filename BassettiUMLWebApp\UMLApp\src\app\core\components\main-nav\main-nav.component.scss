.main-content {
  position: fixed;
  width: 100vw;
  z-index: 1000;
}

.title {
  color: white;
  margin-left: 1rem;
  font-weight: 500;
  letter-spacing: 1px;
}

.tooltip-custom {
  background-color: #333;
  color: #fff;
}

.tooltip-custom .mat-tooltip {
  font-size: 14px;
}

.main-toolbar {
  height: var(--toolbar-height);
  transition: background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
    rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
  display: flex;
  justify-content: space-between;
  &.panel-open {
    background-color: #eeebeb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .header-section {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .version-history {
      display: flex;
      align-items: center;
      margin-right: 1rem;
      cursor: pointer;
      color: black;
    }

    .saving-indicator {
      font-size: 0.9rem;
    }

    .modified-date {
      font-style: italic;
      font-size: 0.8rem;
      padding-right: 0.2rem;
    }

    .release-version {
      font-style: italic;
      font-size: 0.8rem;
      padding-right: 0.2rem;
      // color: rgb(245, 202, 202);
    }
  }

  .back-button {
    color: #333;
    margin-right: 8px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

  .toolbar-logo {
    max-height: 32px;
    cursor: pointer;
  }

  fa-icon {
    font-size: 15px;
    cursor: pointer;
    transition: color 0.3s ease;
    color: aliceblue;
  }

  fa-icon:hover {
    filter: drop-shadow(0 0 10px white);
  }

  .main-toolbar-icon {
    font-size: 15px;
    margin-left: 24px;
    color: "#fffff";
    cursor: pointer;
  }

  .undo-redo-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
    margin-right: 8px;

    .undo-redo-icon {
      font-size: 16px;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;
      color: aliceblue;
      cursor: pointer;

      &:hover:not(.disabled-icon) {
        background-color: rgba(255, 255, 255, 0.1);
        filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
        transform: scale(1.05);
      }

      &.disabled-icon {
        opacity: 0.4;
        cursor: not-allowed;
        color: #888;

        &:hover {
          background-color: transparent;
          filter: none;
          transform: none;
        }
      }
    }
  }

  .sidebar-toggle {
    width: 1.5rem;
  }

  .toolbar-button {
    transition: color 0.3s ease, background-color 0.3s ease;
  }

  .mat-icon,
  fa-icon {
    transition: color 0.3s ease;
  }

  button,
  a {
    transition: color 0.3s ease, background-color 0.3s ease;
  }
}

.menu-icon {
  margin-right: 16px;
}

.menu-flag {
  margin-right: 16px;
  vertical-align: middle;
  height: 24px;
}

.logout {
  color: rgb(216, 0, 0);
}

.rounded {
  border-radius: 50%;
  height: 23px;
  width: auto;
  transition: 300ms;
}

.rounded:hover {
  height: 28px;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .ng-fa-icon {
    margin-right: 1rem;
  }
}

.btn-dash {
  padding: 0.8rem;
  font-size: medium;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  border-radius: 10px;
  background-color: transparent;
  cursor: pointer;
}

.new-project-btn {
  font-size: 13px;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  background: transparent;
  height: calc(var(--toolbar-height) - 6px);
  border: 1px solid white !important;
  color: #fff;
}
