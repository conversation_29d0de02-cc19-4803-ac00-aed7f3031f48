import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { GraphLinksModel } from 'gojs';
import { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';
import {
  CardinalityCreate,
  CardinalityDetails,
  CardinalityPatch,
  CreatedCardinality,
  DeletedLink,
  LinkPort,
  LinkToLink,
} from 'src/app/shared/model/cardinality';
import { ClassEntityDTO } from 'src/app/shared/model/class';
import { Diagram } from 'src/app/shared/model/diagram';
import { ConfirmDialogData } from 'src/app/shared/model/dialog';
import {
  GojsLinkNode,
  GoJsLinkToLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { AccessType } from 'src/app/shared/model/project';
import {
  DefaultDestinationPort,
  DefaultSourcePort,
} from 'src/app/shared/utils/constants';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AccessService } from '../../access/access.service';
import { CardinalityService } from '../../cardinality/cardinality.service';
import { PropertyService } from '../../property/property.service';
import { SnackBarService } from '../../snackbar/snack-bar.service';
import { GojsCommonService } from '../gojsCommon/gojs-common.service';
@Injectable({
  providedIn: 'root',
})
export class GojsCardinalityService {
  private hasEditAccess: boolean = false;
  private currentDiagram!: Diagram;
  constructor(
    private cardinalityService: CardinalityService,
    private accessService: AccessService,
    private dialog: MatDialog,
    private diagramUtils: DiagramUtils,
    private propertyService: PropertyService,
    private gojsCommonService: GojsCommonService,
    private snackbarService: SnackBarService
  ) {
    this.accessService.accessTypeChanges().subscribe((response) => {
      this.hasEditAccess = response != AccessType.Viewer;
    });
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) this.currentDiagram = diagram;
    });
  }

  /**
   * Validates whether a link between two nodes in a GoJS diagram is allowed based on predefined link relations and existing links.
   * @param fromNode - The starting node of the link.
   * @param toNode - The target node of the link.
   * @param linkRelations - An array defining allowed relations between node categories.
   * @param linkData - The data object representing the link being validated.
   * @param gojsDiagram - The GoJS diagram instance containing the nodes and links.
   * @returns A boolean indicating whether the link is valid.
   */
  validateLink(
    fromNode: go.Node,
    toNode: go.Node,
    linkRelations: { from: GojsNodeCategory; to: GojsNodeCategory[] }[],
    linkData: go.Link,
    gojsDiagram: go.Diagram
  ): boolean {
    if (!fromNode?.data || !toNode?.data) return false;
    const linkDataArray = JSON.parse(gojsDiagram.model.toJson())[
      'linkDataArray'
    ];
    // To Prevent link between classes from same template class
    if (
      fromNode.data.idTemplateClass == toNode.data.idTemplateClass &&
      fromNode.data.id != toNode.data.id
    )
      return false;
    //Condition for changing the toPort of associative class and preventing to draw multiple link for same associative class
    if (
      fromNode.data.category == GojsNodeCategory.AssociativeClass &&
      linkDataArray.some(
        (link: go.ObjectData) =>
          link['idAssociativeClass'] &&
          link['idAssociativeClass'] === fromNode.data.idTemplateClass
      ) &&
      linkData == null
    ) {
      return false;
    }
    // Find the relation that matches the category of the fromNode
    const relation = linkRelations?.find(
      (link) => link.from === fromNode.data.category
    );

    // If no relation exists for the fromNode category, the link is invalid
    if (!relation) return false;

    // Check if the toNode category is allowed by the relation
    if (!relation.to.includes(toNode.data.category)) return false;
    // If the linkData is provided, check for specific cardinality rules
    if (linkData && Object.keys(linkData).length > 0) {
      if (linkData.category === GojsNodeCategory.LinkToLink) {
        return this.validateLinkToLink(toNode);
      } else {
        return true;
        // const cardinalityLinks = this.cardinalityService.getLinks();
        // const isCardinalityValid = cardinalityLinks.some(
        //   (link) =>
        //     link.idSourceTempClass === fromNode.data.idTemplateClass ||
        //     link.idDestinationTempClass === toNode.data.idTemplateClass
        // );
        // const isLinkAlreadyExists = linkDataArray.some(
        //   (link: GojsLinkNode) =>
        //     link.idFromClass === fromNode.data.id &&
        //     link.idToClass === toNode.data.id
        // );
        // return isCardinalityValid && isLinkAlreadyExists;
      }
    } else if (fromNode.data.category == GojsNodeCategory.AssociativeClass) {
      return this.validateLinkToLink(toNode);
    }
    // If all checks pass, the link is valid
    return true;
  }

  validateLinkToLink(toNode: go.Node) {
    const linkToLinks = this.cardinalityService.getLinkToLinks();
    const isLinkAlreadyExists = linkToLinks.some(
      (link) => link.idLink === toNode.data.idLink
    );
    if (isLinkAlreadyExists) {
      this.snackbarService.openSnackbar('snackBar.linkToLinkAlreadyExists');
    }
    return !isLinkAlreadyExists;
  }

  /**
   * Validates the relationship between two nodes (fromNode and toNode) based on the link's relation data.
   * @param {go.Node} fromNode - The source node of the link to be validated.
   * @param {go.Node} toNode - The target node of the link to be validated.
   * @param {go.Link} link - The link to be validated.
   * @param {go.Diagram} gojsDiagram - The diagram to which the link and nodes belong.
   * @return {*}  {boolean} - Returns `true` if the link is valid based on the relations, otherwise returns `false`.
   * @memberof GojsCardinalityService
   */
  validateGroupLink(
    fromNode: go.Node,
    toNode: go.Node,
    link: go.Link,
    gojsDiagram: go.Diagram
  ): boolean {
    if (link && link.data && link.data.linkRelations) {
      return this.validateLink(
        fromNode,
        toNode,
        link.data.linkRelations,
        link,
        gojsDiagram
      );
    } else {
      const linkRelations = [
        {
          from: GojsNodeCategory.Class,
          to: [GojsNodeCategory.Class],
        },
        {
          from: GojsNodeCategory.AssociativeClass,
          to: [GojsNodeCategory.LinkLabel],
        },
      ];
      return this.validateLink(
        fromNode,
        toNode,
        linkRelations,
        link,
        gojsDiagram
      );
    }
  }

  /**
   * Maps link data to the format required by the diagram.
   * @param {CardinalityDetails} link The link data.
   * @param {go.ObjectData} srcLink The source node data.
   * @param {go.ObjectData} destLink The destination node data.
   * @returns {GojsLinkNode} The mapped link data.
   * @memberOf CardinalityService
   */
  mapLinkData(
    link: CardinalityDetails,
    srcLink: go.ObjectData,
    destLink: go.ObjectData
  ): GojsLinkNode {
    let linkData = {
      from: srcLink['key'],
      to: destLink['key'],
      name: link.name,
      cardinalityFrom: this.cardinalityService
        .getLinkTypes()
        .get(link.idLinkType)?.from!,
      cardinalityTo: this.cardinalityService.getLinkTypes().get(link.idLinkType)
        ?.to!,
      key: link.id!,
      id: link.id!,
      idLinkType: link.idLinkType,
      idSourceTempClass: link.idSourceTempClass,
      idDestinationTempClass: link.idDestinationTempClass,
      idFromClass: srcLink['id'],
      idToClass: destLink['id'],
      category: GojsNodeCategory.Association,
      editable: this.hasEditAccess,
      fromPort: link.linkPorts[0]?.sourcePort,
      toPort: link.linkPorts[0]?.destinationPort,
      color: link.color,
      labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],
      fromComment: link.fromComment,
      toComment: link.toComment,
      segmentOffset: link.linkPorts[0]?.segmentOffset,
    };
    this.cardinalityService
      .createLinkPort({
        idLink: link.id!,
        idDiagram: this.currentDiagram.id!,
        sourcePort: link.linkPorts[0]?.sourcePort ?? 'R2',
        destinationPort: link.linkPorts[0]?.destinationPort ?? 'L2',
        segmentOffset: '0 0',
      })
      .subscribe((createdLinkPort) => {
        this.cardinalityService.addNewLinkPort(link.id!, createdLinkPort);
      });
    return linkData;
  }

  /**
   * Deletes a link based on the node's data.
   * @param {go.Node} node - The node containing the link to be deleted.
   * @param {number} nodeCount - The number of links associated with the node.
   * @param {go.Diagram} diagram - The diagram containing the node and link to be deleted.
   * @memberof GojsCardinalityService
   */
  deleteLink(node: go.Node, nodeCount: number, diagram: go.Diagram): void {
    const linkData: GojsLinkNode = node.data;
    if (nodeCount > 1) {
      this.cardinalityService.removeLink(+linkData.key!);
      this.cardinalityService.delete(+linkData.key!);
    } else {
      const dialogRef = this.dialog.open<
        DialogConfirmationComponent,
        ConfirmDialogData,
        boolean
      >(DialogConfirmationComponent, {
        width: '320px',
        data: {
          title: 'dialog.deleteTitle',
          reject: 'dialog.no',
          confirm: 'dialog.yes',
        },
      });
      dialogRef.afterClosed().subscribe((isConfirm) => {
        if (isConfirm) {
          this.removeLinkFromAllDiagram(linkData);
        } else {
          this.removeLinkFromCurrentDiagram(linkData, diagram, false);
        }
      });
    }
  }

  /**
   * Handles linking operations for the diagram.
   * @private
   * @param {go.ChangedEvent} event - The change event.
   * @param {string} action - The action type ('undo' or 'redo').
   * @memberof DiagramEditorComponent
   */
  handleUndoRedoLinking(
    event: go.ChangedEvent,
    action: string,
    isDelete: boolean
  ): void {
    if (event.object) {
      event.object['changes']['iterator'].each((obj: go.ObjectData) => {
        if (obj['propertyName'] == 'linkDataArray') {
          const valueParam = isDelete ? 'oldValue' : 'newValue';
          if (action === 'undo') {
            if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {
              this.cardinalityService.removeLinkToLink(obj[valueParam].key);
              this.cardinalityService.deleteLinkToLink(obj[valueParam].key);
            } else {
              this.cardinalityService.removeLink(obj[valueParam].key);
              this.cardinalityService.delete(obj[valueParam].key);
            }
          } else {
            if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {
              this.cardinalityService.addLinkToLink(obj[valueParam]);
              this.cardinalityService.undoLinkToLinkDeletion(
                obj[valueParam].key
              );
            } else {
              this.cardinalityService.addLink(obj[valueParam]);
              this.cardinalityService.undoLinkDeletion(obj[valueParam].key);
            }
          }
        }
      });
    }
  }

  /**
   * Updates the link data after editing the text
   * @param {GojsLinkNode} linkData - The link data containing the cardinality values and other link properties.
   * @memberof GojsCardinalityService
   */
  updateLinkOnTextEdited(linkData: GojsLinkNode): void {
    let idLinkType;
    const linkTypes = this.cardinalityService.getLinkTypes();
    for (let [key, value] of linkTypes.entries()) {
      if (
        value.from == linkData.cardinalityFrom &&
        value.to == linkData.cardinalityTo
      ) {
        idLinkType = key;
        break;
      }
    }
    if (idLinkType) {
      this.updateLink({
        ...linkData,
        idLinkType: idLinkType,
        id: linkData.key,
      });
    }
  }

  /**
   * Updates the link data and modifies the link in the cardinality service.
   * @private
   * @param {go.ObjectData} linkData  - The data of the link to be updated, which includes properties like `id`, `name`, and `idLinkType`.
   * @memberof GojsCardinalityService
   */
  private updateLink(linkData: go.ObjectData) {
    this.cardinalityService
      .updateLink({
        id: linkData['id'],
        name: linkData['name'],
        idLinkType: linkData['idLinkType'],
        color: linkData['color'],
        fromComment: linkData['fromComment'] ?? null,
        toComment: linkData['toComment'] ?? null,
        idDestinationTempClass: linkData['idDestinationTempClass'],
        idSourceTempClass: linkData['idSourceTempClass'],
        segmentOffset: linkData['segmentOffset'],
        linkPort: {
          idDiagram: this.currentDiagram.id!,
          destinationPort: linkData['toPort']!,
          sourcePort: linkData['fromPort']!,
          idLink: linkData['id']!,
          segmentOffset: linkData['segmentOffset'],
        },
      })
      .subscribe((link) => {
        const modifiedLink = this.cardinalityService.getLinkById(link.id!);
        modifiedLink.name = link.name;
        modifiedLink.idLinkType = link.idLinkType;
        modifiedLink.fromComment = link.fromComment;
        modifiedLink.toComment = link.toComment;
        modifiedLink.linkPorts = modifiedLink.linkPorts.map((port) => {
          if (port.idDiagram === this.currentDiagram.id) {
            port.sourcePort = link.linkPort.sourcePort;
            port.destinationPort = link.linkPort.destinationPort;
            port.segmentOffset = link.linkPort.segmentOffset;
          }
          return port;
        });
        this.cardinalityService.modifyLink(modifiedLink);
        this.propertyService.setPropertyData(linkData as GojsLinkNode);
      });
  }

  /**
   * Handles the case when a deleted link is found.
   * @param {DeletedLink} deletedLink - The deleted link object.
   * @param {GojsLinkNode} link - The current link object.
   * @param {CardinalityDetails} newLink - The new link object.
   * @param {go.ObjectData} fromNode - The source node.
   * @param {go.ObjectData} toNode - The destination node.
   */
  handleDeletedLink(
    deletedLink: DeletedLink,
    link: GojsLinkNode,
    newLink: CardinalityCreate,
    fromNode: go.ObjectData,
    toNode: go.ObjectData,
    diagram: go.Diagram
  ): void {
    this.cardinalityService.removeLinkHistory(deletedLink.id!);
    const createdCardinality: CreatedCardinality = {
      ...newLink,
      id: deletedLink.idLink!,
      linkPort: {
        sourcePort: newLink.sourcePort,
        destinationPort: newLink.destinationPort,
        idDiagram: deletedLink.idDiagram,
        idLink: deletedLink.idLink,
        segmentOffset: '0 0',
      },
    };
    this.cardinalityService
      .createLinkPort(createdCardinality.linkPort)
      .subscribe((linkPortResult) => {
        this.cardinalityService.addNewLinkPort(
          createdCardinality.id!,
          linkPortResult
        );
        this.updateLinkProperties(
          link,
          createdCardinality,
          fromNode,
          toNode,
          diagram
        );
      });

    this.diagramUtils.removeDeletedLink(deletedLink.id!);
  }

  /**
   * Updates the properties of a link in the diagram, including its name, key, category, link type,
   * @private
   * @param {GojsLinkNode} link - The existing link node to be updated.
   * @param {CreatedCardinality} linkData - The updated cardinality data for the link.
   * @param {go.ObjectData} fromNode - The source node for the link.
   * @param {go.ObjectData} toNode - The target node for the link.
   * @param {go.Diagram} diagram - The GoJS diagram where the link resides.
   * @memberof GojsCardinalityService
   */
  private updateLinkProperties(
    link: GojsLinkNode,
    linkData: CreatedCardinality,
    fromNode: go.ObjectData,
    toNode: go.ObjectData,
    diagram: go.Diagram
  ): void {
    link.category = GojsNodeCategory.Association;
    link.key = linkData.id!;
    link.idLinkType = linkData.idLinkType;
    link.idSourceTempClass = linkData.idSourceTempClass;
    link.idDestinationTempClass = linkData.idDestinationTempClass;
    link.idFromClass = fromNode['id'];
    link.idToClass = toNode['id'];
    link.name = linkData.name;
    link.cardinalityFrom = this.cardinalityService.getLinkTypes().get(1)?.from!;
    link.cardinalityTo = this.cardinalityService.getLinkTypes().get(1)?.to!;
    link.fromPort = linkData.linkPort.sourcePort;
    link.toPort = linkData.linkPort.destinationPort;
    link.editable = this.hasEditAccess;
    link.color = linkData.color;
    link.labelKeys = [`${linkData.id}_${GojsNodeCategory.LinkLabel}`];
    link.fromComment = linkData.fromComment;
    link.toComment = linkData.toComment;
    link.segmentOffset = linkData.linkPort.segmentOffset;
    diagram.model.updateTargetBindings(link);
  }
  private updateLinkToLinkProp(
    link: GoJsLinkToLinkNode,
    linkData: LinkToLink,
    diagram: go.Diagram
  ) {
    link.category = GojsNodeCategory.LinkToLink;
    link.key = linkData.id!;
    link.idLink = linkData.idLink;
    link.idAssociativeClass = linkData.idAssociativeClass;
    link.fromPort = linkData.port;
    link.editable = this.hasEditAccess;
    diagram.model.updateTargetBindings(link);
  }

  /**
   * Creates a new link based on the provided cardinality data, adds the link to the system,
   * @param {GojsLinkNode} link - The existing link node to be updated after creating the new link.
   * @param {CardinalityCreate} newLink - The data required to create the new link, including cardinality information.
   * @param {go.ObjectData} fromNode - The source node for the new link.
   * @param {go.ObjectData} toNode - The target node for the new link.
   * @param {go.Diagram} diagram - The GoJS diagram where the link should be updated.
   * @memberof GojsCardinalityService
   */
  createNewLinkAndUpdate(
    link: GojsLinkNode,
    newLink: CardinalityCreate,
    fromNode: go.ObjectData,
    toNode: go.ObjectData,
    diagram: go.Diagram
  ): void {
    this.cardinalityService.createNewLink(newLink).subscribe((createdLink) => {
      this.cardinalityService.addLink({
        ...createdLink,
        linkPorts: [createdLink.linkPort],
        idFromClass: link.idFromClass,
        idToClass: link.idToClass,
        segmentOffset: link.segmentOffset,
      });
      this.propertyService.setPropertyData(null);
      this.updateLinkProperties(link, createdLink, fromNode, toNode, diagram);
      const linkLabelNode = {
        key: `${createdLink.id}_${GojsNodeCategory.LinkLabel}`,
        category: GojsNodeCategory.LinkLabel,
        idLink: createdLink.id,
        editable: this.hasEditAccess,
      };
      diagram.model.addNodeData(linkLabelNode);
      diagram.updateAllRelationshipsFromData();
    });
  }
  createNewLinkToLinkAndUpdate(
    link: GoJsLinkToLinkNode,
    newLink: LinkToLink,
    diagram: go.Diagram
  ): void {
    this.cardinalityService
      .createLinkToLink(newLink)
      .subscribe((createdLink) => {
        this.cardinalityService.addLinkToLink(createdLink);
        this.updateLinkToLinkProp(link, createdLink, diagram);
      });
  }

  updatePortForLinkToLink(linkObj: GoJsLinkToLinkNode) {
    // update port for link to link
    this.cardinalityService.updateLinkToLink({
      id: linkObj.key,
      port: linkObj.fromPort,
      idAssociativeClass: linkObj.idAssociativeClass,
      idLink: +linkObj.to.split('_')[0],
    });
  }
  /**
   * Handles the redrawing of a link by updating its link port properties in the diagram.
   * @param {GojsLinkNode} linkData - The link data that contains the new port information.
   * @memberof GojsCardinalityService
   */
  handleReDrawnLink(linkData: GojsLinkNode, diagram: go.Diagram): void {
    const linkDetails = this.cardinalityService.getLinkById(+linkData.key!);
    const fromNode = this.diagramUtils.getObjectDataByKey(
      diagram,
      linkData.from
    );
    const toNode = this.diagramUtils.getObjectDataByKey(diagram, linkData.to);
    if (linkDetails && fromNode && toNode) {
      const currentDiagramLinkPort = linkDetails.linkPorts.find(
        (port) => port.idDiagram === this.currentDiagram.id
      );
      if (currentDiagramLinkPort)
        this.updateLinkPort(
          {
            ...linkDetails,
            idDestinationTempClass: toNode['idTemplateClass'],
            idSourceTempClass: fromNode['idTemplateClass'],
          },
          currentDiagramLinkPort,
          linkData.fromPort!,
          linkData.toPort!
        );
    }
  }

  /**
   * Updates the link port properties in the diagram for a given link.
   * @private
   * @param {CardinalityDetails} linkDetails  - The details of the link being updated, including the link ports.
   * @param {LinkPort} currentDiagramLinkPort - The link port that corresponds to the current diagram.
   * @param {string} fromPort - The source port for the link.
   * @param {string} toPort - The destination port for the link.
   * @memberof GojsCardinalityService
   */
  private updateLinkPort(
    linkDetails: CardinalityDetails,
    currentDiagramLinkPort: LinkPort,
    fromPort: string,
    toPort: string
  ) {
    const linkToUpdate: CardinalityPatch = {
      id: linkDetails.id,
      name: linkDetails.name,
      idLinkType: linkDetails.idLinkType,
      color: linkDetails.color,
      fromComment: linkDetails.fromComment,
      toComment: linkDetails.toComment,
      idDestinationTempClass: linkDetails.idDestinationTempClass,
      idSourceTempClass: linkDetails.idSourceTempClass,
      segmentOffset: currentDiagramLinkPort?.segmentOffset ?? '0 0',
      linkPort: {
        ...currentDiagramLinkPort,
        sourcePort: fromPort,
        destinationPort: toPort,
      },
    };
    const otherDiagramLinkPorts = linkDetails.linkPorts.filter(
      (port) => port.idDiagram !== this.currentDiagram.id
    );
    this.cardinalityService.updateLink(linkToUpdate).subscribe((response) => {
      this.cardinalityService.modifyLink({
        ...linkDetails,
        linkPorts: [...otherDiagramLinkPorts, response.linkPort],
      });
    });
  }

  /**
   * Formats and filters the link data into a structure suitable for GoJS link nodes.
   * @param {ClassEntityDTO[]} classes - An array of class entities that provide the source and destination class details.
   * @param {DeletedLink[]} linkHistories - An array of deleted link histories to filter out the deleted links.
   * @return {GojsLinkNode[]} - An array of formatted GoJS link nodes based on the provided classes and links.
   * @memberof GojsCardinalityService
   */
  formatLinkData(
    classes: ClassEntityDTO[],
    linkHistories: DeletedLink[],
    diagramId: number
  ): GojsLinkNode[] {
    const linkTypes = this.cardinalityService.getLinkTypes();
    const associationLinkData: GojsLinkNode[] = [];
    if (classes.length > 0) {
      this.cardinalityService.getLinks().forEach((link) => {
        const fromClass = classes.find(
          (cls) =>
            (cls.id == link.idFromClass ||
              cls.idTemplateClass == link.idSourceTempClass) &&
            cls.isAssociative == false
        );
        const toClass = classes.find(
          (cls) =>
            (cls.id == link?.idToClass ||
              cls.idTemplateClass == link.idDestinationTempClass) &&
            cls.isAssociative == false
        );
        const linkType = linkTypes.get(link.idLinkType);
        const isDeletedLink =
          linkHistories.find(
            (deletedLink) =>
              deletedLink.idSourceClass == fromClass?.id &&
              deletedLink.idDestinationClass == toClass?.id &&
              deletedLink.idLink === link.id
          ) || {};

        if (fromClass && toClass && Object.keys(isDeletedLink).length == 0) {
          const currentDiagramLinkPort = this.getOrCreateLinkPort(
            link,
            diagramId
          );
          const linkNodeData: GojsLinkNode = {
            from: `${fromClass?.key?.toString()!}`,
            to: `${toClass?.key?.toString()!}`,
            name: link.name,
            cardinalityFrom: linkType?.from!,
            cardinalityTo: linkType?.to!,
            key: link.id!,
            id: link.id!,
            idLinkType: link.idLinkType,
            idSourceTempClass: link.idSourceTempClass,
            idDestinationTempClass: link.idDestinationTempClass,
            idFromClass: fromClass?.id!,
            idToClass: toClass?.id!,
            category: GojsNodeCategory.Association,
            editable: this.hasEditAccess,
            fromPort: currentDiagramLinkPort?.sourcePort,
            toPort: currentDiagramLinkPort?.destinationPort,
            color: link.color,
            labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],
            fromComment: link.fromComment,
            toComment: link.toComment,
            segmentOffset:
              link.linkPorts.find((port) => port.idDiagram == diagramId)
                ?.segmentOffset ?? '0 0', // Offset the label slightly from the midpoint
          };
          associationLinkData.push(linkNodeData);
        }
      });
    }
    return associationLinkData;
  }

  async generateLinkToLinkNodes(
    associationLinks: GojsLinkNode[],
    classes: ClassEntityDTO[]
  ): Promise<GoJsLinkToLinkNode[]> {
    const processedIds = new Set<number>();
    const linkToLinks = this.cardinalityService.getLinkToLinks();
    const linkToLinkNodes: GoJsLinkToLinkNode[] = [];
    for (const classNode of classes) {
      if (!classNode.isAssociative) continue;
      const linkToLink = linkToLinks.find(
        (link) => link.idAssociativeClass === classNode.idTemplateClass
      );
      if (linkToLink) {
        const linkNode = associationLinks.find(
          (link) => link.id === linkToLink.idLink
        );
        if (!linkNode) continue;
        if (processedIds.has(linkToLink.idLink)) continue;
        processedIds.add(linkToLink.idLink);
        linkToLinkNodes.push(
          this.formatLinkToLinkData(linkToLink, classNode.key!.toString())
        );
      }
    }
    return linkToLinkNodes;
  }
  /**
   * Retrieves an existing link port for the current diagram or creates a new one if it doesn't exist.
   * @private
   * @param {CardinalityDetails} link - The link whose port is being retrieved or created.
   * @return {LinkPort} The existing or newly created link port associated with the current diagram.
   * @memberof GojsCardinalityService
   */
  private getOrCreateLinkPort(
    link: CardinalityDetails,
    diagramId: number
  ): LinkPort {
    let currentDiagramLinkPort = link.linkPorts?.find(
      (port) => port.idDiagram === diagramId
    );
    const linkPort: LinkPort = link.linkPorts
      ? {
          idDiagram: diagramId,
          idLink: link.id!,
          sourcePort: link.linkPorts[0]?.sourcePort
            ? link.linkPorts[0]?.sourcePort
            : DefaultSourcePort,
          destinationPort: link.linkPorts[0]?.destinationPort
            ? link.linkPorts[0]?.destinationPort
            : DefaultDestinationPort,
          segmentOffset: link.linkPorts[0]?.segmentOffset ?? '0 0',
        }
      : {
          idDiagram: diagramId,
          idLink: link.id!,
          sourcePort:
            (link as unknown as GojsLinkNode).fromPort ?? DefaultSourcePort,
          destinationPort:
            (link as unknown as GojsLinkNode).toPort ?? DefaultDestinationPort,
          segmentOffset: '0 0',
        };
    if (currentDiagramLinkPort) {
      return currentDiagramLinkPort;
    } else {
      const ports = link.linkPorts ? link.linkPorts : [];
      this.cardinalityService
        .createLinkPort(linkPort)
        .subscribe((createdLinkPort) => {
          currentDiagramLinkPort = createdLinkPort;
          this.cardinalityService.modifyLink({
            ...link,
            linkPorts: [...ports, currentDiagramLinkPort],
          });
        });
      return currentDiagramLinkPort ?? linkPort;
    }
  }

  /**
   * Creates links for a specific class in the GoJS diagram based on its palette ID.
   * @param {number} idPalette  - The ID of the class in the palette to create links for.
   * @param {go.Diagram} gojsDiagram - The GoJS diagram where the links should be created.
   * @memberof GojsCardinalityService
   */
  createLinksForPaletteClass(idPalette: number, gojsDiagram: go.Diagram) {
    if (
      gojsDiagram.model.nodeDataArray.filter(
        (node) => node['idTemplateClass'] == idPalette
      ).length == 1 &&
      this.cardinalityService
        .getLinks()
        .find(
          (link) =>
            link.idSourceTempClass == idPalette ||
            link.idDestinationTempClass == idPalette
        )
    ) {
      const links = this.getRelevantLinks(idPalette);
      links.forEach((link) => {
        this.createLinkForClass(link, gojsDiagram);
      });
    }
    //For add the link to link node in diagram during drop library
    if (
      gojsDiagram.model.nodeDataArray.filter(
        (node) => node['idTemplateClass'] == idPalette
      ).length == 1 &&
      this.cardinalityService
        .getLinkToLinks()
        .find((link) => link.idAssociativeClass == idPalette)
    ) {
      const linkToLink = this.cardinalityService
        .getLinkToLinks()
        .find((link) => link.idAssociativeClass == idPalette);
      const isPresentLinkToLink = (
        gojsDiagram.model as go.GraphLinksModel
      ).linkDataArray.some((link) => link['idLink'] == linkToLink?.idLink);
      if (linkToLink && !isPresentLinkToLink)
        this.formatLinkToLinkObj(linkToLink, gojsDiagram);
    }
  }

  /**
   * Retrieves the relevant links associated with a specific class from the palette
   * @param {number} idPalette - The ID of the class in the palette to create links for.
   * @return   {CardinalityDetails[]} - An array of relevant links associated with the class.
   * @memberof GojsCardinalityService
   */
  getRelevantLinks(idPalette: number): CardinalityDetails[] {
    return this.cardinalityService
      .getLinks()
      .filter(
        (linkData) =>
          linkData.idDestinationTempClass === idPalette ||
          linkData.idSourceTempClass === idPalette
      );
  }

  /**
   * Creates a link between two nodes in the GoJS diagram based on the provided link data.
   * @private
   * @param {CardinalityDetails} link - The link data containing source and destination class IDs.
   * @param {go.Diagram} goJsDiagram - The GoJS diagram where the link should be created.
   * @memberof GojsCardinalityService
   */
  private createLinkForClass(
    link: CardinalityDetails,
    goJsDiagram: go.Diagram
  ) {
    const srcLink = this.diagramUtils.findNodeByIdTemplateClass(
      goJsDiagram,
      link.idSourceTempClass
    );
    const destLink = this.diagramUtils.findNodeByIdTemplateClass(
      goJsDiagram,
      link.idDestinationTempClass
    );
    if (srcLink && destLink) {
      const linkData = this.mapLinkData(link, srcLink, destLink);
      goJsDiagram.model.addNodeData({
        key: `${linkData.id}_${GojsNodeCategory.LinkLabel}`,
        category: GojsNodeCategory.LinkLabel,
        idLink: linkData.id,
        editable: linkData.editable,
      });
      const linkToLinkNode = this.cardinalityService
        .getLinkToLinks()
        .find((link) => link.idLink == linkData.id);
      if (linkToLinkNode) this.formatLinkToLinkObj(linkToLinkNode, goJsDiagram);
      goJsDiagram.updateAllRelationshipsFromData();
      (goJsDiagram.model as go.GraphLinksModel).addLinkData(linkData);
    }
  }
  private formatLinkToLinkObj(link: LinkToLink, goJsDiagram: go.Diagram) {
    const srcLink = this.diagramUtils.findNodeByIdTemplateClass(
      goJsDiagram,
      link.idAssociativeClass
    );
    if (srcLink) {
      const linkData: GoJsLinkToLinkNode = {
        from: srcLink['key'],
        key: link.id!,
        idLink: link.idLink,
        to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,
        idAssociativeClass: link.idAssociativeClass,
        category: GojsNodeCategory.LinkToLink,
        editable: this.hasEditAccess,
        fromPort: link.port,
      };
      (goJsDiagram.model as go.GraphLinksModel).addLinkData(linkData);
    }
  }

  /**
   * Removes all links related to the specified palette ID.
   * @param {number} idPalette - The ID of the palette whose related links should be removed.
   * @memberof GojsCardinalityService
   */
  removeRelatedLinks(idPalette: number) {
    const relatedLinks = this.cardinalityService
      .getLinks()
      .filter(
        (link) =>
          link.idDestinationTempClass === idPalette ||
          link.idSourceTempClass === idPalette
      );
    this.cardinalityService.removeLinks(relatedLinks);
  }

  /**
   * Removes a link from all diagrams associated with the provided link data.
   * @param {GojsLinkNode} linkData - The link data to be removed.
   * @memberof GojsCardinalityService
   */
  removeLinkFromAllDiagram(linkData: GojsLinkNode): void {
    this.cardinalityService.removeLink(+linkData.key!);
    this.cardinalityService.delete(+linkData.key!);
    this.diagramUtils.removeDeletedLink(+linkData.key!);
  }

  /**
   * Removes a link from the current diagram, with an option for temporary or permanent removal.
   * @param {GojsLinkNode} linkData - The link data to be removed.
   * @param {go.Diagram} diagram  - The GoJS diagram from which the link is to be removed.
   * @param {boolean} isPermanent - Flag to indicate if the removal is permanent.
   * @memberof GojsCardinalityService
   */
  removeLinkFromCurrentDiagram(
    linkData: GojsLinkNode,
    diagram: go.Diagram,
    isPermanent: boolean
  ): void {
    if (!isPermanent)
      this.cardinalityService.createDeletedLinkHistory({
        idDiagram: this.currentDiagram.id!,
        idSourceClass: linkData.idFromClass,
        idDestinationClass: linkData.idToClass,
        idLink: +linkData.key!,
      });
    const linkDetails = this.cardinalityService.getLinkById(+linkData.key!);
    const currentDiagramLinkPort = linkDetails.linkPorts.find(
      (port) => port.idDiagram === this.currentDiagram.id
    );
    if (currentDiagramLinkPort) {
      if (!isPermanent)
        this.cardinalityService.deleteLinkPort(currentDiagramLinkPort.id!);
      this.cardinalityService.removeExistingLinkPort(
        linkDetails.id!,
        currentDiagramLinkPort.id!
      );
    }
    (diagram.model as GraphLinksModel).removeLinkData(linkData);
  }

  updateLinkFromProperty(updatedNode: GojsLinkNode, diagram: go.Diagram): void {
    let link: go.ObjectData | null = null;
    (diagram.model as GraphLinksModel).linkDataArray.forEach((linkData) => {
      if (
        linkData['key'] === updatedNode.key &&
        linkData['category'] === updatedNode.category
      ) {
        link = linkData;
      }
    });
    if (!link) return;

    const linkData = link;
    this.gojsCommonService.setDataProperties(diagram.model, linkData, {
      name: updatedNode.name,
      fromComment: updatedNode.fromComment,
      toComment: updatedNode.toComment,
      color: updatedNode.color,
    });
    const modifiedLink = this.cardinalityService.getLinkById(updatedNode.id!);
    modifiedLink.name = updatedNode.name;
    modifiedLink.fromComment = updatedNode.fromComment;
    modifiedLink.toComment = updatedNode.toComment;
    modifiedLink.color = updatedNode.color;
    this.cardinalityService.modifyLink(modifiedLink);
  }

  formatLinkToLinkData(link: LinkToLink, fromClassKey: string) {
    const linkToLinkNode: GoJsLinkToLinkNode = {
      from: fromClassKey,
      to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,
      key: link.id!,
      idLink: link.idLink,
      idAssociativeClass: link.idAssociativeClass,
      category: GojsNodeCategory.LinkToLink,
      editable: this.hasEditAccess,
      fromPort: link.port,
    };
    return linkToLinkNode;
  }
}
