{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, effect } from '@angular/core';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { AboutModalComponent } from '../about-modal/about-modal.component';\nexport let MainNavComponent = class MainNavComponent {\n  constructor(router, appService, accessService, diagramService, diagramUtils, userService, languageService, projectService, loaderService, navbarService, dialog, cdr, versionHistoryService, searchBarService, undoRedoService) {\n    this.router = router;\n    this.appService = appService;\n    this.accessService = accessService;\n    this.diagramService = diagramService;\n    this.diagramUtils = diagramUtils;\n    this.userService = userService;\n    this.languageService = languageService;\n    this.projectService = projectService;\n    this.loaderService = loaderService;\n    this.navbarService = navbarService;\n    this.dialog = dialog;\n    this.cdr = cdr;\n    this.versionHistoryService = versionHistoryService;\n    this.searchBarService = searchBarService;\n    this.undoRedoService = undoRedoService;\n    this.languages = [];\n    this.profilePicture = '';\n    this.title = 'UML Web Application';\n    this.currentDiagramName = '';\n    this.userName = '';\n    this.hasDeleteAccess = false;\n    this.isGridLayout = false;\n    this.isLoading = false;\n    this.loaderType = 'default';\n    this.noOfDiagram = 0;\n    this.destroy$ = new Subject();\n    this.lastModifiedDate = new Date();\n    this.showVersionHistory = false;\n    this.selectedVersion = null;\n    this.undoRedoState = {\n      canUndo: false,\n      canRedo: false,\n      undoText: '',\n      redoText: '',\n      isProcessing: false\n    };\n    effect(() => {\n      this.lastModifiedDate = this.appService.lastModifiedDate();\n      this.selectedVersion = this.versionHistoryService.selectedVersion();\n      this.showVersionHistory = this.navbarService.showVersionHistory();\n    });\n  }\n  ngOnInit() {\n    this.languageService.languages.subscribe(langs => {\n      this.languages = langs;\n    });\n    this.appService.getThemeClass();\n    // Subscribe to user changes\n    this.userService.userChanges().subscribe(user => {\n      if (user && Object.keys(user).length > 0) {\n        // Set profile picture if available\n        if (user.profilePicture) {\n          this.profilePicture = user.profilePicture;\n        }\n        // Set user name\n        if (user.name) {\n          this.userName = user.name.split(' ')[0];\n        }\n      }\n    });\n    // Initialize user data\n    this.userService.initializeUser();\n    this.accessService.accessTypeChanges().subscribe(accessType => {\n      if (accessType == AccessType.Viewer) {\n        this.hasDeleteAccess = false;\n      } else {\n        this.hasDeleteAccess = true;\n      }\n    });\n    this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) {\n        this.currentProject = project;\n        if (project.diagrams.length > 0) {\n          this.currentDiagramName = project.diagrams[0].name;\n          this.title = `${project.name} - ${this.currentDiagramName}`;\n        } else this.title = `${project.name}`;\n      }\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      if (diagrams) this.noOfDiagram = diagrams.length;\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) {\n        this.currentDiagramName = diagram.name;\n        this.title = `${this.currentProject?.name} - ${this.currentDiagramName}`;\n      } else {\n        this.title = `${this.currentProject?.name}`;\n      }\n    });\n    // Subscribe to loading state\n    this.loaderService.isLoading$.pipe(takeUntil(this.destroy$)).subscribe(status => {\n      // Use setTimeout to break change detection cycle\n      setTimeout(() => {\n        this.isLoading = status;\n        // Manually trigger change detection\n        this.cdr.detectChanges();\n      });\n    });\n    // Subscribe to loader type\n    this.loaderService.loaderType$.pipe(takeUntil(this.destroy$)).subscribe(type => {\n      // Use setTimeout to break change detection cycle\n      setTimeout(() => {\n        this.loaderType = type;\n        // Manually trigger change detection\n        this.cdr.detectChanges();\n      });\n    });\n  }\n  ngAfterViewInit() {\n    this.title = 'UML Web Application';\n    this.cdr.detectChanges();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  goBack() {\n    if (this.router.url.includes('/editor')) {\n      this.title = 'UML Web Application';\n      // Reset search term when navigating back to dashboard\n      this.searchBarService.resetSearch();\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  isEditorRoute() {\n    return this.router.url.includes('/editor');\n  }\n  openAboutModal() {\n    this.dialog.open(AboutModalComponent, {\n      width: '42%',\n      maxWidth: '70vw',\n      panelClass: 'about-modal'\n    });\n  }\n  /**\n   * Performs the logout operation.\n   * @memberof MainNavComponent\n   */\n  onLogout() {\n    if (this.router.url.includes('editor')) {\n      this.projectService.handleProjectUnlock(this.currentProject.id);\n    }\n    this.userService.logout();\n  }\n  onImageError() {\n    // Set to empty string to show the default user icon\n    this.profilePicture = '';\n  }\n  onDiagramClear() {\n    this.diagramService.triggerDelete();\n  }\n  downloadCurrent() {\n    this.diagramService.triggerCurrentDiagramDownload();\n  }\n  downloadAllDiagrams(isForOnlyImage) {\n    this.diagramService.triggerAllDiagramDownload(isForOnlyImage);\n  }\n  changeLanguage(langCode) {\n    this.languageService.changeLanguage(langCode);\n  }\n  shouldShowTooltip(name, length) {\n    return name.length > length;\n  }\n  createNewProject() {\n    this.projectService.openProjectDialog();\n  }\n  openVersionHistory() {\n    this.navbarService.setShowVersionHistory(true);\n  }\n  closeVersionHistory() {\n    // If you're using a service with signals as mentioned earlier\n    this.navbarService.setShowVersionHistory(false);\n  }\n};\nMainNavComponent = __decorate([Component({\n  selector: 'app-main-nav',\n  templateUrl: './main-nav.component.html',\n  styleUrls: ['./main-nav.component.scss']\n})], MainNavComponent);", "map": {"version": 3, "names": ["Component", "effect", "Subject", "takeUntil", "AccessType", "AboutModalComponent", "MainNavComponent", "constructor", "router", "appService", "accessService", "diagramService", "diagramUtils", "userService", "languageService", "projectService", "loaderService", "navbarService", "dialog", "cdr", "versionHistoryService", "searchBarService", "undoRedoService", "languages", "profilePicture", "title", "currentDiagramName", "userName", "hasDeleteAccess", "isGridLayout", "isLoading", "loaderType", "noOfDiagram", "destroy$", "lastModifiedDate", "Date", "showVersionHistory", "selectedVersion", "undoRedoState", "canUndo", "canRedo", "undoText", "redoText", "isProcessing", "ngOnInit", "subscribe", "langs", "getThemeClass", "userChanges", "user", "Object", "keys", "length", "name", "split", "initializeUser", "accessTypeChanges", "accessType", "Viewer", "currentProjectChanges", "project", "currentProject", "diagrams", "currentProjectDiagramsChanges", "activeDiagramChanges", "diagram", "isLoading$", "pipe", "status", "setTimeout", "detectChanges", "loaderType$", "type", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "goBack", "url", "includes", "resetSearch", "navigate", "isEditorRoute", "openAboutModal", "open", "width", "max<PERSON><PERSON><PERSON>", "panelClass", "onLogout", "handleProjectUnlock", "id", "logout", "onImageError", "onDiagramClear", "triggerDelete", "downloadCurrent", "triggerCurrentDiagramDownload", "downloadAllDiagrams", "isForOnlyImage", "triggerAllDiagramDownload", "changeLanguage", "langCode", "shouldShowTooltip", "createNewProject", "openProjectDialog", "openVersionHistory", "setShowVersionHistory", "closeVersionHistory", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\main-nav\\main-nav.component.ts"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  effect,\r\n  OnD<PERSON>roy,\r\n  OnInit,\r\n} from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { Lang } from 'src/app/shared/model/common';\r\nimport { AccessType, ProjectDetails } from 'src/app/shared/model/project';\r\nimport { VersionHistory } from 'src/app/shared/model/version-history';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../../services/access/access.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { DiagramService } from '../../services/diagram/diagram.service';\r\nimport { LanguageService } from '../../services/language/language.service';\r\nimport {\r\n  LoaderService,\r\n  LoaderType,\r\n} from '../../services/loader/loader.service';\r\nimport { NavbarService } from '../../services/navbar/navbar.service';\r\nimport { ProjectService } from '../../services/project/project.service';\r\nimport { SearchBarService } from '../../services/search-bar/search-bar.service';\r\nimport { UndoRedoState } from '../../services/undo-redo/undo-redo.service';\r\nimport { UserService } from '../../services/user/user.service';\r\nimport { VersionHistoryService } from '../../services/versionHistory/version-history.service';\r\nimport { AboutModalComponent } from '../about-modal/about-modal.component';\r\n\r\n@Component({\r\n  selector: 'app-main-nav',\r\n  templateUrl: './main-nav.component.html',\r\n  styleUrls: ['./main-nav.component.scss'],\r\n})\r\nexport class MainNavComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  public languages: Lang[] = [];\r\n  public profilePicture: string = '';\r\n  public title: string = 'UML Web Application';\r\n  currentDiagramName: string = '';\r\n  currentProject!: ProjectDetails;\r\n  public userName: string = '';\r\n  hasDeleteAccess: boolean = false;\r\n  isGridLayout: boolean = false;\r\n  isLoading = false;\r\n  loaderType: LoaderType = 'default';\r\n  noOfDiagram: number = 0;\r\n  private destroy$ = new Subject<void>();\r\n  lastModifiedDate: Date | undefined = new Date();\r\n  showVersionHistory: boolean = false;\r\n  selectedVersion: VersionHistory | null = null;\r\n  undoRedoState: UndoRedoState = {\r\n    canUndo: false,\r\n    canRedo: false,\r\n    undoText: '',\r\n    redoText: '',\r\n    isProcessing: false,\r\n  };\r\n  constructor(\r\n    public router: Router,\r\n    private appService: AppService,\r\n    private accessService: AccessService,\r\n    private diagramService: DiagramService,\r\n    private diagramUtils: DiagramUtils,\r\n    private userService: UserService,\r\n    private languageService: LanguageService,\r\n    private projectService: ProjectService,\r\n    public loaderService: LoaderService,\r\n    private navbarService: NavbarService,\r\n    private dialog: MatDialog,\r\n    private cdr: ChangeDetectorRef,\r\n    private versionHistoryService: VersionHistoryService,\r\n    private searchBarService: SearchBarService,\r\n    private undoRedoService: UndoRedoService\r\n  ) {\r\n    effect(() => {\r\n      this.lastModifiedDate = this.appService.lastModifiedDate();\r\n      this.selectedVersion = this.versionHistoryService.selectedVersion();\r\n      this.showVersionHistory = this.navbarService.showVersionHistory();\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.languageService.languages.subscribe((langs: Lang[]) => {\r\n      this.languages = langs;\r\n    });\r\n    this.appService.getThemeClass();\r\n\r\n    // Subscribe to user changes\r\n    this.userService.userChanges().subscribe((user) => {\r\n      if (user && Object.keys(user).length > 0) {\r\n        // Set profile picture if available\r\n        if (user.profilePicture) {\r\n          this.profilePicture = user.profilePicture;\r\n        }\r\n        // Set user name\r\n        if (user.name) {\r\n          this.userName = user.name.split(' ')[0];\r\n        }\r\n      }\r\n    });\r\n\r\n    // Initialize user data\r\n    this.userService.initializeUser();\r\n\r\n    this.accessService.accessTypeChanges().subscribe((accessType) => {\r\n      if (accessType == AccessType.Viewer) {\r\n        this.hasDeleteAccess = false;\r\n      } else {\r\n        this.hasDeleteAccess = true;\r\n      }\r\n    });\r\n\r\n    this.projectService.currentProjectChanges().subscribe((project) => {\r\n      if (project) {\r\n        this.currentProject = project;\r\n        if (project.diagrams.length > 0) {\r\n          this.currentDiagramName = project.diagrams[0].name;\r\n          this.title = `${project.name} - ${this.currentDiagramName}`;\r\n        } else this.title = `${project.name}`;\r\n      }\r\n    });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      if (diagrams) this.noOfDiagram = diagrams.length;\r\n    });\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) {\r\n        this.currentDiagramName = diagram.name;\r\n        this.title = `${this.currentProject?.name} - ${this.currentDiagramName}`;\r\n      } else {\r\n        this.title = `${this.currentProject?.name}`;\r\n      }\r\n    });\r\n\r\n    // Subscribe to loading state\r\n    this.loaderService.isLoading$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((status) => {\r\n        // Use setTimeout to break change detection cycle\r\n        setTimeout(() => {\r\n          this.isLoading = status;\r\n          // Manually trigger change detection\r\n          this.cdr.detectChanges();\r\n        });\r\n      });\r\n\r\n    // Subscribe to loader type\r\n    this.loaderService.loaderType$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((type) => {\r\n        // Use setTimeout to break change detection cycle\r\n        setTimeout(() => {\r\n          this.loaderType = type;\r\n          // Manually trigger change detection\r\n          this.cdr.detectChanges();\r\n        });\r\n      });\r\n  }\r\n  ngAfterViewInit(): void {\r\n    this.title = 'UML Web Application';\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  goBack() {\r\n    if (this.router.url.includes('/editor')) {\r\n      this.title = 'UML Web Application';\r\n      // Reset search term when navigating back to dashboard\r\n      this.searchBarService.resetSearch();\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  isEditorRoute(): boolean {\r\n    return this.router.url.includes('/editor');\r\n  }\r\n\r\n  openAboutModal(): void {\r\n    this.dialog.open(AboutModalComponent, {\r\n      width: '42%',\r\n      maxWidth: '70vw',\r\n      panelClass: 'about-modal',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Performs the logout operation.\r\n   * @memberof MainNavComponent\r\n   */\r\n  onLogout() {\r\n    if (this.router.url.includes('editor')) {\r\n      this.projectService.handleProjectUnlock(this.currentProject.id!);\r\n    }\r\n    this.userService.logout();\r\n  }\r\n\r\n  onImageError() {\r\n    // Set to empty string to show the default user icon\r\n    this.profilePicture = '';\r\n  }\r\n\r\n  onDiagramClear() {\r\n    this.diagramService.triggerDelete();\r\n  }\r\n  downloadCurrent() {\r\n    this.diagramService.triggerCurrentDiagramDownload();\r\n  }\r\n  downloadAllDiagrams(isForOnlyImage: boolean) {\r\n    this.diagramService.triggerAllDiagramDownload(isForOnlyImage);\r\n  }\r\n  changeLanguage(langCode: string) {\r\n    this.languageService.changeLanguage(langCode);\r\n  }\r\n  shouldShowTooltip(name: string, length: number): boolean {\r\n    return name.length > length;\r\n  }\r\n  createNewProject() {\r\n    this.projectService.openProjectDialog();\r\n  }\r\n\r\n  openVersionHistory() {\r\n    this.navbarService.setShowVersionHistory(true);\r\n  }\r\n\r\n  closeVersionHistory(): void {\r\n    // If you're using a service with signals as mentioned earlier\r\n    this.navbarService.setShowVersionHistory(false);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAGEA,SAAS,EACTC,MAAM,QAGD,eAAe;AAGtB,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAEzC,SAASC,UAAU,QAAwB,8BAA8B;AAiBzE,SAASC,mBAAmB,QAAQ,sCAAsC;AAOnE,WAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAuB3BC,YACSC,MAAc,EACbC,UAAsB,EACtBC,aAA4B,EAC5BC,cAA8B,EAC9BC,YAA0B,EAC1BC,WAAwB,EACxBC,eAAgC,EAChCC,cAA8B,EAC/BC,aAA4B,EAC3BC,aAA4B,EAC5BC,MAAiB,EACjBC,GAAsB,EACtBC,qBAA4C,EAC5CC,gBAAkC,EAClCC,eAAgC;IAdjC,KAAAd,MAAM,GAANA,MAAM;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IArClB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,KAAK,GAAW,qBAAqB;IAC5C,KAAAC,kBAAkB,GAAW,EAAE;IAExB,KAAAC,QAAQ,GAAW,EAAE;IAC5B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAe,SAAS;IAClC,KAAAC,WAAW,GAAW,CAAC;IACf,KAAAC,QAAQ,GAAG,IAAI/B,OAAO,EAAQ;IACtC,KAAAgC,gBAAgB,GAAqB,IAAIC,IAAI,EAAE;IAC/C,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,eAAe,GAA0B,IAAI;IAC7C,KAAAC,aAAa,GAAkB;MAC7BC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE;KACf;IAkBC1C,MAAM,CAAC,MAAK;MACV,IAAI,CAACiC,gBAAgB,GAAG,IAAI,CAACzB,UAAU,CAACyB,gBAAgB,EAAE;MAC1D,IAAI,CAACG,eAAe,GAAG,IAAI,CAACjB,qBAAqB,CAACiB,eAAe,EAAE;MACnE,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAACnB,aAAa,CAACmB,kBAAkB,EAAE;IACnE,CAAC,CAAC;EACJ;EAEAQ,QAAQA,CAAA;IACN,IAAI,CAAC9B,eAAe,CAACS,SAAS,CAACsB,SAAS,CAAEC,KAAa,IAAI;MACzD,IAAI,CAACvB,SAAS,GAAGuB,KAAK;IACxB,CAAC,CAAC;IACF,IAAI,CAACrC,UAAU,CAACsC,aAAa,EAAE;IAE/B;IACA,IAAI,CAAClC,WAAW,CAACmC,WAAW,EAAE,CAACH,SAAS,CAAEI,IAAI,IAAI;MAChD,IAAIA,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QACxC;QACA,IAAIH,IAAI,CAACzB,cAAc,EAAE;UACvB,IAAI,CAACA,cAAc,GAAGyB,IAAI,CAACzB,cAAc;;QAE3C;QACA,IAAIyB,IAAI,CAACI,IAAI,EAAE;UACb,IAAI,CAAC1B,QAAQ,GAAGsB,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;;IAG7C,CAAC,CAAC;IAEF;IACA,IAAI,CAACzC,WAAW,CAAC0C,cAAc,EAAE;IAEjC,IAAI,CAAC7C,aAAa,CAAC8C,iBAAiB,EAAE,CAACX,SAAS,CAAEY,UAAU,IAAI;MAC9D,IAAIA,UAAU,IAAIrD,UAAU,CAACsD,MAAM,EAAE;QACnC,IAAI,CAAC9B,eAAe,GAAG,KAAK;OAC7B,MAAM;QACL,IAAI,CAACA,eAAe,GAAG,IAAI;;IAE/B,CAAC,CAAC;IAEF,IAAI,CAACb,cAAc,CAAC4C,qBAAqB,EAAE,CAACd,SAAS,CAAEe,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE;QACX,IAAI,CAACC,cAAc,GAAGD,OAAO;QAC7B,IAAIA,OAAO,CAACE,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAAC1B,kBAAkB,GAAGkC,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACT,IAAI;UAClD,IAAI,CAAC5B,KAAK,GAAG,GAAGmC,OAAO,CAACP,IAAI,MAAM,IAAI,CAAC3B,kBAAkB,EAAE;SAC5D,MAAM,IAAI,CAACD,KAAK,GAAG,GAAGmC,OAAO,CAACP,IAAI,EAAE;;IAEzC,CAAC,CAAC;IACF,IAAI,CAACzC,YAAY,CAACmD,6BAA6B,EAAE,CAAClB,SAAS,CAAEiB,QAAQ,IAAI;MACvE,IAAIA,QAAQ,EAAE,IAAI,CAAC9B,WAAW,GAAG8B,QAAQ,CAACV,MAAM;IAClD,CAAC,CAAC;IACF,IAAI,CAACxC,YAAY,CAACoD,oBAAoB,EAAE,CAACnB,SAAS,CAAEoB,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE;QACX,IAAI,CAACvC,kBAAkB,GAAGuC,OAAO,CAACZ,IAAI;QACtC,IAAI,CAAC5B,KAAK,GAAG,GAAG,IAAI,CAACoC,cAAc,EAAER,IAAI,MAAM,IAAI,CAAC3B,kBAAkB,EAAE;OACzE,MAAM;QACL,IAAI,CAACD,KAAK,GAAG,GAAG,IAAI,CAACoC,cAAc,EAAER,IAAI,EAAE;;IAE/C,CAAC,CAAC;IAEF;IACA,IAAI,CAACrC,aAAa,CAACkD,UAAU,CAC1BC,IAAI,CAAChE,SAAS,CAAC,IAAI,CAAC8B,QAAQ,CAAC,CAAC,CAC9BY,SAAS,CAAEuB,MAAM,IAAI;MACpB;MACAC,UAAU,CAAC,MAAK;QACd,IAAI,CAACvC,SAAS,GAAGsC,MAAM;QACvB;QACA,IAAI,CAACjD,GAAG,CAACmD,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACtD,aAAa,CAACuD,WAAW,CAC3BJ,IAAI,CAAChE,SAAS,CAAC,IAAI,CAAC8B,QAAQ,CAAC,CAAC,CAC9BY,SAAS,CAAE2B,IAAI,IAAI;MAClB;MACAH,UAAU,CAAC,MAAK;QACd,IAAI,CAACtC,UAAU,GAAGyC,IAAI;QACtB;QACA,IAAI,CAACrD,GAAG,CAACmD,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EACAG,eAAeA,CAAA;IACb,IAAI,CAAChD,KAAK,GAAG,qBAAqB;IAClC,IAAI,CAACN,GAAG,CAACmD,aAAa,EAAE;EAC1B;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACzC,QAAQ,CAAC0C,IAAI,EAAE;IACpB,IAAI,CAAC1C,QAAQ,CAAC2C,QAAQ,EAAE;EAC1B;EAEAC,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACrE,MAAM,CAACsE,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MACvC,IAAI,CAACtD,KAAK,GAAG,qBAAqB;MAClC;MACA,IAAI,CAACJ,gBAAgB,CAAC2D,WAAW,EAAE;MACnC,IAAI,CAACxE,MAAM,CAACyE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC1E,MAAM,CAACsE,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC;EAC5C;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAAC/E,mBAAmB,EAAE;MACpCgF,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;KACb,CAAC;EACJ;EAEA;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChF,MAAM,CAACsE,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtC,IAAI,CAAChE,cAAc,CAAC0E,mBAAmB,CAAC,IAAI,CAAC5B,cAAc,CAAC6B,EAAG,CAAC;;IAElE,IAAI,CAAC7E,WAAW,CAAC8E,MAAM,EAAE;EAC3B;EAEAC,YAAYA,CAAA;IACV;IACA,IAAI,CAACpE,cAAc,GAAG,EAAE;EAC1B;EAEAqE,cAAcA,CAAA;IACZ,IAAI,CAAClF,cAAc,CAACmF,aAAa,EAAE;EACrC;EACAC,eAAeA,CAAA;IACb,IAAI,CAACpF,cAAc,CAACqF,6BAA6B,EAAE;EACrD;EACAC,mBAAmBA,CAACC,cAAuB;IACzC,IAAI,CAACvF,cAAc,CAACwF,yBAAyB,CAACD,cAAc,CAAC;EAC/D;EACAE,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAACvF,eAAe,CAACsF,cAAc,CAACC,QAAQ,CAAC;EAC/C;EACAC,iBAAiBA,CAACjD,IAAY,EAAED,MAAc;IAC5C,OAAOC,IAAI,CAACD,MAAM,GAAGA,MAAM;EAC7B;EACAmD,gBAAgBA,CAAA;IACd,IAAI,CAACxF,cAAc,CAACyF,iBAAiB,EAAE;EACzC;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACxF,aAAa,CAACyF,qBAAqB,CAAC,IAAI,CAAC;EAChD;EAEAC,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC1F,aAAa,CAACyF,qBAAqB,CAAC,KAAK,CAAC;EACjD;CACD;AArMYpG,gBAAgB,GAAAsG,UAAA,EAL5B5G,SAAS,CAAC;EACT6G,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,2BAA2B;EACxCC,SAAS,EAAE,CAAC,2BAA2B;CACxC,CAAC,C,EACWzG,gBAAgB,CAqM5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}