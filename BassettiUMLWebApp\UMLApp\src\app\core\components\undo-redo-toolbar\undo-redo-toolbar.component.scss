.undo-redo-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: transparent;
  border-radius: 4px;
  
  &:focus {
    outline: none;
  }

  .toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 2px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
  }

  button {
    width: 36px;
    height: 36px;
    min-width: 36px;
    border-radius: 4px;
    transition: all 0.2s ease;
    
    &:not(:disabled) {
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
      
      mat-icon {
        color: rgba(0, 0, 0, 0.26);
      }
    }

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      transition: color 0.2s ease;
    }
  }

  .undo-button {
    &:not(:disabled) {
      mat-icon {
        color: #1976d2;
      }
      
      &:hover mat-icon {
        color: #1565c0;
      }
    }
  }

  .redo-button {
    &:not(:disabled) {
      mat-icon {
        color: #388e3c;
      }
      
      &:hover mat-icon {
        color: #2e7d32;
      }
    }
  }

  .operation-info {
    display: flex;
    flex-direction: column;
    font-size: 11px;
    color: rgba(0, 0, 0, 0.6);
    margin-left: 8px;
    max-width: 200px;

    .undo-info,
    .redo-info {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }

    .undo-info {
      color: #1976d2;
    }

    .redo-info {
      color: #388e3c;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .undo-redo-toolbar {
    .operation-info {
      display: none;
    }
  }
}

// Dark theme support
.dark-theme .undo-redo-toolbar {
  .toolbar-group {
    border-color: rgba(255, 255, 255, 0.12);
    background: rgba(0, 0, 0, 0.8);
  }

  button {
    &:not(:disabled):hover {
      background-color: rgba(255, 255, 255, 0.04);
    }

    &:disabled mat-icon {
      color: rgba(255, 255, 255, 0.26);
    }
  }

  .operation-info {
    color: rgba(255, 255, 255, 0.6);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .undo-redo-toolbar {
    .toolbar-group {
      border-width: 2px;
      border-color: #000;
    }

    button {
      &:disabled mat-icon {
        color: #666;
      }
    }
  }
}
