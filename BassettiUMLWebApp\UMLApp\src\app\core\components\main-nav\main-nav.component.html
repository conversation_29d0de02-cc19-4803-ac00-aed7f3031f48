<div class="main-content">
  <mat-toolbar
    class="main-toolbar"
    [ngClass]="{ 'panel-open': showVersionHistory }"
  >
    <div class="header-section">
      <!-- Back button shown only when version history panel is open -->
      <div class="version-history" *ngIf="showVersionHistory">
        <button
          mat-icon-button
          class="back-button"
          (click)="closeVersionHistory()"
        >
          <mat-icon>arrow_back</mat-icon>
        </button>

        <p>{{ selectedVersion?.name }}</p>
      </div>

      <!-- Original logo and title (visible only when panel is closed) -->
      <ng-container *ngIf="!showVersionHistory">
        <img
          class="toolbar-logo"
          src="./assets/img/logo.png"
          (click)="goBack()"
        />
        <h3 class="title">
          {{ title }}
        </h3>
      </ng-container>
    </div>

    <!-- Right side of toolbar (hidden when panel is open) -->
    <div class="header-section" *ngIf="!showVersionHistory">
      <!-- Undo/Redo Toolbar -->
      <app-undo-redo-toolbar *ngIf="isEditorRoute()"></app-undo-redo-toolbar>

      <div
        *ngIf="isLoading && loaderType === 'toolbar' && isEditorRoute()"
        color="accent"
        class="saving-indicator"
      >
        <span>{{ "toolbar.savingMsg" | translate }}</span>
      </div>
      <p
        *ngIf="
          !isLoading &&
          isEditorRoute() &&
          currentProject != null &&
          !showVersionHistory
        "
        class="modified-date"
      >
        {{ "toolbar.lastModified" | translate }} :
        {{ lastModifiedDate | timeAgo }}
      </p>
      <fa-icon
        [icon]="['fas', 'download']"
        size="xl"
        class="main-toolbar-icon"
        [matTooltip]="'toolbar.downloadDiagram' | translate"
        [matMenuTriggerFor]="downloadMenu"
        matTooltipClass="tooltip-custom"
        *ngIf="isEditorRoute() && !showVersionHistory"
        [ngClass]="{ 'disabled-icon': noOfDiagram == 0 }"
      >
      </fa-icon>
      <!-- <fa-icon
        [icon]="['fas', 'history']"
        size="xl"
        class="main-toolbar-icon"
        [matTooltip]="'toolbar.versionHistory' | translate"
        matTooltipClass="tooltip-custom"
        *ngIf="isEditorRoute()"
        (click)="openVersionHistory()"
        [ngClass]="{ 'disabled-icon': !hasDeleteAccess || noOfDiagram == 0 }"
      >
      </fa-icon> -->
      <mat-menu #downloadMenu="matMenu" class="mat-menu">
        <button mat-menu-item class="menu-item" (click)="downloadCurrent()">
          <fa-icon [icon]="['fas', 'file-image']"></fa-icon>
          <span>{{ "toolbar.download-current" | translate }}</span>
        </button>
        <button
          mat-menu-item
          [matMenuTriggerFor]="downloadSubMenu"
          class="menu-item"
          [ngClass]="{ 'disabled-icon': selectedVersion != null }"
        >
          <span>{{ "toolbar.download-all" | translate }}</span>
        </button>
      </mat-menu>
      <mat-menu #downloadSubMenu="matMenu">
        <button
          mat-menu-item
          class="menu-item"
          (click)="downloadAllDiagrams(true)"
        >
          <fa-icon [icon]="['fas', 'file-archive']"></fa-icon>
          <span>{{ "toolbar.download-only-images" | translate }}</span>
        </button>
        <button
          mat-menu-item
          class="menu-item"
          (click)="downloadAllDiagrams(false)"
        >
          <fa-icon [icon]="['fas', 'file-pdf']"></fa-icon>
          <span>{{ "toolbar.download-with-details" | translate }}</span>
        </button>
      </mat-menu>
      <fa-icon
        [icon]="['fas', 'trash']"
        size="xl"
        class="main-toolbar-icon"
        [matTooltip]="'toolbar.clearDiagram' | translate"
        matTooltipClass="tooltip-custom"
        (click)="onDiagramClear()"
        *ngIf="isEditorRoute()"
        [ngClass]="{ 'disabled-icon': !hasDeleteAccess || noOfDiagram == 0 }"
      >
      </fa-icon>
      <button
        mat-stroked-button
        class="new-project-btn"
        (click)="createNewProject()"
        *ngIf="!isEditorRoute()"
      >
        {{ "dashboard.btnText" | translate }}
      </button>
      <img
        [src]="profilePicture"
        alt="profile"
        class="rounded main-toolbar-icon"
        [matMenuTriggerFor]="accountMenu"
        *ngIf="profilePicture != null && profilePicture != ''"
        (error)="onImageError()"
      />
      <fa-icon
        *ngIf="profilePicture == null || profilePicture == ''"
        [icon]="['fas', 'user-circle']"
        size="xl"
        class="main-toolbar-icon"
        [matMenuTriggerFor]="accountMenu"
        [matTooltip]="'toolbar.user' | translate"
        matTooltipClass="tooltip-custom"
      ></fa-icon>
      <mat-menu
        #accountMenu="matMenu"
        class="main-toolbar-menu"
        [overlapTrigger]="false"
      >
        <p class="user-name">
          {{ "toolbar.hello" | translate }} {{ userName }}
        </p>
        <button mat-menu-item [matMenuTriggerFor]="languageMenu">
          <fa-icon
            [icon]="['fal', 'globe']"
            size="lg"
            class="menu-icon"
          ></fa-icon>
          <span>{{ "toolbar.language" | translate }}</span>
        </button>
        <button mat-menu-item (click)="openAboutModal()">
          <fa-icon
            [icon]="['fal', 'info-circle']"
            size="lg"
            class="menu-icon"
          ></fa-icon>
          <span>{{ "toolbar.about" | translate }}</span>
        </button>
        <button mat-menu-item (click)="onLogout()">
          <fa-icon
            [icon]="['fal', 'power-off']"
            size="lg"
            class="menu-icon logout"
          ></fa-icon>
          <span>{{ "toolbar.logout" | translate }}</span>
        </button>
      </mat-menu>
      <mat-menu #languageMenu="matMenu">
        <button
          *ngFor="let lang of languages"
          mat-menu-item
          (click)="changeLanguage(lang.code)"
        >
          <img class="menu-flag" src="./assets/img/flags/{{ lang.code }}.svg" />
          <span>{{
            lang.name | translate : { code: lang.code, lang: lang.nameBis }
          }}</span>
        </button>
      </mat-menu>
    </div>
  </mat-toolbar>
</div>
