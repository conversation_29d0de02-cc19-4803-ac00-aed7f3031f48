{"toolbar": {"hello": "Bonjou<PERSON>,", "user": "<PERSON><PERSON> utilisateur", "about": "À propos", "logout": "Se déconnecter", "language": "<PERSON><PERSON>", "savingMsg": "Enregistrement...", "clearDiagram": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>me", "downloadDiagram": "Télécharger le diagramme", "versionHistory": "Historique des versions", "releaseNew": "Publier une nouvelle version", "download-current": "Diagramme actuel", "download-all": "Tous les diagrammes", "download-only-images": "Images", "download-with-details": "Rapport", "onGridView": "Activer la disposition en grille", "offGridView": "Désactiver la disposition en grille", "lastModified": "Dernière modification"}, "lang": {"default": "Langage par défault ({{code}})", "specific": "{{lang}} (d<PERSON><PERSON><PERSON>)"}, "errors": {"unhandled": {"content": "Une erreur non gérée s'est produite, veuillez contacter votre administrateur avec les détails suivants.", "header": "Erreur non gérée"}, "0": {"content": "Le service API Uml App n'est pas disponible ou l'URL configurée n'est pas valide. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Service API non trouvé"}, "400": {"default": {"content": "Une requête sur le service API Uml App n'est pas correctement construite ou les paramètres ont changé. Veuillez contacter votre administrateur avec les détails suivants.", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "401": {"default": {"content": "La connexion au service API Uml App n'est pas disponible pour certaines raisons. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Service non disponible"}}, "403": {"default": {"content": "La ressource que vous essayez d'accéder est protégée et vous n'êtes pas autorisé à la consulter. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Accès interdit"}}, "404": {"default": {"content": "La ressource demandée n'a pas été trouvée ou l'URL de la requête n'existe pas. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Ressource non trouvée"}}, "500": {"default": {"content": "Le serveur a rencontré une erreur interne ou un problème de délai d'attente et ne peut pas fournir la ressource demandée. Veuillez contacter votre administrateur avec les détails suivants.", "header": "<PERSON>rreur interne du serveur"}}, "lockedProject": {"header": "Projet verrouillé !", "content": "Ce projet est actuellement verrouillé par "}}, "errorMessage": {"details": "Détails"}, "window": {"close": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "save": "Enregistrer"}, "dashboard": {"title": "Projets", "btnText": "+ Nouveau Projet", "editProject": "Modifier le Projet", "newProject": "Nouveau Projet", "deleteProject": "Supprimer le Projet", "noProject": "Aucun projet disponible", "loading": "Chargement des projets...", "error": {"loadingProjects": "Erreur lors du chargement des projets. Veuillez réessayer."}, "shareProject": "Partager le projet", "filter": {"projectType": {"title": "Type de projet", "placeholder": "Type de projet"}, "productLine": {"title": "Ligne de produit", "placeholder": "Ligne de produit"}, "selfProject": {"title": "Uniquement mes projets"}, "clearFilter": "Effacer tous les filtres"}, "search": {"placeholder": "Rechercher par nom, description, type, ligne de produit ou propriétaire..."}, "table": {"siNo": "N°", "name": "Nom", "description": "Description du projet", "type": "Type de projet", "productLine": "Ligne de produit", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastModified": "Dernière modification", "action": "Action", "filter": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "share": "Partager", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "diagram": {"components": "Composants", "library": "Bibliothèque", "properties": "Propriétés", "propertyDescription": "Veuillez sélectionner un élément de la bibliothèque pour afficher les propriétés", "selectedDiagram": "Diagramme Sélectionné", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "openMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createDiagram": "<PERSON><PERSON>er un Diagramme", "editDiagram": "Modifier le Diagramme", "diagramPlaceholderText": "Nom du diagramme", "create": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "method": "Méthode", "attribute": "Attribut", "literal": " Littéral", "search": "Rechercher dans la bibliothèque...", "notFound": "<PERSON><PERSON><PERSON> nœud trouvé", "downloadNotAllowedMsg": "L'exportation n'est pas possible car le diagramme est vide", "zoomIn": "Zoom avant", "zoomOut": "Zoom arri<PERSON>", "diagramsList": "Diagrammes"}, "folder": {"header": "<PERSON><PERSON>er un nouveau dossier", "placeholder": "Entrez le nom du dossier"}, "class": {"header": "Créer une nouvelle classe", "placeholder": "Entrez le nom de la classe"}, "enumeration": {"header": "Créer une nouvelle énumération", "placeholder": "Entrez le nom de l'énumération"}, "attribute": {"header": "<PERSON><PERSON>er un nouvel attribut", "placeholder": "Entrez le nom de l'attribut"}, "method": {"header": "<PERSON><PERSON>er une nouvelle méthode", "placeholder": "Entrez le nom de la méthode"}, "literal": {"header": "Créer un nouveau littéral", "placeholder": "Entrez le nom du littéral"}, "dialog": {"yes": "O<PERSON>", "no": "Non", "title": "Voulez-vous supprimer ?", "create": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "projectName": "Nom du Projet", "description": "Description", "productLine": "Ligne de produit", "search": "Rechercher une ligne de produit", "noResults": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "type": "Type", "deleteTitle": "Voulez-vous supprimer le lien de tous les diagrammes?", "releaseVersion": "Version de sortie", "release": "<PERSON><PERSON><PERSON>", "versionName": "Nom de la version"}, "shareDialog": {"title": "Partager le projet", "accessTitle": "<PERSON><PERSON> ayant accès", "addPeople": "Ajouter des personnes", "copyBtn": "Copier le lien", "access": "Accès", "email": "E-mail", "0": "Administrateur", "1": "<PERSON><PERSON><PERSON>", "2": "Visualiseur", "send": "Envoyer", "removeAccess": "Supprimer l'accès"}, "snackBar": {"deleteProjectMsg": "Projet supprimé avec succès", "deleteTempClassMsg": "Classe modèle supprimée avec succès", "deleteTempEnumMsg": "Énumération de modèle supprimée avec succès", "deleteAttributeMsg": "Attribut supprimé avec succès", "deleteLiteralMsg": "Littéral d'énumération supprimé avec succès", "diagramDeleteInfo": "Vous n'avez pas de diagramme. Veuillez en ajouter un.", "diagramDeleteMsg": "Diagramme supprimé avec succès", "diagramUpdateMsg": "Diagramme mis à jour avec succès", "projectUpdatedMsg": "Projet mis à jour avec succès", "projectCreationMsg": "Projet créé avec succès", "deleteFolderMsg": "Dossier supprimé avec succès", "createFolderMsg": "Dossier c<PERSON><PERSON> avec succès", "projectShareMsg": "Projet partagé avec succès", "accessChangedMsg": "Accès modifié avec succès", "commentDeleteMsg": "Commentaire supprimé avec succès", "permissionRemovedMsg": "Permission de projet supprimée avec succès", "linkCopiedMsg": "Lien copié dans le presse-papiers", "linkToLinkAlreadyExists": "Une autre classe associative est déjà liée à ce lien"}, "property": {"name": "Nom", "color": "<PERSON><PERSON><PERSON>", "type": "Type", "description": "Description", "volumetry": "Volumétrie", "tag": "Étiquette", "fromComment": "De<PERSON><PERSON> le commentaire", "toComment": "Vers le commentaire"}, "about": {"version": "Version", "releaseDate": "Date de sortie", "descriptionMsg": "L'application UML Web permet aux utilisateurs de créer et de gérer des diagrammes UML en faisant glisser et déposer des composants tels que des classes, des énumérations et des packages, avec des options pour les lier, les redimensionner et les personnaliser. Les utilisateurs peuvent organiser les projets dans des dossiers, activer l'alignement sur la grille et télécharger les diagrammes en PDF. Elle offre une interface intuitive pour concevoir et gérer des modèles UML professionnels."}, "colorPicker": {"customColor": "<PERSON><PERSON><PERSON> person<PERSON>", "apply": "Appliquer", "primaryColors": "Couleurs primaires", "extendedColors": "Couleurs étendues", "selectColor": "<PERSON><PERSON><PERSON><PERSON>ner une couleur", "colors": {"red": "Rouge", "green": "<PERSON>ert", "blue": "Bleu", "lightBlue": "<PERSON><PERSON>u clair", "skyBlue": "Bleu ciel", "yellow": "Jaune", "darkGray": "<PERSON><PERSON> fon<PERSON>", "gray": "<PERSON><PERSON>", "lightGray": "<PERSON><PERSON> clair", "darkRed": "Rouge foncé", "lightPink": "<PERSON> clair", "pink": "<PERSON>", "darkGreen": "<PERSON><PERSON> fon<PERSON>", "mintGreen": "Vert menthe", "brightGreen": "<PERSON><PERSON> vif", "olive": "<PERSON>", "orange": "Orange", "lightOrange": "Orange clair", "brown": "<PERSON><PERSON>", "salmon": "<PERSON><PERSON><PERSON>", "coral": "<PERSON><PERSON>", "cyan": "<PERSON><PERSON>", "maroon": "Bordeaux", "purple": "Violet"}}}