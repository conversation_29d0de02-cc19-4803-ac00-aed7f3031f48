import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { UndoRedoService, UndoRedoState } from '../../services/undo-redo/undo-redo.service';

@Component({
  selector: 'app-undo-redo-toolbar',
  templateUrl: './undo-redo-toolbar.component.html',
  styleUrls: ['./undo-redo-toolbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UndoRedoToolbarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  undoRedoState: UndoRedoState = {
    canUndo: false,
    canRedo: false,
    undoText: '',
    redoText: ''
  };

  constructor(
    private undoRedoService: UndoRedoService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.subscribeToUndoRedoState();
    this.subscribeToOperations();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Subscribe to undo/redo state changes
   */
  private subscribeToUndoRedoState(): void {
    this.undoRedoService.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.undoRedoState = state;
        this.cdr.markForCheck();
      });
  }

  /**
   * Subscribe to undo/redo operations for additional handling
   */
  private subscribeToOperations(): void {
    this.undoRedoService.operations$
      .pipe(takeUntil(this.destroy$))
      .subscribe(({ type, operation }) => {
        // Handle any additional logic needed after undo/redo operations
        this.handleOperationComplete(type, operation);
      });
  }

  /**
   * Handle undo operation
   */
  onUndo(): void {
    if (this.undoRedoState.canUndo) {
      this.undoRedoService.undo();
    }
  }

  /**
   * Handle redo operation
   */
  onRedo(): void {
    if (this.undoRedoState.canRedo) {
      this.undoRedoService.redo();
    }
  }

  /**
   * Handle keyboard shortcuts
   */
  onKeyDown(event: KeyboardEvent): void {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key.toLowerCase()) {
        case 'z':
          if (event.shiftKey) {
            // Ctrl+Shift+Z or Cmd+Shift+Z for redo
            event.preventDefault();
            this.onRedo();
          } else {
            // Ctrl+Z or Cmd+Z for undo
            event.preventDefault();
            this.onUndo();
          }
          break;
        case 'y':
          // Ctrl+Y or Cmd+Y for redo (alternative)
          event.preventDefault();
          this.onRedo();
          break;
      }
    }
  }

  /**
   * Get tooltip text for undo button
   */
  getUndoTooltip(): string {
    if (!this.undoRedoState.canUndo) {
      return 'toolbar.undo.disabled';
    }
    return this.undoRedoState.undoText 
      ? `toolbar.undo.action: ${this.undoRedoState.undoText}`
      : 'toolbar.undo.default';
  }

  /**
   * Get tooltip text for redo button
   */
  getRedoTooltip(): string {
    if (!this.undoRedoState.canRedo) {
      return 'toolbar.redo.disabled';
    }
    return this.undoRedoState.redoText 
      ? `toolbar.redo.action: ${this.undoRedoState.redoText}`
      : 'toolbar.redo.default';
  }

  /**
   * Handle completion of undo/redo operations
   */
  private handleOperationComplete(type: 'undo' | 'redo', operation: any): void {
    // Additional logic can be added here for specific operation handling
    console.log(`${type} operation completed:`, operation);
  }

  /**
   * Get CSS classes for undo button
   */
  getUndoButtonClasses(): string[] {
    const classes = ['undo-button'];
    if (!this.undoRedoState.canUndo) {
      classes.push('disabled');
    }
    return classes;
  }

  /**
   * Get CSS classes for redo button
   */
  getRedoButtonClasses(): string[] {
    const classes = ['redo-button'];
    if (!this.undoRedoState.canRedo) {
      classes.push('disabled');
    }
    return classes;
  }
}
