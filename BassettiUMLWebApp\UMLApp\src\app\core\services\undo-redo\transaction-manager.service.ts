import { Injectable } from '@angular/core';
import * as go from 'gojs';
import { Observable, Subject } from 'rxjs';
import { UndoRedoService } from './undo-redo.service';

export interface TransactionContext {
  name: string;
  diagram: go.Diagram;
  operations: TransactionOperation[];
  rollbackOperations: TransactionOperation[];
}

export interface TransactionOperation {
  type: 'model' | 'database' | 'ui';
  execute: () => Promise<any> | any;
  rollback?: () => Promise<any> | any;
  description: string;
}

export interface DatabaseOperation {
  apiCall: () => Observable<any>;
  rollbackCall?: () => Observable<any>;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

@Injectable({
  providedIn: 'root'
})
export class TransactionManagerService {
  private _activeTransactions: Map<string, TransactionContext> = new Map();
  private _transactionSubject = new Subject<{ type: 'start' | 'commit' | 'rollback', context: TransactionContext }>();

  constructor(private undoRedoService: UndoRedoService) {}

  /**
   * Observable for transaction events
   */
  get transactions$(): Observable<{ type: 'start' | 'commit' | 'rollback', context: TransactionContext }> {
    return this._transactionSubject.asObservable();
  }

  /**
   * Start a new complex transaction
   */
  startTransaction(name: string, diagram: go.Diagram): string {
    const transactionId = this.generateTransactionId();
    const context: TransactionContext = {
      name,
      diagram,
      operations: [],
      rollbackOperations: []
    };

    this._activeTransactions.set(transactionId, context);
    diagram.startTransaction(name);
    
    this._transactionSubject.next({ type: 'start', context });
    return transactionId;
  }

  /**
   * Add a model operation to the transaction
   */
  addModelOperation(
    transactionId: string, 
    operation: () => void, 
    description: string,
    rollback?: () => void
  ): void {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    context.operations.push({
      type: 'model',
      execute: operation,
      rollback,
      description
    });
  }

  /**
   * Add a database operation to the transaction
   */
  addDatabaseOperation(
    transactionId: string,
    dbOperation: DatabaseOperation,
    description: string
  ): void {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    context.operations.push({
      type: 'database',
      execute: () => this.executeDatabaseOperation(dbOperation),
      description
    });
  }

  /**
   * Add a UI operation to the transaction
   */
  addUIOperation(
    transactionId: string,
    operation: () => void,
    description: string,
    rollback?: () => void
  ): void {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    context.operations.push({
      type: 'ui',
      execute: operation,
      rollback,
      description
    });
  }

  /**
   * Commit the transaction
   */
  async commitTransaction(transactionId: string): Promise<void> {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    try {
      // Execute all operations
      for (const operation of context.operations) {
        await operation.execute();
      }

      // Commit the GoJS transaction
      context.diagram.commitTransaction(context.name);
      
      this._transactionSubject.next({ type: 'commit', context });
      this._activeTransactions.delete(transactionId);
    } catch (error) {
      await this.rollbackTransaction(transactionId);
      throw error;
    }
  }

  /**
   * Rollback the transaction
   */
  async rollbackTransaction(transactionId: string): Promise<void> {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    try {
      // Execute rollback operations in reverse order
      for (let i = context.rollbackOperations.length - 1; i >= 0; i--) {
        const operation = context.rollbackOperations[i];
        if (operation.rollback) {
          await operation.rollback();
        }
      }

      // Rollback the GoJS transaction
      context.diagram.rollbackTransaction();
      
      this._transactionSubject.next({ type: 'rollback', context });
    } finally {
      this._activeTransactions.delete(transactionId);
    }
  }

  /**
   * Execute a simple operation with automatic transaction management
   */
  async executeWithTransaction<T>(
    name: string,
    diagram: go.Diagram,
    operation: (transactionId: string) => Promise<T>
  ): Promise<T> {
    const transactionId = this.startTransaction(name, diagram);
    
    try {
      const result = await operation(transactionId);
      await this.commitTransaction(transactionId);
      return result;
    } catch (error) {
      await this.rollbackTransaction(transactionId);
      throw error;
    }
  }

  /**
   * Execute a database operation with proper error handling
   */
  private executeDatabaseOperation(dbOperation: DatabaseOperation): Promise<any> {
    return new Promise((resolve, reject) => {
      dbOperation.apiCall().subscribe({
        next: (result) => {
          if (dbOperation.onSuccess) {
            dbOperation.onSuccess(result);
          }
          resolve(result);
        },
        error: (error) => {
          if (dbOperation.onError) {
            dbOperation.onError(error);
          }
          reject(error);
        }
      });
    });
  }

  /**
   * Create a compound operation that combines model and database changes
   */
  createCompoundOperation(
    name: string,
    diagram: go.Diagram,
    modelOperation: () => void,
    databaseOperation: DatabaseOperation,
    uiOperation?: () => void
  ): Promise<any> {
    return this.executeWithTransaction(name, diagram, async (transactionId) => {
      // Add model operation
      this.addModelOperation(transactionId, modelOperation, `Model: ${name}`);
      
      // Add database operation
      this.addDatabaseOperation(transactionId, databaseOperation, `Database: ${name}`);
      
      // Add UI operation if provided
      if (uiOperation) {
        this.addUIOperation(transactionId, uiOperation, `UI: ${name}`);
      }
      
      return true;
    });
  }

  /**
   * Generate a unique transaction ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get active transaction count
   */
  getActiveTransactionCount(): number {
    return this._activeTransactions.size;
  }

  /**
   * Check if a transaction is active
   */
  isTransactionActive(transactionId: string): boolean {
    return this._activeTransactions.has(transactionId);
  }

  /**
   * Get transaction context
   */
  getTransactionContext(transactionId: string): TransactionContext | undefined {
    return this._activeTransactions.get(transactionId);
  }

  /**
   * Clear all active transactions (emergency cleanup)
   */
  clearAllTransactions(): void {
    for (const [transactionId, context] of this._activeTransactions) {
      try {
        context.diagram.rollbackTransaction();
      } catch (error) {
        console.warn(`Failed to rollback transaction ${transactionId}:`, error);
      }
    }
    this._activeTransactions.clear();
  }
}
