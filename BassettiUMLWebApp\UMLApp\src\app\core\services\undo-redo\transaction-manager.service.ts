import { Injectable } from '@angular/core';
import { Observable, Subject, forkJoin, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import * as go from 'gojs';
import { UndoRedoService } from './undo-redo.service';

export interface TransactionContext {
  id: string;
  name: string;
  diagram: go.Diagram;
  operations: TransactionOperation[];
  rollbackOperations: TransactionOperation[];
  metadata: { [key: string]: any };
}

export interface TransactionOperation {
  id: string;
  type: 'model' | 'database' | 'ui';
  execute: () => Promise<any> | any;
  rollback?: () => Promise<any> | any;
  description: string;
  dependencies?: string[];
}

export interface DatabaseOperation {
  apiCall: () => Observable<any>;
  rollbackCall?: () => Observable<any>;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  retryCount?: number;
  timeout?: number;
}

export interface CompoundOperationResult {
  success: boolean;
  results: any[];
  errors: any[];
  transactionId: string;
}

@Injectable({
  providedIn: 'root'
})
export class TransactionManagerService {
  private _activeTransactions: Map<string, TransactionContext> = new Map();
  private _transactionSubject = new Subject<{ type: string, context: TransactionContext }>();
  private _operationCounter = 0;

  constructor(private undoRedoService: UndoRedoService) {}

  /**
   * Observable for transaction events
   */
  get transactions$(): Observable<{ type: string, context: TransactionContext }> {
    return this._transactionSubject.asObservable();
  }

  /**
   * Start a new complex transaction
   */
  startTransaction(name: string, diagram: go.Diagram, metadata: { [key: string]: any } = {}): string {
    const transactionId = this.generateTransactionId();
    const context: TransactionContext = {
      id: transactionId,
      name,
      diagram,
      operations: [],
      rollbackOperations: [],
      metadata
    };

    this._activeTransactions.set(transactionId, context);
    diagram.startTransaction(name);
    
    this._transactionSubject.next({ type: 'start', context });
    return transactionId;
  }

  /**
   * Add a model operation to the transaction
   */
  addModelOperation(
    transactionId: string, 
    operation: () => void, 
    description: string,
    rollback?: () => void,
    dependencies?: string[]
  ): string {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    const operationId = this.generateOperationId();
    const transactionOp: TransactionOperation = {
      id: operationId,
      type: 'model',
      execute: operation,
      rollback,
      description,
      dependencies
    };

    context.operations.push(transactionOp);
    return operationId;
  }

  /**
   * Add a database operation to the transaction
   */
  addDatabaseOperation(
    transactionId: string, 
    dbOperation: DatabaseOperation, 
    description: string,
    dependencies?: string[]
  ): string {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    const operationId = this.generateOperationId();
    const transactionOp: TransactionOperation = {
      id: operationId,
      type: 'database',
      execute: () => this.executeDatabaseOperation(dbOperation),
      rollback: dbOperation.rollbackCall ? () => this.executeDatabaseOperation({
        apiCall: dbOperation.rollbackCall!,
        onError: dbOperation.onError
      }) : undefined,
      description,
      dependencies
    };

    context.operations.push(transactionOp);
    return operationId;
  }

  /**
   * Add a UI operation to the transaction
   */
  addUIOperation(
    transactionId: string, 
    operation: () => void | Promise<void>, 
    description: string,
    rollback?: () => void | Promise<void>,
    dependencies?: string[]
  ): string {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    const operationId = this.generateOperationId();
    const transactionOp: TransactionOperation = {
      id: operationId,
      type: 'ui',
      execute: operation,
      rollback,
      description,
      dependencies
    };

    context.operations.push(transactionOp);
    return operationId;
  }

  /**
   * Commit the transaction
   */
  async commitTransaction(transactionId: string): Promise<CompoundOperationResult> {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    const results: any[] = [];
    const errors: any[] = [];

    try {
      // Sort operations by dependencies
      const sortedOperations = this.sortOperationsByDependencies(context.operations);

      // Execute all operations in order
      for (const operation of sortedOperations) {
        try {
          const result = await operation.execute();
          results.push({ operationId: operation.id, result });
        } catch (error) {
          errors.push({ operationId: operation.id, error });
          throw error; // Stop execution on first error
        }
      }

      // Commit the GoJS transaction
      context.diagram.commitTransaction(context.name);
      
      this._transactionSubject.next({ type: 'commit', context });
      this._activeTransactions.delete(transactionId);

      return {
        success: true,
        results,
        errors,
        transactionId
      };
    } catch (error) {
      const rollbackResult = await this.rollbackTransaction(transactionId);
      return {
        success: false,
        results,
        errors: [...errors, error],
        transactionId
      };
    }
  }

  /**
   * Rollback the transaction
   */
  async rollbackTransaction(transactionId: string): Promise<void> {
    const context = this._activeTransactions.get(transactionId);
    if (!context) throw new Error(`Transaction ${transactionId} not found`);

    try {
      // Execute rollback operations in reverse order
      const rollbackOps = [...context.operations].reverse().filter(op => op.rollback);
      
      for (const operation of rollbackOps) {
        try {
          if (operation.rollback) {
            await operation.rollback();
          }
        } catch (rollbackError) {
          console.error(`Rollback failed for operation ${operation.id}:`, rollbackError);
        }
      }

      // Rollback the GoJS transaction
      context.diagram.rollbackTransaction();
      
      this._transactionSubject.next({ type: 'rollback', context });
    } finally {
      this._activeTransactions.delete(transactionId);
    }
  }

  /**
   * Execute a transaction with automatic commit/rollback
   */
  async executeWithTransaction<T>(
    name: string, 
    diagram: go.Diagram, 
    operations: (transactionId: string) => Promise<T>,
    metadata: { [key: string]: any } = {}
  ): Promise<T> {
    const transactionId = this.startTransaction(name, diagram, metadata);
    
    try {
      const result = await operations(transactionId);
      await this.commitTransaction(transactionId);
      return result;
    } catch (error) {
      await this.rollbackTransaction(transactionId);
      throw error;
    }
  }

  /**
   * Create a compound operation that combines model and database changes
   */
  createCompoundOperation(
    name: string,
    diagram: go.Diagram,
    modelOperation: () => void,
    databaseOperation: DatabaseOperation,
    uiOperation?: () => void,
    metadata: { [key: string]: any } = {}
  ): Promise<CompoundOperationResult> {
    return this.executeWithTransaction(name, diagram, async (transactionId) => {
      // Add model operation
      this.addModelOperation(transactionId, modelOperation, `Model: ${name}`);
      
      // Add database operation
      this.addDatabaseOperation(transactionId, databaseOperation, `Database: ${name}`);
      
      // Add UI operation if provided
      if (uiOperation) {
        this.addUIOperation(transactionId, uiOperation, `UI: ${name}`);
      }
      
      return await this.commitTransaction(transactionId);
    }, metadata);
  }

  /**
   * Execute a database operation with proper error handling
   */
  private executeDatabaseOperation(dbOperation: DatabaseOperation): Promise<any> {
    const retryCount = dbOperation.retryCount || 0;
    const timeout = dbOperation.timeout || 30000;

    return dbOperation.apiCall()
      .pipe(
        map(result => {
          if (dbOperation.onSuccess) {
            dbOperation.onSuccess(result);
          }
          return result;
        }),
        catchError(error => {
          if (dbOperation.onError) {
            dbOperation.onError(error);
          }
          return throwError(() => error);
        })
      )
      .toPromise();
  }

  /**
   * Sort operations by their dependencies
   */
  private sortOperationsByDependencies(operations: TransactionOperation[]): TransactionOperation[] {
    const sorted: TransactionOperation[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (operation: TransactionOperation) => {
      if (visiting.has(operation.id)) {
        throw new Error(`Circular dependency detected involving operation ${operation.id}`);
      }
      if (visited.has(operation.id)) {
        return;
      }

      visiting.add(operation.id);

      // Visit dependencies first
      if (operation.dependencies) {
        for (const depId of operation.dependencies) {
          const depOperation = operations.find(op => op.id === depId);
          if (depOperation) {
            visit(depOperation);
          }
        }
      }

      visiting.delete(operation.id);
      visited.add(operation.id);
      sorted.push(operation);
    };

    for (const operation of operations) {
      if (!visited.has(operation.id)) {
        visit(operation);
      }
    }

    return sorted;
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `op_${++this._operationCounter}_${Date.now()}`;
  }

  /**
   * Get active transaction count
   */
  getActiveTransactionCount(): number {
    return this._activeTransactions.size;
  }

  /**
   * Get transaction by ID
   */
  getTransaction(transactionId: string): TransactionContext | undefined {
    return this._activeTransactions.get(transactionId);
  }

  /**
   * Cancel a transaction (rollback without executing)
   */
  async cancelTransaction(transactionId: string): Promise<void> {
    const context = this._activeTransactions.get(transactionId);
    if (!context) return;

    context.diagram.rollbackTransaction();
    this._activeTransactions.delete(transactionId);
    this._transactionSubject.next({ type: 'cancel', context });
  }

  /**
   * Clear all active transactions (emergency cleanup)
   */
  clearAllTransactions(): void {
    for (const [id, context] of this._activeTransactions) {
      try {
        context.diagram.rollbackTransaction();
      } catch (error) {
        console.error(`Error rolling back transaction ${id}:`, error);
      }
    }
    this._activeTransactions.clear();
  }
}
