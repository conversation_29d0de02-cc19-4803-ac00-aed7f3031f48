import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Diagram } from 'gojs';
import { Observable, catchError } from 'rxjs';
import { FolderUpdate, ProjectFolder } from 'src/app/shared/model/class';
import { Folder, FolderMove, MoveToFolder } from 'src/app/shared/model/folder';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root',
})
export class FolderApiService {
  private backendUrl: string = environment.backEndUrl;
  constructor(private http: HttpClient) {}

  createFolder(folderObj: ProjectFolder): Observable<ProjectFolder> {
    return this.http
      .post<ProjectFolder>(this.backendUrl + '/Folder', folderObj)
      .pipe(
        catchError((error) => {
          console.error('Error creating folder:', error);
          throw error;
        })
      );
  }

  deleteFolder(folderIds: number[]): Observable<void> {
    return this.http
      .post<void>(this.backendUrl + `/Folder/deleteMultiple`, folderIds)
      .pipe(
        catchError((error) => {
          console.error('Error deleting folder:', error);
          throw error;
        })
      );
  }

  updateFolder(folderObj: FolderUpdate): Observable<FolderUpdate> {
    return this.http
      .patch<FolderUpdate>(this.backendUrl + `/Folder`, folderObj)
      .pipe(
        catchError((error) => {
          console.error('Error updating update folder:', error);
          throw error;
        })
      );
  }
  moveDiagramToFolder(moveDiagram: MoveToFolder): Observable<Diagram> {
    return this.http
      .patch<Diagram>(this.backendUrl + `/Diagram/moveToFolder`, moveDiagram)
      .pipe(
        catchError((error) => {
          console.error('Error moving diagram:', error);
          throw error;
        })
      );
  }
  removeDiagramFromFolder(diagramId: number): Observable<Diagram> {
    return this.http
      .patch<Diagram>(this.backendUrl + `/Diagram/removeFromFolder`, {
        id: diagramId,
      })
      .pipe(
        catchError((error) => {
          console.error('Error moving diagram:', error);
          throw error;
        })
      );
  }
  moveFolderToFolder(moveFolder: FolderMove): Observable<Folder> {
    return this.http
      .patch<Folder>(this.backendUrl + `/Folder/moveToFolder`, moveFolder)
      .pipe(
        catchError((error) => {
          console.error('Error moving folder:', error);
          throw error;
        })
      );
  }
  removeFolderFromFolder(folderId: number): Observable<Folder> {
    return this.http
      .patch<Folder>(this.backendUrl + `/Folder/removeFromFolder`, {
        id: folderId,
      })
      .pipe(
        catchError((error) => {
          console.error('Error moving folder:', error);
          throw error;
        })
      );
  }

  /**
   * Alias methods to match DatabaseSyncService interface
   */
  deleteFolders(folderIds: number[]): Observable<void> {
    return this.deleteFolder(folderIds);
  }

  /**
   * Undo folder deletion
   *
   * @param {number} idFolder folder id
   * @returns {Observable<Folder>}
   *
   * @memberOf FolderApiService
   */
  undoFolderDelete(idFolder: number): Observable<Folder> {
    return this.http
      .patch<Folder>(this.backendUrl + `/Folder/${idFolder}`, {})
      .pipe(
        catchError((error) => {
          console.error('Error undo folder deletion:', error);
          throw error;
        })
      );
  }
}
