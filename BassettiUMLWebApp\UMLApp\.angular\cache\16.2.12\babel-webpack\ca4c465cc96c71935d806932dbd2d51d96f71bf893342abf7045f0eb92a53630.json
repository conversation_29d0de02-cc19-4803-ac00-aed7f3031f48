{"ast": null, "code": "import { effect } from '@angular/core';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { AboutModalComponent } from '../about-modal/about-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/access/access.service\";\nimport * as i4 from \"../../services/diagram/diagram.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nimport * as i6 from \"../../services/user/user.service\";\nimport * as i7 from \"../../services/language/language.service\";\nimport * as i8 from \"../../services/project/project.service\";\nimport * as i9 from \"../../services/loader/loader.service\";\nimport * as i10 from \"../../services/navbar/navbar.service\";\nimport * as i11 from \"@angular/material/dialog\";\nimport * as i12 from \"../../services/versionHistory/version-history.service\";\nimport * as i13 from \"../../services/search-bar/search-bar.service\";\nimport * as i14 from \"@angular/common\";\nimport * as i15 from \"@fortawesome/angular-fontawesome\";\nimport * as i16 from \"@angular/material/button\";\nimport * as i17 from \"@angular/material/tooltip\";\nimport * as i18 from \"@angular/material/icon\";\nimport * as i19 from \"@angular/material/toolbar\";\nimport * as i20 from \"@angular/material/menu\";\nimport * as i21 from \"@ngx-translate/core\";\nimport * as i22 from \"../../../shared/pipes/time-ago.pipe\";\nfunction MainNavComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeVersionHistory());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"arrow_back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.selectedVersion == null ? null : ctx_r0.selectedVersion.name);\n  }\n}\nfunction MainNavComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"img\", 8);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_ng_container_4_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.goBack());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"h3\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n  }\n}\nfunction MainNavComponent_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"toolbar.savingMsg\"));\n  }\n}\nfunction MainNavComponent_div_5_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"timeAgo\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"toolbar.lastModified\"), \" : \", i0.ɵɵpipeBind1(3, 4, ctx_r8.lastModifiedDate), \" \");\n  }\n}\nconst _c0 = function () {\n  return [\"fas\", \"undo\"];\n};\nconst _c1 = function (a0) {\n  return {\n    \"disabled-icon\": a0\n  };\n};\nconst _c2 = function () {\n  return [\"fas\", \"redo\"];\n};\nfunction MainNavComponent_div_5_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"fa-icon\", 36);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_div_3_Template_fa_icon_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onUndo());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"fa-icon\", 36);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_div_3_Template_fa_icon_click_3_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onRedo());\n    });\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(12, _c0))(\"matTooltip\", ctx_r9.undoRedoState.undoText || i0.ɵɵpipeBind1(2, 8, \"toolbar.undo\"))(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, !ctx_r9.undoRedoState.canUndo || ctx_r9.undoRedoState.isProcessing));\n    i0.ɵɵattribute(\"disabled\", !ctx_r9.undoRedoState.canUndo || ctx_r9.undoRedoState.isProcessing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(15, _c2))(\"matTooltip\", ctx_r9.undoRedoState.redoText || i0.ɵɵpipeBind1(4, 10, \"toolbar.redo\"))(\"ngClass\", i0.ɵɵpureFunction1(16, _c1, !ctx_r9.undoRedoState.canRedo || ctx_r9.undoRedoState.isProcessing));\n    i0.ɵɵattribute(\"disabled\", !ctx_r9.undoRedoState.canRedo || ctx_r9.undoRedoState.isProcessing);\n  }\n}\nconst _c3 = function () {\n  return [\"fas\", \"download\"];\n};\nfunction MainNavComponent_div_5_fa_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fa-icon\", 37);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r11 = i0.ɵɵreference(6);\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(6, _c3))(\"matTooltip\", i0.ɵɵpipeBind1(1, 4, \"toolbar.downloadDiagram\"))(\"matMenuTriggerFor\", _r11)(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx_r10.noOfDiagram == 0));\n  }\n}\nconst _c4 = function () {\n  return [\"fas\", \"trash\"];\n};\nfunction MainNavComponent_div_5_fa_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"fa-icon\", 38);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_fa_icon_28_Template_fa_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onDiagramClear());\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(5, _c4))(\"matTooltip\", i0.ɵɵpipeBind1(1, 3, \"toolbar.clearDiagram\"))(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, !ctx_r13.hasDeleteAccess || ctx_r13.noOfDiagram == 0));\n  }\n}\nfunction MainNavComponent_div_5_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.createNewProject());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"dashboard.btnText\"), \" \");\n  }\n}\nfunction MainNavComponent_div_5_img_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 40);\n    i0.ɵɵlistener(\"error\", function MainNavComponent_div_5_img_30_Template_img_error_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onImageError());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r17 = i0.ɵɵreference(33);\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r15.profilePicture, i0.ɵɵsanitizeUrl)(\"matMenuTriggerFor\", _r17);\n  }\n}\nconst _c5 = function () {\n  return [\"fas\", \"user-circle\"];\n};\nfunction MainNavComponent_div_5_fa_icon_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fa-icon\", 41);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r17 = i0.ɵɵreference(33);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(5, _c5))(\"matMenuTriggerFor\", _r17)(\"matTooltip\", i0.ɵɵpipeBind1(1, 3, \"toolbar.user\"));\n  }\n}\nconst _c6 = function (a0, a1) {\n  return {\n    code: a0,\n    lang: a1\n  };\n};\nfunction MainNavComponent_div_5_button_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_button_54_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r31);\n      const lang_r29 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.changeLanguage(lang_r29.code));\n    });\n    i0.ɵɵelement(1, \"img\", 42);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const lang_r29 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"src\", \"./assets/img/flags/\", lang_r29.code, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 2, lang_r29.name, i0.ɵɵpureFunction2(5, _c6, lang_r29.code, lang_r29.nameBis)));\n  }\n}\nconst _c7 = function () {\n  return [\"fas\", \"file-image\"];\n};\nconst _c8 = function () {\n  return [\"fas\", \"file-archive\"];\n};\nconst _c9 = function () {\n  return [\"fas\", \"file-pdf\"];\n};\nconst _c10 = function () {\n  return [\"fal\", \"globe\"];\n};\nconst _c11 = function () {\n  return [\"fal\", \"info-circle\"];\n};\nconst _c12 = function () {\n  return [\"fal\", \"power-off\"];\n};\nfunction MainNavComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, MainNavComponent_div_5_div_1_Template, 4, 3, \"div\", 10);\n    i0.ɵɵtemplate(2, MainNavComponent_div_5_p_2_Template, 4, 6, \"p\", 11);\n    i0.ɵɵtemplate(3, MainNavComponent_div_5_div_3_Template, 5, 18, \"div\", 12);\n    i0.ɵɵtemplate(4, MainNavComponent_div_5_fa_icon_4_Template, 2, 9, \"fa-icon\", 13);\n    i0.ɵɵelementStart(5, \"mat-menu\", 14, 15)(7, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.downloadCurrent());\n    });\n    i0.ɵɵelement(8, \"fa-icon\", 17);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 18)(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"mat-menu\", null, 19)(18, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.downloadAllDiagrams(true));\n    });\n    i0.ɵɵelement(19, \"fa-icon\", 17);\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.downloadAllDiagrams(false));\n    });\n    i0.ɵɵelement(24, \"fa-icon\", 17);\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, MainNavComponent_div_5_fa_icon_28_Template, 2, 8, \"fa-icon\", 20);\n    i0.ɵɵtemplate(29, MainNavComponent_div_5_button_29_Template, 3, 3, \"button\", 21);\n    i0.ɵɵtemplate(30, MainNavComponent_div_5_img_30_Template, 1, 2, \"img\", 22);\n    i0.ɵɵtemplate(31, MainNavComponent_div_5_fa_icon_31_Template, 2, 6, \"fa-icon\", 23);\n    i0.ɵɵelementStart(32, \"mat-menu\", 24, 25)(34, \"p\", 26);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 27);\n    i0.ɵɵelement(38, \"fa-icon\", 28);\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.openAboutModal());\n    });\n    i0.ɵɵelement(43, \"fa-icon\", 28);\n    i0.ɵɵelementStart(44, \"span\");\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function MainNavComponent_div_5_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onLogout());\n    });\n    i0.ɵɵelement(48, \"fa-icon\", 30);\n    i0.ɵɵelementStart(49, \"span\");\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"mat-menu\", null, 31);\n    i0.ɵɵtemplate(54, MainNavComponent_div_5_button_54_Template, 5, 8, \"button\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(17);\n    const _r18 = i0.ɵɵreference(53);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading && ctx_r2.loaderType === \"toolbar\" && ctx_r2.isEditorRoute());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading && ctx_r2.isEditorRoute() && ctx_r2.currentProject != null && !ctx_r2.showVersionHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditorRoute() && !ctx_r2.showVersionHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditorRoute() && !ctx_r2.showVersionHistory);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(44, _c7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 28, \"toolbar.download-current\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r12)(\"ngClass\", i0.ɵɵpureFunction1(45, _c1, ctx_r2.selectedVersion != null));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 30, \"toolbar.download-all\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(47, _c8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 32, \"toolbar.download-only-images\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(48, _c9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 34, \"toolbar.download-with-details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditorRoute());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditorRoute());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.profilePicture != null && ctx_r2.profilePicture != \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.profilePicture == null || ctx_r2.profilePicture == \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"overlapTrigger\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(36, 36, \"toolbar.hello\"), \" \", ctx_r2.userName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r18);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(49, _c10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(41, 38, \"toolbar.language\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(50, _c11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(46, 40, \"toolbar.about\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(51, _c12));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(51, 42, \"toolbar.logout\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.languages);\n  }\n}\nconst _c13 = function (a0) {\n  return {\n    \"panel-open\": a0\n  };\n};\nexport class MainNavComponent {\n  constructor(router, appService, accessService, diagramService, diagramUtils, userService, languageService, projectService, loaderService, navbarService, dialog, cdr, versionHistoryService, searchBarService) {\n    this.router = router;\n    this.appService = appService;\n    this.accessService = accessService;\n    this.diagramService = diagramService;\n    this.diagramUtils = diagramUtils;\n    this.userService = userService;\n    this.languageService = languageService;\n    this.projectService = projectService;\n    this.loaderService = loaderService;\n    this.navbarService = navbarService;\n    this.dialog = dialog;\n    this.cdr = cdr;\n    this.versionHistoryService = versionHistoryService;\n    this.searchBarService = searchBarService;\n    this.languages = [];\n    this.profilePicture = '';\n    this.title = 'UML Web Application';\n    this.currentDiagramName = '';\n    this.userName = '';\n    this.hasDeleteAccess = false;\n    this.isGridLayout = false;\n    this.isLoading = false;\n    this.loaderType = 'default';\n    this.noOfDiagram = 0;\n    this.destroy$ = new Subject();\n    this.lastModifiedDate = new Date();\n    this.showVersionHistory = false;\n    this.selectedVersion = null;\n    this.undoRedoState = {\n      canUndo: false,\n      canRedo: false,\n      undoText: '',\n      redoText: '',\n      isProcessing: false\n    };\n    effect(() => {\n      this.lastModifiedDate = this.appService.lastModifiedDate();\n      this.selectedVersion = this.versionHistoryService.selectedVersion();\n      this.showVersionHistory = this.navbarService.showVersionHistory();\n    });\n  }\n  ngOnInit() {\n    this.languageService.languages.subscribe(langs => {\n      this.languages = langs;\n    });\n    this.appService.getThemeClass();\n    // Subscribe to user changes\n    this.userService.userChanges().subscribe(user => {\n      if (user && Object.keys(user).length > 0) {\n        // Set profile picture if available\n        if (user.profilePicture) {\n          this.profilePicture = user.profilePicture;\n        }\n        // Set user name\n        if (user.name) {\n          this.userName = user.name.split(' ')[0];\n        }\n      }\n    });\n    // Initialize user data\n    this.userService.initializeUser();\n    this.accessService.accessTypeChanges().subscribe(accessType => {\n      if (accessType == AccessType.Viewer) {\n        this.hasDeleteAccess = false;\n      } else {\n        this.hasDeleteAccess = true;\n      }\n    });\n    this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) {\n        this.currentProject = project;\n        if (project.diagrams.length > 0) {\n          this.currentDiagramName = project.diagrams[0].name;\n          this.title = `${project.name} - ${this.currentDiagramName}`;\n        } else this.title = `${project.name}`;\n      }\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      if (diagrams) this.noOfDiagram = diagrams.length;\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) {\n        this.currentDiagramName = diagram.name;\n        this.title = `${this.currentProject?.name} - ${this.currentDiagramName}`;\n      } else {\n        this.title = `${this.currentProject?.name}`;\n      }\n    });\n    // Subscribe to loading state\n    this.loaderService.isLoading$.pipe(takeUntil(this.destroy$)).subscribe(status => {\n      // Use setTimeout to break change detection cycle\n      setTimeout(() => {\n        this.isLoading = status;\n        // Manually trigger change detection\n        this.cdr.detectChanges();\n      });\n    });\n    // Subscribe to loader type\n    this.loaderService.loaderType$.pipe(takeUntil(this.destroy$)).subscribe(type => {\n      // Use setTimeout to break change detection cycle\n      setTimeout(() => {\n        this.loaderType = type;\n        // Manually trigger change detection\n        this.cdr.detectChanges();\n      });\n    });\n  }\n  ngAfterViewInit() {\n    this.title = 'UML Web Application';\n    this.cdr.detectChanges();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  goBack() {\n    if (this.router.url.includes('/editor')) {\n      this.title = 'UML Web Application';\n      // Reset search term when navigating back to dashboard\n      this.searchBarService.resetSearch();\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  isEditorRoute() {\n    return this.router.url.includes('/editor');\n  }\n  openAboutModal() {\n    this.dialog.open(AboutModalComponent, {\n      width: '42%',\n      maxWidth: '70vw',\n      panelClass: 'about-modal'\n    });\n  }\n  /**\n   * Performs the logout operation.\n   * @memberof MainNavComponent\n   */\n  onLogout() {\n    if (this.router.url.includes('editor')) {\n      this.projectService.handleProjectUnlock(this.currentProject.id);\n    }\n    this.userService.logout();\n  }\n  onImageError() {\n    // Set to empty string to show the default user icon\n    this.profilePicture = '';\n  }\n  onDiagramClear() {\n    this.diagramService.triggerDelete();\n  }\n  downloadCurrent() {\n    this.diagramService.triggerCurrentDiagramDownload();\n  }\n  downloadAllDiagrams(isForOnlyImage) {\n    this.diagramService.triggerAllDiagramDownload(isForOnlyImage);\n  }\n  changeLanguage(langCode) {\n    this.languageService.changeLanguage(langCode);\n  }\n  shouldShowTooltip(name, length) {\n    return name.length > length;\n  }\n  createNewProject() {\n    this.projectService.openProjectDialog();\n  }\n  openVersionHistory() {\n    this.navbarService.setShowVersionHistory(true);\n  }\n  closeVersionHistory() {\n    // If you're using a service with signals as mentioned earlier\n    this.navbarService.setShowVersionHistory(false);\n  }\n  static #_ = this.ɵfac = function MainNavComponent_Factory(t) {\n    return new (t || MainNavComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.AccessService), i0.ɵɵdirectiveInject(i4.DiagramService), i0.ɵɵdirectiveInject(i5.DiagramUtils), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.LanguageService), i0.ɵɵdirectiveInject(i8.ProjectService), i0.ɵɵdirectiveInject(i9.LoaderService), i0.ɵɵdirectiveInject(i10.NavbarService), i0.ɵɵdirectiveInject(i11.MatDialog), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i12.VersionHistoryService), i0.ɵɵdirectiveInject(i13.SearchBarService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MainNavComponent,\n    selectors: [[\"app-main-nav\"]],\n    decls: 6,\n    vars: 6,\n    consts: [[1, \"main-content\"], [1, \"main-toolbar\", 3, \"ngClass\"], [1, \"header-section\"], [\"class\", \"version-history\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"header-section\", 4, \"ngIf\"], [1, \"version-history\"], [\"mat-icon-button\", \"\", 1, \"back-button\", 3, \"click\"], [\"src\", \"./assets/img/logo.png\", 1, \"toolbar-logo\", 3, \"click\"], [1, \"title\"], [\"color\", \"accent\", \"class\", \"saving-indicator\", 4, \"ngIf\"], [\"class\", \"modified-date\", 4, \"ngIf\"], [\"class\", \"undo-redo-controls\", 4, \"ngIf\"], [\"size\", \"xl\", \"class\", \"main-toolbar-icon\", \"matTooltipClass\", \"tooltip-custom\", 3, \"icon\", \"matTooltip\", \"matMenuTriggerFor\", \"ngClass\", 4, \"ngIf\"], [1, \"mat-menu\"], [\"downloadMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 1, \"menu-item\", 3, \"click\"], [3, \"icon\"], [\"mat-menu-item\", \"\", 1, \"menu-item\", 3, \"matMenuTriggerFor\", \"ngClass\"], [\"downloadSubMenu\", \"matMenu\"], [\"size\", \"xl\", \"class\", \"main-toolbar-icon\", \"matTooltipClass\", \"tooltip-custom\", 3, \"icon\", \"matTooltip\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"class\", \"new-project-btn\", 3, \"click\", 4, \"ngIf\"], [\"alt\", \"profile\", \"class\", \"rounded main-toolbar-icon\", 3, \"src\", \"matMenuTriggerFor\", \"error\", 4, \"ngIf\"], [\"size\", \"xl\", \"class\", \"main-toolbar-icon\", \"matTooltipClass\", \"tooltip-custom\", 3, \"icon\", \"matMenuTriggerFor\", \"matTooltip\", 4, \"ngIf\"], [1, \"main-toolbar-menu\", 3, \"overlapTrigger\"], [\"accountMenu\", \"matMenu\"], [1, \"user-name\"], [\"mat-menu-item\", \"\", 3, \"matMenuTriggerFor\"], [\"size\", \"lg\", 1, \"menu-icon\", 3, \"icon\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"size\", \"lg\", 1, \"menu-icon\", \"logout\", 3, \"icon\"], [\"languageMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"color\", \"accent\", 1, \"saving-indicator\"], [1, \"modified-date\"], [1, \"undo-redo-controls\"], [\"size\", \"lg\", \"matTooltipClass\", \"tooltip-custom\", 1, \"main-toolbar-icon\", \"undo-redo-icon\", 3, \"icon\", \"matTooltip\", \"ngClass\", \"click\"], [\"size\", \"xl\", \"matTooltipClass\", \"tooltip-custom\", 1, \"main-toolbar-icon\", 3, \"icon\", \"matTooltip\", \"matMenuTriggerFor\", \"ngClass\"], [\"size\", \"xl\", \"matTooltipClass\", \"tooltip-custom\", 1, \"main-toolbar-icon\", 3, \"icon\", \"matTooltip\", \"ngClass\", \"click\"], [\"mat-stroked-button\", \"\", 1, \"new-project-btn\", 3, \"click\"], [\"alt\", \"profile\", 1, \"rounded\", \"main-toolbar-icon\", 3, \"src\", \"matMenuTriggerFor\", \"error\"], [\"size\", \"xl\", \"matTooltipClass\", \"tooltip-custom\", 1, \"main-toolbar-icon\", 3, \"icon\", \"matMenuTriggerFor\", \"matTooltip\"], [1, \"menu-flag\", 3, \"src\"]],\n    template: function MainNavComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2);\n        i0.ɵɵtemplate(3, MainNavComponent_div_3_Template, 6, 1, \"div\", 3);\n        i0.ɵɵtemplate(4, MainNavComponent_ng_container_4_Template, 4, 1, \"ng-container\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, MainNavComponent_div_5_Template, 55, 52, \"div\", 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c13, ctx.showVersionHistory));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showVersionHistory);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showVersionHistory);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showVersionHistory);\n      }\n    },\n    dependencies: [i14.NgClass, i14.NgForOf, i14.NgIf, i15.FaIconComponent, i16.MatButton, i16.MatIconButton, i17.MatTooltip, i18.MatIcon, i19.MatToolbar, i20.MatMenu, i20.MatMenuItem, i20.MatMenuTrigger, i21.TranslatePipe, i22.TimeAgoPipe],\n    styles: [\".main-content[_ngcontent-%COMP%] {\\n  position: fixed;\\n  width: 100vw;\\n  z-index: 1000;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  color: white;\\n  margin-left: 1rem;\\n  font-weight: 500;\\n  letter-spacing: 1px;\\n}\\n\\n.tooltip-custom[_ngcontent-%COMP%] {\\n  background-color: #333;\\n  color: #fff;\\n}\\n\\n.tooltip-custom[_ngcontent-%COMP%]   .mat-tooltip[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.main-toolbar[_ngcontent-%COMP%] {\\n  height: var(--toolbar-height);\\n  transition: background 400ms cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.main-toolbar.panel-open[_ngcontent-%COMP%] {\\n  background-color: #eeebeb;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .version-history[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 1rem;\\n  cursor: pointer;\\n  color: black;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .saving-indicator[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .modified-date[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  font-size: 0.8rem;\\n  padding-right: 0.2rem;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .release-version[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  font-size: 0.8rem;\\n  padding-right: 0.2rem;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-right: 8px;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .toolbar-logo[_ngcontent-%COMP%] {\\n  max-height: 32px;\\n  cursor: pointer;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   fa-icon[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  cursor: pointer;\\n  transition: color 0.3s ease;\\n  color: aliceblue;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   fa-icon[_ngcontent-%COMP%]:hover {\\n  filter: drop-shadow(0 0 10px white);\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .main-toolbar-icon[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin-left: 24px;\\n  color: \\\"#fffff\\\";\\n  cursor: pointer;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%] {\\n  width: 1.5rem;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .toolbar-button[_ngcontent-%COMP%] {\\n  transition: color 0.3s ease, background-color 0.3s ease;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%], .main-toolbar[_ngcontent-%COMP%]   fa-icon[_ngcontent-%COMP%] {\\n  transition: color 0.3s ease;\\n}\\n.main-toolbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .main-toolbar[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  transition: color 0.3s ease, background-color 0.3s ease;\\n}\\n\\n.menu-icon[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n\\n.menu-flag[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  vertical-align: middle;\\n  height: 24px;\\n}\\n\\n.logout[_ngcontent-%COMP%] {\\n  color: rgb(216, 0, 0);\\n}\\n\\n.rounded[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  height: 23px;\\n  width: auto;\\n  transition: 300ms;\\n}\\n\\n.rounded[_ngcontent-%COMP%]:hover {\\n  height: 28px;\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.menu-item[_ngcontent-%COMP%]   .ng-fa-icon[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n\\n.btn-dash[_ngcontent-%COMP%] {\\n  padding: 0.8rem;\\n  font-size: medium;\\n  font-family: Roboto, \\\"Helvetica Neue\\\", sans-serif;\\n  border-radius: 10px;\\n  background-color: transparent;\\n  cursor: pointer;\\n}\\n\\n.new-project-btn[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-family: Roboto, \\\"Helvetica Neue\\\", sans-serif;\\n  background: transparent;\\n  height: calc(var(--toolbar-height) - 6px);\\n  border: 1px solid white !important;\\n  color: #fff;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["effect", "Subject", "takeUntil", "AccessType", "AboutModalComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "MainNavComponent_div_3_Template_button_click_1_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "closeVersionHistory", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "selectedVersion", "name", "ɵɵelementContainerStart", "MainNavComponent_ng_container_4_Template_img_click_1_listener", "_r6", "ctx_r5", "goBack", "ɵɵelementContainerEnd", "ɵɵtextInterpolate1", "ctx_r1", "title", "ɵɵpipeBind1", "ɵɵtextInterpolate2", "ctx_r8", "lastModifiedDate", "MainNavComponent_div_5_div_3_Template_fa_icon_click_1_listener", "_r21", "ctx_r20", "onUndo", "MainNavComponent_div_5_div_3_Template_fa_icon_click_3_listener", "ctx_r22", "onRedo", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ctx_r9", "undoRedoState", "undoText", "ɵɵpureFunction1", "_c1", "canUndo", "isProcessing", "ɵɵattribute", "_c2", "redoText", "canRedo", "ɵɵelement", "_c3", "_r11", "ctx_r10", "noOfDiagram", "MainNavComponent_div_5_fa_icon_28_Template_fa_icon_click_0_listener", "_r24", "ctx_r23", "onDiagramClear", "_c4", "ctx_r13", "hasDeleteAccess", "MainNavComponent_div_5_button_29_Template_button_click_0_listener", "_r26", "ctx_r25", "createNewProject", "MainNavComponent_div_5_img_30_Template_img_error_0_listener", "_r28", "ctx_r27", "onImageError", "ctx_r15", "profilePicture", "ɵɵsanitizeUrl", "_r17", "_c5", "MainNavComponent_div_5_button_54_Template_button_click_0_listener", "restoredCtx", "_r31", "lang_r29", "$implicit", "ctx_r30", "changeLanguage", "code", "ɵɵpropertyInterpolate1", "ɵɵpipeBind2", "ɵɵpureFunction2", "_c6", "nameBis", "ɵɵtemplate", "MainNavComponent_div_5_div_1_Template", "MainNavComponent_div_5_p_2_Template", "MainNavComponent_div_5_div_3_Template", "MainNavComponent_div_5_fa_icon_4_Template", "MainNavComponent_div_5_Template_button_click_7_listener", "_r33", "ctx_r32", "downloadCurrent", "MainNavComponent_div_5_Template_button_click_18_listener", "ctx_r34", "downloadAllDiagrams", "MainNavComponent_div_5_Template_button_click_23_listener", "ctx_r35", "MainNavComponent_div_5_fa_icon_28_Template", "MainNavComponent_div_5_button_29_Template", "MainNavComponent_div_5_img_30_Template", "MainNavComponent_div_5_fa_icon_31_Template", "MainNavComponent_div_5_Template_button_click_42_listener", "ctx_r36", "openAboutModal", "MainNavComponent_div_5_Template_button_click_47_listener", "ctx_r37", "onLogout", "MainNavComponent_div_5_button_54_Template", "ctx_r2", "isLoading", "loaderType", "isEditorRoute", "currentProject", "showVersionHistory", "_c7", "_r12", "_c8", "_c9", "userName", "_r18", "_c10", "_c11", "_c12", "languages", "MainNavComponent", "constructor", "router", "appService", "accessService", "diagramService", "diagramUtils", "userService", "languageService", "projectService", "loaderService", "navbarService", "dialog", "cdr", "versionHistoryService", "searchBarService", "currentDiagramName", "isGridLayout", "destroy$", "Date", "ngOnInit", "subscribe", "langs", "getThemeClass", "userChanges", "user", "Object", "keys", "length", "split", "initializeUser", "accessTypeChanges", "accessType", "Viewer", "currentProjectChanges", "project", "diagrams", "currentProjectDiagramsChanges", "activeDiagramChanges", "diagram", "isLoading$", "pipe", "status", "setTimeout", "detectChanges", "loaderType$", "type", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "url", "includes", "resetSearch", "navigate", "open", "width", "max<PERSON><PERSON><PERSON>", "panelClass", "handleProjectUnlock", "id", "logout", "triggerDelete", "triggerCurrentDiagramDownload", "isForOnlyImage", "triggerAllDiagramDownload", "langCode", "shouldShowTooltip", "openProjectDialog", "openVersionHistory", "setShowVersionHistory", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "AppService", "i3", "AccessService", "i4", "DiagramService", "i5", "DiagramUtils", "i6", "UserService", "i7", "LanguageService", "i8", "ProjectService", "i9", "LoaderService", "i10", "NavbarService", "i11", "MatDialog", "ChangeDetectorRef", "i12", "VersionHistoryService", "i13", "SearchBarService", "_2", "selectors", "decls", "vars", "consts", "template", "MainNavComponent_Template", "rf", "ctx", "MainNavComponent_div_3_Template", "MainNavComponent_ng_container_4_Template", "MainNavComponent_div_5_Template", "_c13"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\main-nav\\main-nav.component.ts", "D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\main-nav\\main-nav.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  effect,\r\n  OnD<PERSON>roy,\r\n  OnInit,\r\n} from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { Lang } from 'src/app/shared/model/common';\r\nimport { AccessType, ProjectDetails } from 'src/app/shared/model/project';\r\nimport { VersionHistory } from 'src/app/shared/model/version-history';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../../services/access/access.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { DiagramService } from '../../services/diagram/diagram.service';\r\nimport { LanguageService } from '../../services/language/language.service';\r\nimport {\r\n  LoaderService,\r\n  LoaderType,\r\n} from '../../services/loader/loader.service';\r\nimport { NavbarService } from '../../services/navbar/navbar.service';\r\nimport { ProjectService } from '../../services/project/project.service';\r\nimport { SearchBarService } from '../../services/search-bar/search-bar.service';\r\nimport { UndoRedoState } from '../../services/undo-redo/undo-redo.service';\r\nimport { UserService } from '../../services/user/user.service';\r\nimport { VersionHistoryService } from '../../services/versionHistory/version-history.service';\r\nimport { AboutModalComponent } from '../about-modal/about-modal.component';\r\n\r\n@Component({\r\n  selector: 'app-main-nav',\r\n  templateUrl: './main-nav.component.html',\r\n  styleUrls: ['./main-nav.component.scss'],\r\n})\r\nexport class MainNavComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  public languages: Lang[] = [];\r\n  public profilePicture: string = '';\r\n  public title: string = 'UML Web Application';\r\n  currentDiagramName: string = '';\r\n  currentProject!: ProjectDetails;\r\n  public userName: string = '';\r\n  hasDeleteAccess: boolean = false;\r\n  isGridLayout: boolean = false;\r\n  isLoading = false;\r\n  loaderType: LoaderType = 'default';\r\n  noOfDiagram: number = 0;\r\n  private destroy$ = new Subject<void>();\r\n  lastModifiedDate: Date | undefined = new Date();\r\n  showVersionHistory: boolean = false;\r\n  selectedVersion: VersionHistory | null = null;\r\n  undoRedoState: UndoRedoState = {\r\n    canUndo: false,\r\n    canRedo: false,\r\n    undoText: '',\r\n    redoText: '',\r\n    isProcessing: false,\r\n  };\r\n  constructor(\r\n    public router: Router,\r\n    private appService: AppService,\r\n    private accessService: AccessService,\r\n    private diagramService: DiagramService,\r\n    private diagramUtils: DiagramUtils,\r\n    private userService: UserService,\r\n    private languageService: LanguageService,\r\n    private projectService: ProjectService,\r\n    public loaderService: LoaderService,\r\n    private navbarService: NavbarService,\r\n    private dialog: MatDialog,\r\n    private cdr: ChangeDetectorRef,\r\n    private versionHistoryService: VersionHistoryService,\r\n    private searchBarService: SearchBarService\r\n  ) {\r\n    effect(() => {\r\n      this.lastModifiedDate = this.appService.lastModifiedDate();\r\n      this.selectedVersion = this.versionHistoryService.selectedVersion();\r\n      this.showVersionHistory = this.navbarService.showVersionHistory();\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.languageService.languages.subscribe((langs: Lang[]) => {\r\n      this.languages = langs;\r\n    });\r\n    this.appService.getThemeClass();\r\n\r\n    // Subscribe to user changes\r\n    this.userService.userChanges().subscribe((user) => {\r\n      if (user && Object.keys(user).length > 0) {\r\n        // Set profile picture if available\r\n        if (user.profilePicture) {\r\n          this.profilePicture = user.profilePicture;\r\n        }\r\n        // Set user name\r\n        if (user.name) {\r\n          this.userName = user.name.split(' ')[0];\r\n        }\r\n      }\r\n    });\r\n\r\n    // Initialize user data\r\n    this.userService.initializeUser();\r\n\r\n    this.accessService.accessTypeChanges().subscribe((accessType) => {\r\n      if (accessType == AccessType.Viewer) {\r\n        this.hasDeleteAccess = false;\r\n      } else {\r\n        this.hasDeleteAccess = true;\r\n      }\r\n    });\r\n\r\n    this.projectService.currentProjectChanges().subscribe((project) => {\r\n      if (project) {\r\n        this.currentProject = project;\r\n        if (project.diagrams.length > 0) {\r\n          this.currentDiagramName = project.diagrams[0].name;\r\n          this.title = `${project.name} - ${this.currentDiagramName}`;\r\n        } else this.title = `${project.name}`;\r\n      }\r\n    });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      if (diagrams) this.noOfDiagram = diagrams.length;\r\n    });\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) {\r\n        this.currentDiagramName = diagram.name;\r\n        this.title = `${this.currentProject?.name} - ${this.currentDiagramName}`;\r\n      } else {\r\n        this.title = `${this.currentProject?.name}`;\r\n      }\r\n    });\r\n\r\n    // Subscribe to loading state\r\n    this.loaderService.isLoading$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((status) => {\r\n        // Use setTimeout to break change detection cycle\r\n        setTimeout(() => {\r\n          this.isLoading = status;\r\n          // Manually trigger change detection\r\n          this.cdr.detectChanges();\r\n        });\r\n      });\r\n\r\n    // Subscribe to loader type\r\n    this.loaderService.loaderType$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((type) => {\r\n        // Use setTimeout to break change detection cycle\r\n        setTimeout(() => {\r\n          this.loaderType = type;\r\n          // Manually trigger change detection\r\n          this.cdr.detectChanges();\r\n        });\r\n      });\r\n  }\r\n  ngAfterViewInit(): void {\r\n    this.title = 'UML Web Application';\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  goBack() {\r\n    if (this.router.url.includes('/editor')) {\r\n      this.title = 'UML Web Application';\r\n      // Reset search term when navigating back to dashboard\r\n      this.searchBarService.resetSearch();\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  isEditorRoute(): boolean {\r\n    return this.router.url.includes('/editor');\r\n  }\r\n\r\n  openAboutModal(): void {\r\n    this.dialog.open(AboutModalComponent, {\r\n      width: '42%',\r\n      maxWidth: '70vw',\r\n      panelClass: 'about-modal',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Performs the logout operation.\r\n   * @memberof MainNavComponent\r\n   */\r\n  onLogout() {\r\n    if (this.router.url.includes('editor')) {\r\n      this.projectService.handleProjectUnlock(this.currentProject.id!);\r\n    }\r\n    this.userService.logout();\r\n  }\r\n\r\n  onImageError() {\r\n    // Set to empty string to show the default user icon\r\n    this.profilePicture = '';\r\n  }\r\n\r\n  onDiagramClear() {\r\n    this.diagramService.triggerDelete();\r\n  }\r\n  downloadCurrent() {\r\n    this.diagramService.triggerCurrentDiagramDownload();\r\n  }\r\n  downloadAllDiagrams(isForOnlyImage: boolean) {\r\n    this.diagramService.triggerAllDiagramDownload(isForOnlyImage);\r\n  }\r\n  changeLanguage(langCode: string) {\r\n    this.languageService.changeLanguage(langCode);\r\n  }\r\n  shouldShowTooltip(name: string, length: number): boolean {\r\n    return name.length > length;\r\n  }\r\n  createNewProject() {\r\n    this.projectService.openProjectDialog();\r\n  }\r\n\r\n  openVersionHistory() {\r\n    this.navbarService.setShowVersionHistory(true);\r\n  }\r\n\r\n  closeVersionHistory(): void {\r\n    // If you're using a service with signals as mentioned earlier\r\n    this.navbarService.setShowVersionHistory(false);\r\n  }\r\n}\r\n", "<div class=\"main-content\">\r\n  <mat-toolbar\r\n    class=\"main-toolbar\"\r\n    [ngClass]=\"{ 'panel-open': showVersionHistory }\"\r\n  >\r\n    <div class=\"header-section\">\r\n      <!-- Back button shown only when version history panel is open -->\r\n      <div class=\"version-history\" *ngIf=\"showVersionHistory\">\r\n        <button\r\n          mat-icon-button\r\n          class=\"back-button\"\r\n          (click)=\"closeVersionHistory()\"\r\n        >\r\n          <mat-icon>arrow_back</mat-icon>\r\n        </button>\r\n\r\n        <p>{{ selectedVersion?.name }}</p>\r\n      </div>\r\n\r\n      <!-- Original logo and title (visible only when panel is closed) -->\r\n      <ng-container *ngIf=\"!showVersionHistory\">\r\n        <img\r\n          class=\"toolbar-logo\"\r\n          src=\"./assets/img/logo.png\"\r\n          (click)=\"goBack()\"\r\n        />\r\n        <h3 class=\"title\">\r\n          {{ title }}\r\n        </h3>\r\n      </ng-container>\r\n    </div>\r\n\r\n    <!-- Right side of toolbar (hidden when panel is open) -->\r\n    <div class=\"header-section\" *ngIf=\"!showVersionHistory\">\r\n      <div\r\n        *ngIf=\"isLoading && loaderType === 'toolbar' && isEditorRoute()\"\r\n        color=\"accent\"\r\n        class=\"saving-indicator\"\r\n      >\r\n        <span>{{ \"toolbar.savingMsg\" | translate }}</span>\r\n      </div>\r\n      <p\r\n        *ngIf=\"\r\n          !isLoading &&\r\n          isEditorRoute() &&\r\n          currentProject != null &&\r\n          !showVersionHistory\r\n        \"\r\n        class=\"modified-date\"\r\n      >\r\n        {{ \"toolbar.lastModified\" | translate }} :\r\n        {{ lastModifiedDate | timeAgo }}\r\n      </p>\r\n\r\n      <!-- Undo/Redo Toolbar -->\r\n      <div\r\n        class=\"undo-redo-controls\"\r\n        *ngIf=\"isEditorRoute() && !showVersionHistory\"\r\n      >\r\n        <fa-icon\r\n          [icon]=\"['fas', 'undo']\"\r\n          size=\"lg\"\r\n          class=\"main-toolbar-icon undo-redo-icon\"\r\n          [matTooltip]=\"undoRedoState.undoText || ('toolbar.undo' | translate)\"\r\n          matTooltipClass=\"tooltip-custom\"\r\n          (click)=\"onUndo()\"\r\n          [ngClass]=\"{\r\n            'disabled-icon':\r\n              !undoRedoState.canUndo || undoRedoState.isProcessing\r\n          }\"\r\n          [attr.disabled]=\"!undoRedoState.canUndo || undoRedoState.isProcessing\"\r\n        ></fa-icon>\r\n        <fa-icon\r\n          [icon]=\"['fas', 'redo']\"\r\n          size=\"lg\"\r\n          class=\"main-toolbar-icon undo-redo-icon\"\r\n          [matTooltip]=\"undoRedoState.redoText || ('toolbar.redo' | translate)\"\r\n          matTooltipClass=\"tooltip-custom\"\r\n          (click)=\"onRedo()\"\r\n          [ngClass]=\"{\r\n            'disabled-icon':\r\n              !undoRedoState.canRedo || undoRedoState.isProcessing\r\n          }\"\r\n          [attr.disabled]=\"!undoRedoState.canRedo || undoRedoState.isProcessing\"\r\n        ></fa-icon>\r\n      </div>\r\n\r\n      <fa-icon\r\n        [icon]=\"['fas', 'download']\"\r\n        size=\"xl\"\r\n        class=\"main-toolbar-icon\"\r\n        [matTooltip]=\"'toolbar.downloadDiagram' | translate\"\r\n        [matMenuTriggerFor]=\"downloadMenu\"\r\n        matTooltipClass=\"tooltip-custom\"\r\n        *ngIf=\"isEditorRoute() && !showVersionHistory\"\r\n        [ngClass]=\"{ 'disabled-icon': noOfDiagram == 0 }\"\r\n      >\r\n      </fa-icon>\r\n      <!-- <fa-icon\r\n        [icon]=\"['fas', 'history']\"\r\n        size=\"xl\"\r\n        class=\"main-toolbar-icon\"\r\n        [matTooltip]=\"'toolbar.versionHistory' | translate\"\r\n        matTooltipClass=\"tooltip-custom\"\r\n        *ngIf=\"isEditorRoute()\"\r\n        (click)=\"openVersionHistory()\"\r\n        [ngClass]=\"{ 'disabled-icon': !hasDeleteAccess || noOfDiagram == 0 }\"\r\n      >\r\n      </fa-icon> -->\r\n      <mat-menu #downloadMenu=\"matMenu\" class=\"mat-menu\">\r\n        <button mat-menu-item class=\"menu-item\" (click)=\"downloadCurrent()\">\r\n          <fa-icon [icon]=\"['fas', 'file-image']\"></fa-icon>\r\n          <span>{{ \"toolbar.download-current\" | translate }}</span>\r\n        </button>\r\n        <button\r\n          mat-menu-item\r\n          [matMenuTriggerFor]=\"downloadSubMenu\"\r\n          class=\"menu-item\"\r\n          [ngClass]=\"{ 'disabled-icon': selectedVersion != null }\"\r\n        >\r\n          <span>{{ \"toolbar.download-all\" | translate }}</span>\r\n        </button>\r\n      </mat-menu>\r\n      <mat-menu #downloadSubMenu=\"matMenu\">\r\n        <button\r\n          mat-menu-item\r\n          class=\"menu-item\"\r\n          (click)=\"downloadAllDiagrams(true)\"\r\n        >\r\n          <fa-icon [icon]=\"['fas', 'file-archive']\"></fa-icon>\r\n          <span>{{ \"toolbar.download-only-images\" | translate }}</span>\r\n        </button>\r\n        <button\r\n          mat-menu-item\r\n          class=\"menu-item\"\r\n          (click)=\"downloadAllDiagrams(false)\"\r\n        >\r\n          <fa-icon [icon]=\"['fas', 'file-pdf']\"></fa-icon>\r\n          <span>{{ \"toolbar.download-with-details\" | translate }}</span>\r\n        </button>\r\n      </mat-menu>\r\n      <fa-icon\r\n        [icon]=\"['fas', 'trash']\"\r\n        size=\"xl\"\r\n        class=\"main-toolbar-icon\"\r\n        [matTooltip]=\"'toolbar.clearDiagram' | translate\"\r\n        matTooltipClass=\"tooltip-custom\"\r\n        (click)=\"onDiagramClear()\"\r\n        *ngIf=\"isEditorRoute()\"\r\n        [ngClass]=\"{ 'disabled-icon': !hasDeleteAccess || noOfDiagram == 0 }\"\r\n      >\r\n      </fa-icon>\r\n      <button\r\n        mat-stroked-button\r\n        class=\"new-project-btn\"\r\n        (click)=\"createNewProject()\"\r\n        *ngIf=\"!isEditorRoute()\"\r\n      >\r\n        {{ \"dashboard.btnText\" | translate }}\r\n      </button>\r\n      <img\r\n        [src]=\"profilePicture\"\r\n        alt=\"profile\"\r\n        class=\"rounded main-toolbar-icon\"\r\n        [matMenuTriggerFor]=\"accountMenu\"\r\n        *ngIf=\"profilePicture != null && profilePicture != ''\"\r\n        (error)=\"onImageError()\"\r\n      />\r\n      <fa-icon\r\n        *ngIf=\"profilePicture == null || profilePicture == ''\"\r\n        [icon]=\"['fas', 'user-circle']\"\r\n        size=\"xl\"\r\n        class=\"main-toolbar-icon\"\r\n        [matMenuTriggerFor]=\"accountMenu\"\r\n        [matTooltip]=\"'toolbar.user' | translate\"\r\n        matTooltipClass=\"tooltip-custom\"\r\n      ></fa-icon>\r\n      <mat-menu\r\n        #accountMenu=\"matMenu\"\r\n        class=\"main-toolbar-menu\"\r\n        [overlapTrigger]=\"false\"\r\n      >\r\n        <p class=\"user-name\">\r\n          {{ \"toolbar.hello\" | translate }} {{ userName }}\r\n        </p>\r\n        <button mat-menu-item [matMenuTriggerFor]=\"languageMenu\">\r\n          <fa-icon\r\n            [icon]=\"['fal', 'globe']\"\r\n            size=\"lg\"\r\n            class=\"menu-icon\"\r\n          ></fa-icon>\r\n          <span>{{ \"toolbar.language\" | translate }}</span>\r\n        </button>\r\n        <button mat-menu-item (click)=\"openAboutModal()\">\r\n          <fa-icon\r\n            [icon]=\"['fal', 'info-circle']\"\r\n            size=\"lg\"\r\n            class=\"menu-icon\"\r\n          ></fa-icon>\r\n          <span>{{ \"toolbar.about\" | translate }}</span>\r\n        </button>\r\n        <button mat-menu-item (click)=\"onLogout()\">\r\n          <fa-icon\r\n            [icon]=\"['fal', 'power-off']\"\r\n            size=\"lg\"\r\n            class=\"menu-icon logout\"\r\n          ></fa-icon>\r\n          <span>{{ \"toolbar.logout\" | translate }}</span>\r\n        </button>\r\n      </mat-menu>\r\n      <mat-menu #languageMenu=\"matMenu\">\r\n        <button\r\n          *ngFor=\"let lang of languages\"\r\n          mat-menu-item\r\n          (click)=\"changeLanguage(lang.code)\"\r\n        >\r\n          <img class=\"menu-flag\" src=\"./assets/img/flags/{{ lang.code }}.svg\" />\r\n          <span>{{\r\n            lang.name | translate : { code: lang.code, lang: lang.nameBis }\r\n          }}</span>\r\n        </button>\r\n      </mat-menu>\r\n    </div>\r\n  </mat-toolbar>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,MAAM,QAGD,eAAe;AAGtB,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAEzC,SAASC,UAAU,QAAwB,8BAA8B;AAiBzE,SAASC,mBAAmB,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtBpEC,EAAA,CAAAC,cAAA,aAAwD;IAIpDD,EAAA,CAAAE,UAAA,mBAAAC,wDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,mBAAA,EAAqB;IAAA,EAAC;IAE/BT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAGjCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;IAA/BX,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAa,iBAAA,CAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,IAAA,CAA2B;;;;;;IAIhChB,EAAA,CAAAiB,uBAAA,GAA0C;IACxCjB,EAAA,CAAAC,cAAA,aAIE;IADAD,EAAA,CAAAE,UAAA,mBAAAgB,8DAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAHpBrB,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAC,cAAA,YAAkB;IAChBD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACPX,EAAA,CAAAsB,qBAAA,EAAe;;;;IAFXtB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMFzB,EAAA,CAAAC,cAAA,cAIC;IACOD,EAAA,CAAAU,MAAA,GAAqC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;IAA5CX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,4BAAqC;;;;;IAE7C1B,EAAA,CAAAC,cAAA,YAQC;IACCD,EAAA,CAAAU,MAAA,GAEF;;;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;IAFFX,EAAA,CAAAY,SAAA,GAEF;IAFEZ,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA0B,WAAA,uCAAA1B,EAAA,CAAA0B,WAAA,OAAAE,MAAA,CAAAC,gBAAA,OAEF;;;;;;;;;;;;;;;;;IAGA7B,EAAA,CAAAC,cAAA,cAGC;IAOGD,EAAA,CAAAE,UAAA,mBAAA4B,+DAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;;IAMnBjC,EAAA,CAAAW,YAAA,EAAU;IACXX,EAAA,CAAAC,cAAA,kBAYC;IANCD,EAAA,CAAAE,UAAA,mBAAAgC,+DAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;;IAMnBpC,EAAA,CAAAW,YAAA,EAAU;;;;IAxBTX,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAC,GAAA,EAAwB,eAAAC,MAAA,CAAAC,aAAA,CAAAC,QAAA,IAAA1C,EAAA,CAAA0B,WAAA,mCAAA1B,EAAA,CAAA2C,eAAA,KAAAC,GAAA,GAAAJ,MAAA,CAAAC,aAAA,CAAAI,OAAA,IAAAL,MAAA,CAAAC,aAAA,CAAAK,YAAA;IAUxB9C,EAAA,CAAA+C,WAAA,cAAAP,MAAA,CAAAC,aAAA,CAAAI,OAAA,IAAAL,MAAA,CAAAC,aAAA,CAAAK,YAAA,CAAsE;IAGtE9C,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAU,GAAA,EAAwB,eAAAR,MAAA,CAAAC,aAAA,CAAAQ,QAAA,IAAAjD,EAAA,CAAA0B,WAAA,oCAAA1B,EAAA,CAAA2C,eAAA,KAAAC,GAAA,GAAAJ,MAAA,CAAAC,aAAA,CAAAS,OAAA,IAAAV,MAAA,CAAAC,aAAA,CAAAK,YAAA;IAUxB9C,EAAA,CAAA+C,WAAA,cAAAP,MAAA,CAAAC,aAAA,CAAAS,OAAA,IAAAV,MAAA,CAAAC,aAAA,CAAAK,YAAA,CAAsE;;;;;;;;IAI1E9C,EAAA,CAAAmD,SAAA,kBAUU;;;;;;;IATRnD,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,IAAAc,GAAA,EAA4B,eAAApD,EAAA,CAAA0B,WAAA,wDAAA2B,IAAA,aAAArD,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAAAU,OAAA,CAAAC,WAAA;;;;;;;;;IAqD9BvD,EAAA,CAAAC,cAAA,kBASC;IAHCD,EAAA,CAAAE,UAAA,mBAAAsD,oEAAA;MAAAxD,EAAA,CAAAI,aAAA,CAAAqD,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkD,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;;IAI5B3D,EAAA,CAAAW,YAAA,EAAU;;;;IATRX,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,IAAAsB,GAAA,EAAyB,eAAA5D,EAAA,CAAA0B,WAAA,2CAAA1B,EAAA,CAAA2C,eAAA,IAAAC,GAAA,GAAAiB,OAAA,CAAAC,eAAA,IAAAD,OAAA,CAAAN,WAAA;;;;;;IAU3BvD,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6D,kEAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAyD,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAG5BlE,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;IADPX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAA0B,WAAA,iCACF;;;;;;IACA1B,EAAA,CAAAC,cAAA,cAOE;IADAD,EAAA,CAAAE,UAAA,mBAAAiE,4DAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAAgE,IAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6D,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAN1BtE,EAAA,CAAAW,YAAA,EAOE;;;;;;IANAX,EAAA,CAAAqC,UAAA,QAAAkC,OAAA,CAAAC,cAAA,EAAAxE,EAAA,CAAAyE,aAAA,CAAsB,sBAAAC,IAAA;;;;;;;;IAOxB1E,EAAA,CAAAmD,SAAA,kBAQW;;;;;;IANTnD,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,IAAAqC,GAAA,EAA+B,sBAAAD,IAAA,gBAAA1E,EAAA,CAAA0B,WAAA;;;;;;;;;;;;IAyC/B1B,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA0E,kEAAA;MAAA,MAAAC,WAAA,GAAA7E,EAAA,CAAAI,aAAA,CAAA0E,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAjF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAyE,OAAA,CAAAC,cAAA,CAAAH,QAAA,CAAAI,IAAA,CAAyB;IAAA,EAAC;IAEnCnF,EAAA,CAAAmD,SAAA,cAAsE;IACtEnD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAEJ;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAHcX,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAoF,sBAAA,+BAAAL,QAAA,CAAAI,IAAA,UAAAnF,EAAA,CAAAyE,aAAA,CAA4C;IAC7DzE,EAAA,CAAAY,SAAA,GAEJ;IAFIZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAqF,WAAA,OAAAN,QAAA,CAAA/D,IAAA,EAAAhB,EAAA,CAAAsF,eAAA,IAAAC,GAAA,EAAAR,QAAA,CAAAI,IAAA,EAAAJ,QAAA,CAAAS,OAAA,GAEJ;;;;;;;;;;;;;;;;;;;;;;;;IA1LRxF,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAyF,UAAA,IAAAC,qCAAA,kBAMM;IACN1F,EAAA,CAAAyF,UAAA,IAAAE,mCAAA,gBAWI;IAGJ3F,EAAA,CAAAyF,UAAA,IAAAG,qCAAA,mBA8BM;IAEN5F,EAAA,CAAAyF,UAAA,IAAAI,yCAAA,sBAUU;IAYV7F,EAAA,CAAAC,cAAA,uBAAmD;IACTD,EAAA,CAAAE,UAAA,mBAAA4F,wDAAA;MAAA9F,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAC,OAAA,GAAAhG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwF,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IACjEjG,EAAA,CAAAmD,SAAA,kBAAkD;IAClDnD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,IAA4C;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3DX,EAAA,CAAAC,cAAA,kBAKC;IACOD,EAAA,CAAAU,MAAA,IAAwC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGzDX,EAAA,CAAAC,cAAA,0BAAqC;IAIjCD,EAAA,CAAAE,UAAA,mBAAAgG,yDAAA;MAAAlG,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAI,OAAA,GAAAnG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2F,OAAA,CAAAC,mBAAA,CAAoB,IAAI,CAAC;IAAA,EAAC;IAEnCpG,EAAA,CAAAmD,SAAA,mBAAoD;IACpDnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAgD;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE/DX,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAmG,yDAAA;MAAArG,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAO,OAAA,GAAAtG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8F,OAAA,CAAAF,mBAAA,CAAoB,KAAK,CAAC;IAAA,EAAC;IAEpCpG,EAAA,CAAAmD,SAAA,mBAAgD;IAChDnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiD;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGlEX,EAAA,CAAAyF,UAAA,KAAAc,0CAAA,sBAUU;IACVvG,EAAA,CAAAyF,UAAA,KAAAe,yCAAA,qBAOS;IACTxG,EAAA,CAAAyF,UAAA,KAAAgB,sCAAA,kBAOE;IACFzG,EAAA,CAAAyF,UAAA,KAAAiB,0CAAA,sBAQW;IACX1G,EAAA,CAAAC,cAAA,wBAIC;IAEGD,EAAA,CAAAU,MAAA,IACF;;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAAC,cAAA,kBAAyD;IACvDD,EAAA,CAAAmD,SAAA,mBAIW;IACXnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAoC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEnDX,EAAA,CAAAC,cAAA,kBAAiD;IAA3BD,EAAA,CAAAE,UAAA,mBAAAyG,yDAAA;MAAA3G,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAa,OAAA,GAAA5G,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoG,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAC9C7G,EAAA,CAAAmD,SAAA,mBAIW;IACXnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEhDX,EAAA,CAAAC,cAAA,kBAA2C;IAArBD,EAAA,CAAAE,UAAA,mBAAA4G,yDAAA;MAAA9G,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAgB,OAAA,GAAA/G,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAuG,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACxChH,EAAA,CAAAmD,SAAA,mBAIW;IACXnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAkC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGnDX,EAAA,CAAAC,cAAA,0BAAkC;IAChCD,EAAA,CAAAyF,UAAA,KAAAwB,yCAAA,qBASS;IACXjH,EAAA,CAAAW,YAAA,EAAW;;;;;;IA1LRX,EAAA,CAAAY,SAAA,GAA8D;IAA9DZ,EAAA,CAAAqC,UAAA,SAAA6E,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,UAAA,kBAAAF,MAAA,CAAAG,aAAA,GAA8D;IAO9DrH,EAAA,CAAAY,SAAA,GAKH;IALGZ,EAAA,CAAAqC,UAAA,UAAA6E,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAG,aAAA,MAAAH,MAAA,CAAAI,cAAA,aAAAJ,MAAA,CAAAK,kBAAA,CAKH;IAUGvH,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAqC,UAAA,SAAA6E,MAAA,CAAAG,aAAA,OAAAH,MAAA,CAAAK,kBAAA,CAA4C;IAqC5CvH,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAqC,UAAA,SAAA6E,MAAA,CAAAG,aAAA,OAAAH,MAAA,CAAAK,kBAAA,CAA4C;IAiBlCvH,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAkF,GAAA,EAA8B;IACjCxH,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,qCAA4C;IAIlD1B,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAqC,UAAA,sBAAAoF,IAAA,CAAqC,YAAAzH,EAAA,CAAA2C,eAAA,KAAAC,GAAA,EAAAsE,MAAA,CAAAnG,eAAA;IAI/Bf,EAAA,CAAAY,SAAA,GAAwC;IAAxCZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,iCAAwC;IASrC1B,EAAA,CAAAY,SAAA,GAAgC;IAAhCZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAoF,GAAA,EAAgC;IACnC1H,EAAA,CAAAY,SAAA,GAAgD;IAAhDZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,yCAAgD;IAO7C1B,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAqF,GAAA,EAA4B;IAC/B3H,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,0CAAiD;IAUxD1B,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAqC,UAAA,SAAA6E,MAAA,CAAAG,aAAA,GAAqB;IAQrBrH,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAqC,UAAA,UAAA6E,MAAA,CAAAG,aAAA,GAAsB;IAStBrH,EAAA,CAAAY,SAAA,GAAoD;IAApDZ,EAAA,CAAAqC,UAAA,SAAA6E,MAAA,CAAA1C,cAAA,YAAA0C,MAAA,CAAA1C,cAAA,OAAoD;IAIpDxE,EAAA,CAAAY,SAAA,GAAoD;IAApDZ,EAAA,CAAAqC,UAAA,SAAA6E,MAAA,CAAA1C,cAAA,YAAA0C,MAAA,CAAA1C,cAAA,OAAoD;IAWrDxE,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqC,UAAA,yBAAwB;IAGtBrC,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA0B,WAAA,gCAAAwF,MAAA,CAAAU,QAAA,MACF;IACsB5H,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAqC,UAAA,sBAAAwF,IAAA,CAAkC;IAEpD7H,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAwF,IAAA,EAAyB;IAIrB9H,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,6BAAoC;IAIxC1B,EAAA,CAAAY,SAAA,GAA+B;IAA/BZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAAyF,IAAA,EAA+B;IAI3B/H,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,0BAAiC;IAIrC1B,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAqC,UAAA,SAAArC,EAAA,CAAAsC,eAAA,KAAA0F,IAAA,EAA6B;IAIzBhI,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,2BAAkC;IAKvB1B,EAAA,CAAAY,SAAA,GAAY;IAAZZ,EAAA,CAAAqC,UAAA,YAAA6E,MAAA,CAAAe,SAAA,CAAY;;;;;;;;ADhLvC,OAAM,MAAOC,gBAAgB;EAuB3BC,YACSC,MAAc,EACbC,UAAsB,EACtBC,aAA4B,EAC5BC,cAA8B,EAC9BC,YAA0B,EAC1BC,WAAwB,EACxBC,eAAgC,EAChCC,cAA8B,EAC/BC,aAA4B,EAC3BC,aAA4B,EAC5BC,MAAiB,EACjBC,GAAsB,EACtBC,qBAA4C,EAC5CC,gBAAkC;IAbnC,KAAAb,MAAM,GAANA,MAAM;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IApCnB,KAAAhB,SAAS,GAAW,EAAE;IACtB,KAAAzD,cAAc,GAAW,EAAE;IAC3B,KAAA/C,KAAK,GAAW,qBAAqB;IAC5C,KAAAyH,kBAAkB,GAAW,EAAE;IAExB,KAAAtB,QAAQ,GAAW,EAAE;IAC5B,KAAA9D,eAAe,GAAY,KAAK;IAChC,KAAAqF,YAAY,GAAY,KAAK;IAC7B,KAAAhC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAe,SAAS;IAClC,KAAA7D,WAAW,GAAW,CAAC;IACf,KAAA6F,QAAQ,GAAG,IAAIxJ,OAAO,EAAQ;IACtC,KAAAiC,gBAAgB,GAAqB,IAAIwH,IAAI,EAAE;IAC/C,KAAA9B,kBAAkB,GAAY,KAAK;IACnC,KAAAxG,eAAe,GAA0B,IAAI;IAC7C,KAAA0B,aAAa,GAAkB;MAC7BI,OAAO,EAAE,KAAK;MACdK,OAAO,EAAE,KAAK;MACdR,QAAQ,EAAE,EAAE;MACZO,QAAQ,EAAE,EAAE;MACZH,YAAY,EAAE;KACf;IAiBCnD,MAAM,CAAC,MAAK;MACV,IAAI,CAACkC,gBAAgB,GAAG,IAAI,CAACwG,UAAU,CAACxG,gBAAgB,EAAE;MAC1D,IAAI,CAACd,eAAe,GAAG,IAAI,CAACiI,qBAAqB,CAACjI,eAAe,EAAE;MACnE,IAAI,CAACwG,kBAAkB,GAAG,IAAI,CAACsB,aAAa,CAACtB,kBAAkB,EAAE;IACnE,CAAC,CAAC;EACJ;EAEA+B,QAAQA,CAAA;IACN,IAAI,CAACZ,eAAe,CAACT,SAAS,CAACsB,SAAS,CAAEC,KAAa,IAAI;MACzD,IAAI,CAACvB,SAAS,GAAGuB,KAAK;IACxB,CAAC,CAAC;IACF,IAAI,CAACnB,UAAU,CAACoB,aAAa,EAAE;IAE/B;IACA,IAAI,CAAChB,WAAW,CAACiB,WAAW,EAAE,CAACH,SAAS,CAAEI,IAAI,IAAI;MAChD,IAAIA,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QACxC;QACA,IAAIH,IAAI,CAACnF,cAAc,EAAE;UACvB,IAAI,CAACA,cAAc,GAAGmF,IAAI,CAACnF,cAAc;;QAE3C;QACA,IAAImF,IAAI,CAAC3I,IAAI,EAAE;UACb,IAAI,CAAC4G,QAAQ,GAAG+B,IAAI,CAAC3I,IAAI,CAAC+I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;;IAG7C,CAAC,CAAC;IAEF;IACA,IAAI,CAACtB,WAAW,CAACuB,cAAc,EAAE;IAEjC,IAAI,CAAC1B,aAAa,CAAC2B,iBAAiB,EAAE,CAACV,SAAS,CAAEW,UAAU,IAAI;MAC9D,IAAIA,UAAU,IAAIpK,UAAU,CAACqK,MAAM,EAAE;QACnC,IAAI,CAACrG,eAAe,GAAG,KAAK;OAC7B,MAAM;QACL,IAAI,CAACA,eAAe,GAAG,IAAI;;IAE/B,CAAC,CAAC;IAEF,IAAI,CAAC6E,cAAc,CAACyB,qBAAqB,EAAE,CAACb,SAAS,CAAEc,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE;QACX,IAAI,CAAC/C,cAAc,GAAG+C,OAAO;QAC7B,IAAIA,OAAO,CAACC,QAAQ,CAACR,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACZ,kBAAkB,GAAGmB,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACtJ,IAAI;UAClD,IAAI,CAACS,KAAK,GAAG,GAAG4I,OAAO,CAACrJ,IAAI,MAAM,IAAI,CAACkI,kBAAkB,EAAE;SAC5D,MAAM,IAAI,CAACzH,KAAK,GAAG,GAAG4I,OAAO,CAACrJ,IAAI,EAAE;;IAEzC,CAAC,CAAC;IACF,IAAI,CAACwH,YAAY,CAAC+B,6BAA6B,EAAE,CAAChB,SAAS,CAAEe,QAAQ,IAAI;MACvE,IAAIA,QAAQ,EAAE,IAAI,CAAC/G,WAAW,GAAG+G,QAAQ,CAACR,MAAM;IAClD,CAAC,CAAC;IACF,IAAI,CAACtB,YAAY,CAACgC,oBAAoB,EAAE,CAACjB,SAAS,CAAEkB,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE;QACX,IAAI,CAACvB,kBAAkB,GAAGuB,OAAO,CAACzJ,IAAI;QACtC,IAAI,CAACS,KAAK,GAAG,GAAG,IAAI,CAAC6F,cAAc,EAAEtG,IAAI,MAAM,IAAI,CAACkI,kBAAkB,EAAE;OACzE,MAAM;QACL,IAAI,CAACzH,KAAK,GAAG,GAAG,IAAI,CAAC6F,cAAc,EAAEtG,IAAI,EAAE;;IAE/C,CAAC,CAAC;IAEF;IACA,IAAI,CAAC4H,aAAa,CAAC8B,UAAU,CAC1BC,IAAI,CAAC9K,SAAS,CAAC,IAAI,CAACuJ,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAEqB,MAAM,IAAI;MACpB;MACAC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1D,SAAS,GAAGyD,MAAM;QACvB;QACA,IAAI,CAAC7B,GAAG,CAAC+B,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAClC,aAAa,CAACmC,WAAW,CAC3BJ,IAAI,CAAC9K,SAAS,CAAC,IAAI,CAACuJ,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAEyB,IAAI,IAAI;MAClB;MACAH,UAAU,CAAC,MAAK;QACd,IAAI,CAACzD,UAAU,GAAG4D,IAAI;QACtB;QACA,IAAI,CAACjC,GAAG,CAAC+B,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EACAG,eAAeA,CAAA;IACb,IAAI,CAACxJ,KAAK,GAAG,qBAAqB;IAClC,IAAI,CAACsH,GAAG,CAAC+B,aAAa,EAAE;EAC1B;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC9B,QAAQ,CAAC+B,IAAI,EAAE;IACpB,IAAI,CAAC/B,QAAQ,CAACgC,QAAQ,EAAE;EAC1B;EAEA/J,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC+G,MAAM,CAACiD,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MACvC,IAAI,CAAC7J,KAAK,GAAG,qBAAqB;MAClC;MACA,IAAI,CAACwH,gBAAgB,CAACsC,WAAW,EAAE;MACnC,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEAnE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACe,MAAM,CAACiD,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC;EAC5C;EAEAzE,cAAcA,CAAA;IACZ,IAAI,CAACiC,MAAM,CAAC2C,IAAI,CAAC1L,mBAAmB,EAAE;MACpC2L,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;KACb,CAAC;EACJ;EAEA;;;;EAIA5E,QAAQA,CAAA;IACN,IAAI,IAAI,CAACoB,MAAM,CAACiD,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtC,IAAI,CAAC3C,cAAc,CAACkD,mBAAmB,CAAC,IAAI,CAACvE,cAAc,CAACwE,EAAG,CAAC;;IAElE,IAAI,CAACrD,WAAW,CAACsD,MAAM,EAAE;EAC3B;EAEAzH,YAAYA,CAAA;IACV;IACA,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EAEAb,cAAcA,CAAA;IACZ,IAAI,CAAC4E,cAAc,CAACyD,aAAa,EAAE;EACrC;EACA/F,eAAeA,CAAA;IACb,IAAI,CAACsC,cAAc,CAAC0D,6BAA6B,EAAE;EACrD;EACA7F,mBAAmBA,CAAC8F,cAAuB;IACzC,IAAI,CAAC3D,cAAc,CAAC4D,yBAAyB,CAACD,cAAc,CAAC;EAC/D;EACAhH,cAAcA,CAACkH,QAAgB;IAC7B,IAAI,CAAC1D,eAAe,CAACxD,cAAc,CAACkH,QAAQ,CAAC;EAC/C;EACAC,iBAAiBA,CAACrL,IAAY,EAAE8I,MAAc;IAC5C,OAAO9I,IAAI,CAAC8I,MAAM,GAAGA,MAAM;EAC7B;EACA5F,gBAAgBA,CAAA;IACd,IAAI,CAACyE,cAAc,CAAC2D,iBAAiB,EAAE;EACzC;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAAC1D,aAAa,CAAC2D,qBAAqB,CAAC,IAAI,CAAC;EAChD;EAEA/L,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACoI,aAAa,CAAC2D,qBAAqB,CAAC,KAAK,CAAC;EACjD;EAAC,QAAAC,CAAA,G;qBAnMUvE,gBAAgB,EAAAlI,EAAA,CAAA0M,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA5M,EAAA,CAAA0M,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA9M,EAAA,CAAA0M,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAhN,EAAA,CAAA0M,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAlN,EAAA,CAAA0M,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAApN,EAAA,CAAA0M,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAtN,EAAA,CAAA0M,iBAAA,CAAAa,EAAA,CAAAC,eAAA,GAAAxN,EAAA,CAAA0M,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAA1N,EAAA,CAAA0M,iBAAA,CAAAiB,EAAA,CAAAC,aAAA,GAAA5N,EAAA,CAAA0M,iBAAA,CAAAmB,GAAA,CAAAC,aAAA,GAAA9N,EAAA,CAAA0M,iBAAA,CAAAqB,GAAA,CAAAC,SAAA,GAAAhO,EAAA,CAAA0M,iBAAA,CAAA1M,EAAA,CAAAiO,iBAAA,GAAAjO,EAAA,CAAA0M,iBAAA,CAAAwB,GAAA,CAAAC,qBAAA,GAAAnO,EAAA,CAAA0M,iBAAA,CAAA0B,GAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBpG,gBAAgB;IAAAqG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpC7B7O,EAAA,CAAAC,cAAA,aAA0B;QAOpBD,EAAA,CAAAyF,UAAA,IAAAsJ,+BAAA,iBAUM;QAGN/O,EAAA,CAAAyF,UAAA,IAAAuJ,wCAAA,0BASe;QACjBhP,EAAA,CAAAW,YAAA,EAAM;QAGNX,EAAA,CAAAyF,UAAA,IAAAwJ,+BAAA,mBA6LM;QACRjP,EAAA,CAAAW,YAAA,EAAc;;;QA5NZX,EAAA,CAAAY,SAAA,GAAgD;QAAhDZ,EAAA,CAAAqC,UAAA,YAAArC,EAAA,CAAA2C,eAAA,IAAAuM,IAAA,EAAAJ,GAAA,CAAAvH,kBAAA,EAAgD;QAIhBvH,EAAA,CAAAY,SAAA,GAAwB;QAAxBZ,EAAA,CAAAqC,UAAA,SAAAyM,GAAA,CAAAvH,kBAAA,CAAwB;QAavCvH,EAAA,CAAAY,SAAA,GAAyB;QAAzBZ,EAAA,CAAAqC,UAAA,UAAAyM,GAAA,CAAAvH,kBAAA,CAAyB;QAabvH,EAAA,CAAAY,SAAA,GAAyB;QAAzBZ,EAAA,CAAAqC,UAAA,UAAAyM,GAAA,CAAAvH,kBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}