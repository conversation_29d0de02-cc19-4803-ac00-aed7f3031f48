{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { Subject, debounceTime, takeUntil } from 'rxjs';\nimport { colorGroups, colorList } from 'src/app/shared/configs/colorConfig';\nimport { AttributeType } from 'src/app/shared/model/attribute';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/attribute/attribute.service\";\nimport * as i3 from \"../../services/enumeration/enumeration.service\";\nimport * as i4 from \"../../services/folder/folder.service\";\nimport * as i5 from \"../../services/class/class.service\";\nimport * as i6 from \"../../services/literal/literal.service\";\nimport * as i7 from \"../../services/property/property.service\";\nimport * as i8 from \"../../services/gojs/gojsCommon/gojs-common.service\";\nimport * as i9 from \"../../services/cardinality/cardinality.service\";\nimport * as i10 from \"src/app/shared/utils/diagram-utils\";\nimport * as i11 from \"@ngx-translate/core\";\nfunction PropertiesComponent_div_0_div_13_mat_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", type_r11.name, \" \");\n  }\n}\nfunction PropertiesComponent_div_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-form-field\", 6)(2, \"mat-select\", 13);\n    i0.ɵɵlistener(\"selectionChange\", function PropertiesComponent_div_0_div_13_Template_mat_select_selectionChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onSelectionChange($event));\n    })(\"valueChange\", function PropertiesComponent_div_0_div_13_Template_mat_select_valueChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.form.value.dataType = $event);\n    });\n    i0.ɵɵtemplate(3, PropertiesComponent_div_0_div_13_mat_option_3_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.form.value.dataType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.attributeTypes);\n  }\n}\nfunction PropertiesComponent_div_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-form-field\", 17)(2, \"mat-label\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"textarea\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"property.description\"));\n  }\n}\nfunction PropertiesComponent_div_0_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"mat-form-field\", 6)(3, \"mat-label\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"input\", 19);\n    i0.ɵɵlistener(\"value\", function PropertiesComponent_div_0_ng_container_15_Template_input_value_6_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.form.value.volumetry);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 5)(8, \"mat-form-field\", 6)(9, \"mat-label\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 20);\n    i0.ɵɵlistener(\"value\", function PropertiesComponent_div_0_ng_container_15_Template_input_value_12_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.form.value.tag);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"property.volumetry\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 4, \"property.tag\"));\n  }\n}\nfunction PropertiesComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-form-field\", 6)(2, \"mat-label\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 21);\n    i0.ɵɵlistener(\"value\", function PropertiesComponent_div_0_div_16_Template_input_value_5_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.form.value.fromComment);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"property.fromComment\"));\n  }\n}\nfunction PropertiesComponent_div_0_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-form-field\", 6)(2, \"mat-label\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 22);\n    i0.ɵɵlistener(\"value\", function PropertiesComponent_div_0_div_17_Template_input_value_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.form.value.toComment);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"property.toComment\"));\n  }\n}\nfunction PropertiesComponent_div_0_div_18_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PropertiesComponent_div_0_div_18_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.toggleColorPicker());\n    });\n    i0.ɵɵtext(6, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"app-color-groups\", 31);\n    i0.ɵɵlistener(\"colorSelected\", function PropertiesComponent_div_0_div_18_div_8_Template_app_color_groups_colorSelected_7_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.onColorSelected($event));\n    })(\"customColorSelected\", function PropertiesComponent_div_0_div_18_div_8_Template_app_color_groups_customColorSelected_7_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.onCustomColorSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"colorPicker.selectColor\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"selectedColor\", ctx_r22.color);\n  }\n}\nfunction PropertiesComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-form-field\", 23)(2, \"mat-label\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 24);\n    i0.ɵɵelement(6, \"input\", 25);\n    i0.ɵɵelementStart(7, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function PropertiesComponent_div_0_div_18_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.toggleColorPicker());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, PropertiesComponent_div_0_div_18_div_8_Template, 8, 4, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 5, \"property.color\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r7.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r7.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.showColorPicker);\n  }\n}\nfunction PropertiesComponent_div_0_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"diagram.propertyDescription\"));\n  }\n}\nfunction PropertiesComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"span\", 2);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"mat-form-field\", 6)(8, \"mat-label\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 7, 8);\n    i0.ɵɵlistener(\"value\", function PropertiesComponent_div_0_Template_input_value_11_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.form.value.name);\n    })(\"click\", function PropertiesComponent_div_0_Template_input_click_11_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const _r1 = i0.ɵɵreference(12);\n      return i0.ɵɵresetView(_r1.select());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, PropertiesComponent_div_0_div_13_Template, 4, 2, \"div\", 9);\n    i0.ɵɵtemplate(14, PropertiesComponent_div_0_div_14_Template, 6, 3, \"div\", 10);\n    i0.ɵɵtemplate(15, PropertiesComponent_div_0_ng_container_15_Template, 13, 6, \"ng-container\", 11);\n    i0.ɵɵtemplate(16, PropertiesComponent_div_0_div_16_Template, 6, 3, \"div\", 9);\n    i0.ɵɵtemplate(17, PropertiesComponent_div_0_div_17_Template, 6, 3, \"div\", 9);\n    i0.ɵɵtemplate(18, PropertiesComponent_div_0_div_18_Template, 9, 7, \"div\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, PropertiesComponent_div_0_ng_template_19_Template, 3, 3, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 9, \"diagram.properties\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.form);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 11, \"property.name\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.form.value.dataType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isGroupNode || ctx_r0.isAttributeNode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isGroupNode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLinkNode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLinkNode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isGroupNode || ctx_r0.isLinkNode);\n  }\n}\nexport class PropertiesComponent {\n  constructor(fb, attributeService, enumerationService, folderService, classService, literalService, propertyService, commonGojsService, linkService, diagramUtils, elementRef, translateService) {\n    this.fb = fb;\n    this.attributeService = attributeService;\n    this.enumerationService = enumerationService;\n    this.folderService = folderService;\n    this.classService = classService;\n    this.literalService = literalService;\n    this.propertyService = propertyService;\n    this.commonGojsService = commonGojsService;\n    this.linkService = linkService;\n    this.diagramUtils = diagramUtils;\n    this.elementRef = elementRef;\n    this.translateService = translateService;\n    this.isGroupNode = false;\n    this.isAttributeNode = false;\n    this.showPropertyForm = false;\n    this.isLinkNode = false;\n    this.showColorPicker = false;\n    this.cardinalities = ['0..1', '1', '*', '1..*'];\n    this.inputSubject = new Subject();\n    this.destroy$ = new Subject();\n    this.debounceTimeMs = 400;\n    this.presetValues = [];\n    this.colorGroupsConfig = {};\n    this.haveEditAccess = false;\n    this.currentDiagramId = 0;\n    this.updateNodePropertyEmitter = new EventEmitter();\n    this.colorPickerSelectEmitter = new EventEmitter();\n    this.attributeTypes = [];\n    this.presetValues = this.getColorValues();\n    this.colorGroupsConfig = this.getColorGroups();\n  }\n  ngOnInit() {\n    this.inputSubject.pipe(debounceTime(this.debounceTimeMs), takeUntil(this.destroy$)).subscribe(inputValue => {\n      if (inputValue.name.trim() != '') {\n        this.nodeProperty = {\n          ...this.nodeProperty,\n          ...inputValue\n        };\n        if (inputValue.category == GojsNodeCategory.Class || inputValue.category == GojsNodeCategory.AssociativeClass) {\n          if (this.commonGojsService.isGojsDiagramClassNode(this.nodeProperty)) {\n            this.updateTemplateClass(this.nodeProperty);\n          }\n        } else if (inputValue.category == GojsNodeCategory.Enumeration) {\n          if (this.commonGojsService.isGojsDiagramEnumerationNode(this.nodeProperty)) {\n            this.updateTemplateEnumeration(this.nodeProperty);\n          }\n        } else if (inputValue.category == GojsNodeCategory.Attribute || inputValue.category == GojsNodeCategory.Operation) {\n          if (this.commonGojsService.isGojsDiagramAttributeNode(this.nodeProperty)) {\n            this.updateAttribute(this.nodeProperty);\n          }\n        } else if (inputValue.category == GojsNodeCategory.EnumerationLiteral) {\n          if (this.commonGojsService.isGojsDiagramLiteralNode(this.nodeProperty)) {\n            this.updateLiteral(this.nodeProperty);\n          }\n        } else if (this.nodeProperty.category == GojsNodeCategory.Folder) {\n          if (this.commonGojsService.isGojsPaletteFolderNode(this.nodeProperty)) this.updateFolder({\n            id: this.nodeProperty.idFolder,\n            name: inputValue.name,\n            icon: this.nodeProperty.icon\n          });\n        } else if (this.nodeProperty.category == GojsNodeCategory.Association) {\n          this.updateLinkNode({\n            ...this.nodeProperty,\n            name: inputValue.name,\n            color: this.nodeProperty.color,\n            cardinalityFrom: this.nodeProperty.cardinalityFrom,\n            cardinalityTo: this.nodeProperty.cardinalityTo,\n            segmentOffset: this.nodeProperty.segmentOffset\n          });\n        }\n      }\n    });\n    this.getPropertyData();\n  }\n  updateLiteral(nodeProperty) {\n    this.literalService.updateEnumerationLiteral({\n      id: nodeProperty.id,\n      name: nodeProperty.name,\n      key: nodeProperty.key\n    }).subscribe(res => {\n      this.updateNodePropertyEmitter.emit({\n        ...nodeProperty,\n        ...res\n      });\n    });\n  }\n  updateTemplateEnumeration(inputValue) {\n    this.enumerationService.updateTempEnumeration({\n      id: inputValue.idTemplateEnumeration,\n      name: inputValue.name,\n      key: this.nodeProperty.key,\n      icon: inputValue.icon,\n      color: inputValue.color,\n      description: inputValue.description,\n      volumetry: inputValue.volumetry,\n      tag: inputValue.tag\n    }, this.currentDiagramId).subscribe(res => {\n      this.updateNodePropertyEmitter.emit({\n        ...this.nodeProperty,\n        ...res\n      });\n      this.diagramUtils.updateAttributeType(res.id?.toString(), res.name);\n    });\n  }\n  updateTemplateClass(templateClass) {\n    this.classService.updateTemplateClass({\n      id: templateClass.idTemplateClass,\n      name: templateClass.name,\n      key: templateClass?.key?.toString().split('_')[0],\n      icon: templateClass.icon,\n      color: templateClass.color,\n      tag: templateClass?.tag,\n      volumetry: templateClass?.volumetry,\n      description: templateClass?.description,\n      isAssociative: templateClass.category == GojsNodeCategory.AssociativeClass ? true : false\n    }, this.currentDiagramId).subscribe(_ => {\n      this.updateNodePropertyEmitter.emit({\n        ...templateClass,\n        id: templateClass.idTemplateClass\n      });\n    });\n  }\n  updateAttribute(attribute) {\n    const attributeOption = this.attributeTypes.find(o => o.id === attribute.dataType);\n    this.attributeService.updateAttribute({\n      id: attribute.id,\n      name: attribute.name,\n      description: attribute?.description,\n      category: attribute.memberType,\n      attributeType: !attributeOption?.isEnumeration ? this.attributeService.getDefaultAttributeId(attribute.dataType.toString()) : AttributeType.Undefined,\n      idTemplateEnumeration: attributeOption?.isEnumeration ? attributeOption?.id : 0,\n      key: attribute.key\n    }).subscribe(attr => {\n      this.updateNodePropertyEmitter.emit({\n        ...attribute,\n        dataType: attr.idTemplateEnumeration == 0 ? `${attr.attributeType}_${AttributeType[attr.attributeType]}` : attr.idTemplateEnumeration?.toString()\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  updateFolder(folderObject) {\n    this.folderService.updateFolder(folderObject).subscribe(updatedFolder => {\n      this.updateNodePropertyEmitter.emit({\n        ...this.nodeProperty,\n        ...updatedFolder\n      });\n    });\n  }\n  updateLinkNode(linkNode) {\n    this.linkService.updateLink({\n      idLinkType: linkNode.idLinkType,\n      color: linkNode.color,\n      id: +linkNode?.key,\n      name: linkNode.name,\n      fromComment: linkNode.fromComment,\n      toComment: linkNode.toComment,\n      idSourceTempClass: linkNode.idSourceTempClass,\n      idDestinationTempClass: linkNode.idDestinationTempClass,\n      segmentOffset: linkNode.segmentOffset,\n      linkPort: {\n        idDiagram: this.currentDiagramId,\n        destinationPort: linkNode.toPort,\n        sourcePort: linkNode.fromPort,\n        idLink: linkNode.id,\n        segmentOffset: linkNode.segmentOffset\n      }\n    }).subscribe(updatedLink => {\n      this.updateNodePropertyEmitter.emit({\n        ...this.nodeProperty,\n        ...updatedLink\n      });\n    });\n  }\n  getPropertyData() {\n    this.propertyService.propertyDataChanges().pipe(takeUntil(this.destroy$)).subscribe(data => {\n      if (data) {\n        if (Object.keys(data).length > 0) {\n          this.showPropertyForm = true;\n          this.nodeProperty = data;\n          this.isLinkNode = Boolean(this.nodeProperty.category == GojsNodeCategory.Association);\n          this.isAttributeNode = Boolean(this.nodeProperty.category === GojsNodeCategory.Attribute || this.nodeProperty.category === GojsNodeCategory.Operation);\n          if (this.nodeProperty.category === GojsNodeCategory.Enumeration || this.nodeProperty.category === GojsNodeCategory.Class || this.nodeProperty.category === GojsNodeCategory.AssociativeClass) this.isGroupNode = true;else this.isGroupNode = false;\n          this.initForm(data);\n        } else this.showPropertyForm = false;\n      } else this.showPropertyForm = false;\n    });\n  }\n  initForm(data) {\n    const {\n      category,\n      name,\n      description,\n      volumetry,\n      tag,\n      color,\n      dataType,\n      fromComment,\n      toComment\n    } = data;\n    if (category === GojsNodeCategory.Operation || category === GojsNodeCategory.Attribute) {\n      const attributeOption = this.attributeTypes.find(option => option.id === dataType.toString());\n      data.dataType = attributeOption ? attributeOption.id : `0_${AttributeType[AttributeType.Undefined]}`;\n    }\n    const createControl = value => new FormControl({\n      value,\n      disabled: !this.haveEditAccess\n    });\n    this.form = this.fb.group({\n      name: createControl(name),\n      description: createControl(description),\n      volumetry: createControl(volumetry),\n      tag: createControl(tag),\n      color: createControl(color),\n      dataType: createControl(dataType?.toString()),\n      category: createControl(category),\n      fromComment: createControl(fromComment),\n      toComment: createControl(toComment)\n    });\n    this.color = color;\n    this.form.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(res => {\n      this.inputSubject.next(res);\n    });\n  }\n  changeColor(color) {\n    this.color = color;\n    this.form.controls['color'].setValue(color, {\n      emitEvent: true\n    });\n  }\n  onSelectionChange(event) {\n    this.form.controls['dataType'].setValue(event.value);\n  }\n  toggleColorPicker() {\n    this.showColorPicker = !this.showColorPicker;\n    this.colorPickerSelectEmitter.emit(this.showColorPicker);\n    // Ensure the color is properly initialized\n    if (this.showColorPicker && !this.color) {\n      // Set a default color if none is selected\n      this.color = 'rgba(229, 57, 53, 0.8)';\n      this.form.controls['color'].setValue(this.color, {\n        emitEvent: false\n      });\n    }\n  }\n  onColorSelected(color) {\n    this.color = color;\n    this.changeColor(color);\n    this.showColorPicker = false;\n    // Update the form control value without displaying it in the input\n    this.form.controls['color'].setValue(color, {\n      emitEvent: false\n    });\n  }\n  onCustomColorSelected(color) {\n    this.color = color;\n    this.changeColor(color);\n    // Update the form control value without displaying it in the input\n    this.form.controls['color'].setValue(color, {\n      emitEvent: false\n    });\n    // Note: We don't close the color picker here\n  }\n  handleDocumentClick(event) {\n    // Close color picker when clicking outside the component\n    if (this.showColorPicker) {\n      const clickedInside = this.elementRef.nativeElement.contains(event.target);\n      if (!clickedInside) {\n        this.showColorPicker = false;\n      }\n    }\n  }\n  getColorValues() {\n    return colorList.map(c => c.value);\n  }\n  getColorGroups() {\n    const groups = {};\n    colorGroups.forEach(group => {\n      groups[group.name] = group.colors.map(c => c.value);\n    });\n    return groups;\n  }\n  static #_ = this.ɵfac = function PropertiesComponent_Factory(t) {\n    return new (t || PropertiesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AttributeService), i0.ɵɵdirectiveInject(i3.EnumerationService), i0.ɵɵdirectiveInject(i4.FolderService), i0.ɵɵdirectiveInject(i5.ClassService), i0.ɵɵdirectiveInject(i6.LiteralService), i0.ɵɵdirectiveInject(i7.PropertyService), i0.ɵɵdirectiveInject(i8.GojsCommonService), i0.ɵɵdirectiveInject(i9.CardinalityService), i0.ɵɵdirectiveInject(i10.DiagramUtils), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i11.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PropertiesComponent,\n    selectors: [[\"app-properties\"]],\n    hostBindings: function PropertiesComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function PropertiesComponent_click_HostBindingHandler($event) {\n          return ctx.handleDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    inputs: {\n      nodeProperty: [\"nodeData\", \"nodeProperty\"],\n      haveEditAccess: [\"editAccess\", \"haveEditAccess\"],\n      currentDiagramId: [\"idDiagram\", \"currentDiagramId\"],\n      attributeTypes: [\"dataTypes\", \"attributeTypes\"]\n    },\n    outputs: {\n      updateNodePropertyEmitter: \"onChangePropertyData\",\n      colorPickerSelectEmitter: \"isColorPickerSelected\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"properties\", 4, \"ngIf\"], [1, \"properties\"], [1, \"property-title\"], [1, \"property-form\", 3, \"formGroup\"], [2, \"position\", \"relative\"], [1, \"property-field\"], [\"appearance\", \"outline\"], [\"type\", \"text\", \"matInput\", \"\", \"name\", \"nodeName\", \"formControlName\", \"name\", 3, \"value\", \"click\"], [\"nameInput\", \"\"], [\"class\", \"property-field\", 4, \"ngIf\"], [\"class\", \"description-textArea\", 4, \"ngIf\"], [4, \"ngIf\"], [\"noProperty\", \"\"], [3, \"value\", \"selectionChange\", \"valueChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"description-textArea\"], [\"appearance\", \"outline\", 1, \"description-field\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"2\"], [\"type\", \"text\", \"matInput\", \"\", \"formControlName\", \"volumetry\", 3, \"value\"], [\"type\", \"text\", \"matInput\", \"\", \"formControlName\", \"tag\", 3, \"value\"], [\"type\", \"text\", \"matInput\", \"\", \"formControlName\", \"fromComment\", 3, \"value\"], [\"type\", \"text\", \"matInput\", \"\", \"formControlName\", \"toComment\", 3, \"value\"], [\"appearance\", \"outline\", 1, \"color-field-form\"], [1, \"color-field-container\"], [\"matInput\", \"\", \"formControlName\", \"color\", \"readonly\", \"true\", 2, \"display\", \"none\", 3, \"value\"], [1, \"color-display\", 3, \"click\"], [\"class\", \"color-picker-popup\", 4, \"ngIf\"], [1, \"color-picker-popup\"], [1, \"color-picker-header\"], [1, \"close-button\", 3, \"click\"], [3, \"selectedColor\", \"colorSelected\", \"customColorSelected\"]],\n    template: function PropertiesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, PropertiesComponent_div_0_Template, 21, 13, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.showPropertyForm);\n      }\n    },\n    styles: [\".properties[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  margin-inline: 4%;\\n  align-items: center;\\n}\\n.properties[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 1rem;\\n}\\n\\n.property-form[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  height: 1.5vh;\\n}\\n.property-form[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label {\\n  top: 16px;\\n}\\n.property-form[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {\\n  --mat-mdc-form-field-label-transform: translateY(-23.75px)\\n    scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));\\n  transform: var(--mat-mdc-form-field-label-transform);\\n}\\n.property-form[_ngcontent-%COMP%]    .mat-mdc-form-field-infix {\\n  margin-top: 0.2rem;\\n}\\n.property-form[_ngcontent-%COMP%]   .link-port[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.property-form[_ngcontent-%COMP%]   .link-port[_ngcontent-%COMP%]    > mat-form-field[_ngcontent-%COMP%]:first-child {\\n  margin-right: 0.6rem;\\n}\\n\\n.property-field[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  margin-inline: 0.5rem;\\n  font-size: 14px;\\n  position: relative;\\n  \\n\\n  \\n\\n}\\n.property-field.color-field[_ngcontent-%COMP%] {\\n  position: relative;\\n  \\n\\n}\\n.property-field.color-field[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.property-field[_ngcontent-%COMP%]   mat-form-field.color-field-form[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.property-field[_ngcontent-%COMP%]   mat-form-field.color-field-form[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding-top: 6px;\\n  padding-bottom: 6px;\\n  display: flex;\\n}\\n.property-field[_ngcontent-%COMP%]   mat-form-field.color-field-form[_ngcontent-%COMP%]   .mat-mdc-form-field-flex[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\n\\n.property-label[_ngcontent-%COMP%] {\\n  font-size: smaller;\\n  margin-bottom: 0.4rem;\\n  font-weight: 400;\\n}\\n\\n.description-textArea[_ngcontent-%COMP%] {\\n  margin-inline: 0.5rem;\\n  font-size: 14px;\\n}\\n\\n.description-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0;\\n  font-size: 14px;\\n  padding: 0;\\n}\\n\\n.color-field-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  height: 32px;\\n  margin-top: 4px;\\n}\\n\\n.color-display[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  height: 20px;\\n  width: 100%;\\n  border-radius: 3px;\\n  position: relative;\\n  border: 1px solid rgba(0, 0, 0, 0.12);\\n  margin-bottom: 0.4rem;\\n  \\n\\n}\\n.color-display[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);\\n  background-size: 6px 6px;\\n  background-position: 0 0, 0 3px, 3px -3px, -3px 0px;\\n  border-radius: 2px;\\n  z-index: -1;\\n}\\n\\n.color-picker-popup[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100%;\\n  left: 0;\\n  z-index: 1000;\\n  background-color: white;\\n  border-radius: 4px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 8px;\\n  width: auto;\\n  min-width: 200px;\\n  max-width: 100%;\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n.color-picker-popup[_ngcontent-%COMP%]   .color-picker-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 12px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n  background-color: #f5f5f5;\\n  height: 36px;\\n  box-sizing: border-box;\\n}\\n.color-picker-popup[_ngcontent-%COMP%]   .color-picker-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.color-picker-popup[_ngcontent-%COMP%]   .color-picker-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 18px;\\n  line-height: 1;\\n  padding: 4px 6px;\\n  cursor: pointer;\\n  color: #666;\\n}\\n\\n.mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mat-mdc-form-field-flex[_ngcontent-%COMP%]   .mat-mdc-floating-label[_ngcontent-%COMP%] {\\n  top: 16px;\\n}\\n\\n.mat-mdc-text-field-wrapper.mdc-text-field--outlined[_ngcontent-%COMP%]   .mdc-notched-outline--upgraded[_ngcontent-%COMP%]   .mdc-floating-label--float-above[_ngcontent-%COMP%] {\\n  --mat-mdc-form-field-label-transform: translateY(-23.75px)\\n    scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));\\n  transform: var(--mat-mdc-form-field-label-transform);\\n}\\n\\n.mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  margin-top: 0.2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Subject", "debounceTime", "takeUntil", "colorGroups", "colorList", "AttributeType", "GojsNodeCategory", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "type_r11", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "ɵɵlistener", "PropertiesComponent_div_0_div_13_Template_mat_select_selectionChange_2_listener", "$event", "ɵɵrestoreView", "_r13", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "onSelectionChange", "PropertiesComponent_div_0_div_13_Template_mat_select_valueChange_2_listener", "ctx_r14", "form", "value", "dataType", "ɵɵtemplate", "PropertiesComponent_div_0_div_13_mat_option_3_Template", "ctx_r2", "attributeTypes", "ɵɵelement", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵelementContainerStart", "PropertiesComponent_div_0_ng_container_15_Template_input_value_6_listener", "_r16", "ctx_r15", "volumetry", "PropertiesComponent_div_0_ng_container_15_Template_input_value_12_listener", "ctx_r17", "tag", "ɵɵelementContainerEnd", "PropertiesComponent_div_0_div_16_Template_input_value_5_listener", "_r19", "ctx_r18", "fromComment", "PropertiesComponent_div_0_div_17_Template_input_value_5_listener", "_r21", "ctx_r20", "toComment", "PropertiesComponent_div_0_div_18_div_8_Template_button_click_5_listener", "_r24", "ctx_r23", "toggleColorPicker", "PropertiesComponent_div_0_div_18_div_8_Template_app_color_groups_colorSelected_7_listener", "ctx_r25", "onColorSelected", "PropertiesComponent_div_0_div_18_div_8_Template_app_color_groups_customColorSelected_7_listener", "ctx_r26", "onCustomColorSelected", "ctx_r22", "color", "PropertiesComponent_div_0_div_18_Template_div_click_7_listener", "_r28", "ctx_r27", "PropertiesComponent_div_0_div_18_div_8_Template", "ctx_r7", "ɵɵstyleProp", "showColorPicker", "PropertiesComponent_div_0_Template_input_value_11_listener", "_r30", "ctx_r29", "PropertiesComponent_div_0_Template_input_click_11_listener", "_r1", "ɵɵreference", "select", "PropertiesComponent_div_0_div_13_Template", "PropertiesComponent_div_0_div_14_Template", "PropertiesComponent_div_0_ng_container_15_Template", "PropertiesComponent_div_0_div_16_Template", "PropertiesComponent_div_0_div_17_Template", "PropertiesComponent_div_0_div_18_Template", "PropertiesComponent_div_0_ng_template_19_Template", "ɵɵtemplateRefExtractor", "ctx_r0", "isGroupNode", "isAttributeNode", "isLinkNode", "PropertiesComponent", "constructor", "fb", "attributeService", "enumerationService", "folderService", "classService", "literalService", "propertyService", "commonGojsService", "linkService", "diagramUtils", "elementRef", "translateService", "showPropertyForm", "cardinalities", "inputSubject", "destroy$", "debounceTimeMs", "presetValues", "colorGroupsConfig", "haveEditAccess", "currentDiagramId", "updateNodePropertyEmitter", "colorPickerSelectEmitter", "getColorValues", "getColorGroups", "ngOnInit", "pipe", "subscribe", "inputValue", "trim", "nodeProperty", "category", "Class", "AssociativeClass", "isGojsDiagramClassNode", "updateTemplateClass", "Enumeration", "isGojsDiagramEnumerationNode", "updateTemplateEnumeration", "Attribute", "Operation", "isGojsDiagramAttributeNode", "updateAttribute", "EnumerationLiteral", "isGojsDiagramLiteralNode", "updateLiteral", "Folder", "isGojsPaletteFolderNode", "updateFolder", "idFolder", "icon", "Association", "updateLinkNode", "cardinalityFrom", "cardinalityTo", "segmentOffset", "getPropertyData", "updateEnumerationLiteral", "key", "res", "emit", "updateTempEnumeration", "idTemplateEnumeration", "description", "updateAttributeType", "toString", "templateClass", "idTemplateClass", "split", "isAssociative", "_", "attribute", "attributeOption", "find", "o", "memberType", "attributeType", "isEnumeration", "getDefaultAttributeId", "Undefined", "attr", "ngOnDestroy", "next", "complete", "folderObject", "updatedFolder", "linkNode", "updateLink", "idLinkType", "idSourceTempClass", "idDestinationTempClass", "linkPort", "idDiagram", "destinationPort", "to<PERSON><PERSON>", "sourcePort", "fromPort", "idLink", "updatedLink", "propertyDataChanges", "data", "Object", "keys", "length", "Boolean", "initForm", "option", "createControl", "disabled", "group", "valueChanges", "changeColor", "controls", "setValue", "emitEvent", "event", "handleDocumentClick", "clickedInside", "nativeElement", "contains", "target", "map", "c", "groups", "for<PERSON>ach", "colors", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AttributeService", "i3", "EnumerationService", "i4", "FolderService", "i5", "ClassService", "i6", "LiteralService", "i7", "PropertyService", "i8", "GojsCommonService", "i9", "CardinalityService", "i10", "DiagramUtils", "ElementRef", "i11", "TranslateService", "_2", "selectors", "hostBindings", "PropertiesComponent_HostBindings", "rf", "ctx", "ɵɵresolveDocument", "PropertiesComponent_div_0_Template"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\properties\\properties.component.ts", "D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\properties\\properties.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostListener,\r\n  Input,\r\n  OnDestroy,\r\n  OnInit,\r\n  Output,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormControl, FormGroup } from '@angular/forms';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Subject, debounceTime, takeUntil } from 'rxjs';\r\nimport { colorGroups, colorList } from 'src/app/shared/configs/colorConfig';\r\nimport {\r\n  Attribute,\r\n  AttributeOption,\r\n  AttributeType,\r\n} from 'src/app/shared/model/attribute';\r\nimport { PropertyFormData } from 'src/app/shared/model/common';\r\nimport { Folder } from 'src/app/shared/model/folder';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { Literal } from 'src/app/shared/model/literal';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AttributeService } from '../../services/attribute/attribute.service';\r\nimport { CardinalityService } from '../../services/cardinality/cardinality.service';\r\nimport { ClassService } from '../../services/class/class.service';\r\nimport { EnumerationService } from '../../services/enumeration/enumeration.service';\r\nimport { FolderService } from '../../services/folder/folder.service';\r\nimport { GojsCommonService } from '../../services/gojs/gojsCommon/gojs-common.service';\r\nimport { LiteralService } from '../../services/literal/literal.service';\r\nimport { PropertyService } from '../../services/property/property.service';\r\n\r\n@Component({\r\n  selector: 'app-properties',\r\n  templateUrl: './properties.component.html',\r\n  styleUrls: ['./properties.component.scss'],\r\n})\r\nexport class PropertiesComponent implements OnInit, OnDestroy {\r\n  form!: FormGroup;\r\n  color!: string;\r\n  isGroupNode: boolean = false;\r\n  isAttributeNode: boolean = false;\r\n  showPropertyForm: boolean = false;\r\n  isLinkNode: boolean = false;\r\n  showColorPicker: boolean = false;\r\n  cardinalities: string[] = ['0..1', '1', '*', '1..*'];\r\n  private inputSubject = new Subject<PropertyFormData>();\r\n  private destroy$ = new Subject<void>();\r\n  private readonly debounceTimeMs = 400;\r\n  public presetValues: string[] = [];\r\n  public colorGroupsConfig: any = {};\r\n  @Input('nodeData') nodeProperty!:\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsLinkNode\r\n    | GojsFolderNode;\r\n  @Input('editAccess') haveEditAccess: boolean = false;\r\n  @Input('idDiagram') currentDiagramId: number = 0;\r\n  @Output('onChangePropertyData') updateNodePropertyEmitter = new EventEmitter<\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsLinkNode\r\n    | GojsFolderNode\r\n  >();\r\n  @Output('isColorPickerSelected') colorPickerSelectEmitter =\r\n    new EventEmitter<boolean>();\r\n  @Input('dataTypes') attributeTypes: AttributeOption[] = [];\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private attributeService: AttributeService,\r\n    private enumerationService: EnumerationService,\r\n    private folderService: FolderService,\r\n    private classService: ClassService,\r\n    private literalService: LiteralService,\r\n    private propertyService: PropertyService,\r\n    private commonGojsService: GojsCommonService,\r\n    private linkService: CardinalityService,\r\n    private diagramUtils: DiagramUtils,\r\n    private elementRef: ElementRef,\r\n    private translateService: TranslateService\r\n  ) {\r\n    this.presetValues = this.getColorValues();\r\n    this.colorGroupsConfig = this.getColorGroups();\r\n  }\r\n  ngOnInit(): void {\r\n    this.inputSubject\r\n      .pipe(debounceTime(this.debounceTimeMs), takeUntil(this.destroy$))\r\n      .subscribe((inputValue) => {\r\n        if (inputValue.name.trim() != '') {\r\n          this.nodeProperty = { ...this.nodeProperty, ...inputValue } as\r\n            | GojsDiagramClassNode\r\n            | GojsDiagramEnumerationNode\r\n            | GojsDiagramAttributeNode\r\n            | GojsDiagramLiteralNode\r\n            | GojsLinkNode\r\n            | GojsFolderNode;\r\n          if (\r\n            inputValue.category == GojsNodeCategory.Class ||\r\n            inputValue.category == GojsNodeCategory.AssociativeClass\r\n          ) {\r\n            if (\r\n              this.commonGojsService.isGojsDiagramClassNode(this.nodeProperty)\r\n            ) {\r\n              this.updateTemplateClass(this.nodeProperty);\r\n            }\r\n          } else if (inputValue.category == GojsNodeCategory.Enumeration) {\r\n            if (\r\n              this.commonGojsService.isGojsDiagramEnumerationNode(\r\n                this.nodeProperty\r\n              )\r\n            ) {\r\n              this.updateTemplateEnumeration(this.nodeProperty);\r\n            }\r\n          } else if (\r\n            inputValue.category == GojsNodeCategory.Attribute ||\r\n            inputValue.category == GojsNodeCategory.Operation\r\n          ) {\r\n            if (\r\n              this.commonGojsService.isGojsDiagramAttributeNode(\r\n                this.nodeProperty\r\n              )\r\n            ) {\r\n              this.updateAttribute(this.nodeProperty);\r\n            }\r\n          } else if (\r\n            inputValue.category == GojsNodeCategory.EnumerationLiteral\r\n          ) {\r\n            if (\r\n              this.commonGojsService.isGojsDiagramLiteralNode(this.nodeProperty)\r\n            ) {\r\n              this.updateLiteral(this.nodeProperty);\r\n            }\r\n          } else if (this.nodeProperty.category == GojsNodeCategory.Folder) {\r\n            if (\r\n              this.commonGojsService.isGojsPaletteFolderNode(this.nodeProperty)\r\n            )\r\n              this.updateFolder({\r\n                id: this.nodeProperty.idFolder,\r\n                name: inputValue.name,\r\n                icon: this.nodeProperty.icon,\r\n              } as Folder);\r\n          } else if (\r\n            this.nodeProperty.category == GojsNodeCategory.Association\r\n          ) {\r\n            this.updateLinkNode({\r\n              ...this.nodeProperty,\r\n              name: inputValue.name,\r\n              color: (this.nodeProperty as GojsLinkNode).color,\r\n              cardinalityFrom: (this.nodeProperty as GojsLinkNode)\r\n                .cardinalityFrom,\r\n              cardinalityTo: (this.nodeProperty as GojsLinkNode).cardinalityTo,\r\n              segmentOffset: (this.nodeProperty as GojsLinkNode).segmentOffset,\r\n            } as GojsLinkNode);\r\n          }\r\n        }\r\n      });\r\n    this.getPropertyData();\r\n  }\r\n  updateLiteral(nodeProperty: GojsDiagramLiteralNode) {\r\n    this.literalService\r\n      .updateEnumerationLiteral({\r\n        id: nodeProperty.id,\r\n        name: nodeProperty.name,\r\n        key: nodeProperty.key,\r\n      } as Literal)\r\n      .subscribe((res) => {\r\n        this.updateNodePropertyEmitter.emit({\r\n          ...nodeProperty,\r\n          ...res,\r\n        });\r\n      });\r\n  }\r\n  updateTemplateEnumeration(inputValue: GojsDiagramEnumerationNode) {\r\n    this.enumerationService\r\n      .updateTempEnumeration(\r\n        {\r\n          id: inputValue.idTemplateEnumeration,\r\n          name: inputValue.name,\r\n          key: this.nodeProperty.key,\r\n          icon: inputValue.icon,\r\n          color: inputValue.color,\r\n          description: inputValue.description,\r\n          volumetry: inputValue.volumetry,\r\n          tag: inputValue.tag,\r\n        },\r\n        this.currentDiagramId\r\n      )\r\n      .subscribe((res) => {\r\n        this.updateNodePropertyEmitter.emit({\r\n          ...this.nodeProperty,\r\n          ...res,\r\n        });\r\n        this.diagramUtils.updateAttributeType(res.id?.toString()!, res.name);\r\n      });\r\n  }\r\n  updateTemplateClass(templateClass: GojsDiagramClassNode) {\r\n    this.classService\r\n      .updateTemplateClass(\r\n        {\r\n          id: templateClass.idTemplateClass,\r\n          name: templateClass.name,\r\n          key: templateClass?.key?.toString().split('_')[0],\r\n          icon: templateClass.icon,\r\n          color: templateClass.color,\r\n          tag: templateClass?.tag,\r\n          volumetry: templateClass?.volumetry,\r\n          description: templateClass?.description,\r\n          isAssociative:\r\n            templateClass.category == GojsNodeCategory.AssociativeClass\r\n              ? true\r\n              : false,\r\n        },\r\n        this.currentDiagramId\r\n      )\r\n      .subscribe((_) => {\r\n        this.updateNodePropertyEmitter.emit({\r\n          ...templateClass,\r\n          id: templateClass.idTemplateClass,\r\n        });\r\n      });\r\n  }\r\n  updateAttribute(attribute: GojsDiagramAttributeNode) {\r\n    const attributeOption = this.attributeTypes.find(\r\n      (o) => o.id === attribute.dataType\r\n    );\r\n    this.attributeService\r\n      .updateAttribute({\r\n        id: attribute.id,\r\n        name: attribute.name,\r\n        description: attribute?.description,\r\n        category: attribute.memberType,\r\n        attributeType: !attributeOption?.isEnumeration\r\n          ? this.attributeService.getDefaultAttributeId(\r\n              attribute.dataType.toString()\r\n            )\r\n          : AttributeType.Undefined,\r\n        idTemplateEnumeration: attributeOption?.isEnumeration\r\n          ? attributeOption?.id\r\n          : 0,\r\n        key: attribute.key,\r\n      } as Attribute)\r\n      .subscribe((attr) => {\r\n        this.updateNodePropertyEmitter.emit({\r\n          ...attribute,\r\n          dataType:\r\n            attr.idTemplateEnumeration == 0\r\n              ? `${attr.attributeType}_${AttributeType[attr.attributeType]}`\r\n              : attr.idTemplateEnumeration?.toString(),\r\n        });\r\n      });\r\n  }\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n  private updateFolder(folderObject: Folder) {\r\n    this.folderService.updateFolder(folderObject).subscribe((updatedFolder) => {\r\n      this.updateNodePropertyEmitter.emit({\r\n        ...this.nodeProperty,\r\n        ...updatedFolder,\r\n      });\r\n    });\r\n  }\r\n  updateLinkNode(linkNode: GojsLinkNode): void {\r\n    this.linkService\r\n      .updateLink({\r\n        idLinkType: linkNode.idLinkType,\r\n        color: linkNode.color,\r\n        id: +linkNode?.key!,\r\n        name: linkNode.name,\r\n        fromComment: linkNode.fromComment,\r\n        toComment: linkNode.toComment,\r\n        idSourceTempClass: linkNode.idSourceTempClass,\r\n        idDestinationTempClass: linkNode.idDestinationTempClass,\r\n        segmentOffset: linkNode.segmentOffset,\r\n        linkPort: {\r\n          idDiagram: this.currentDiagramId,\r\n          destinationPort: linkNode.toPort!,\r\n          sourcePort: linkNode.fromPort!,\r\n          idLink: linkNode.id!,\r\n          segmentOffset: linkNode.segmentOffset,\r\n        },\r\n      })\r\n      .subscribe((updatedLink) => {\r\n        this.updateNodePropertyEmitter.emit({\r\n          ...this.nodeProperty,\r\n          ...updatedLink,\r\n        });\r\n      });\r\n  }\r\n  getPropertyData(): void {\r\n    this.propertyService\r\n      .propertyDataChanges()\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((data) => {\r\n        if (data) {\r\n          if (Object.keys(data).length > 0) {\r\n            this.showPropertyForm = true;\r\n            this.nodeProperty = data;\r\n            this.isLinkNode = Boolean(\r\n              this.nodeProperty.category == GojsNodeCategory.Association\r\n            );\r\n            this.isAttributeNode = Boolean(\r\n              this.nodeProperty.category === GojsNodeCategory.Attribute ||\r\n                this.nodeProperty.category === GojsNodeCategory.Operation\r\n            );\r\n            if (\r\n              this.nodeProperty.category === GojsNodeCategory.Enumeration ||\r\n              this.nodeProperty.category === GojsNodeCategory.Class ||\r\n              this.nodeProperty.category === GojsNodeCategory.AssociativeClass\r\n            )\r\n              this.isGroupNode = true;\r\n            else this.isGroupNode = false;\r\n            this.initForm(data);\r\n          } else this.showPropertyForm = false;\r\n        } else this.showPropertyForm = false;\r\n      });\r\n  }\r\n  initForm(data: any): void {\r\n    const {\r\n      category,\r\n      name,\r\n      description,\r\n      volumetry,\r\n      tag,\r\n      color,\r\n      dataType,\r\n      fromComment,\r\n      toComment,\r\n    } = data;\r\n    if (\r\n      category === GojsNodeCategory.Operation ||\r\n      category === GojsNodeCategory.Attribute\r\n    ) {\r\n      const attributeOption = this.attributeTypes.find(\r\n        (option) => option.id === dataType.toString()\r\n      );\r\n      data.dataType = attributeOption\r\n        ? attributeOption.id\r\n        : `0_${AttributeType[AttributeType.Undefined]}`;\r\n    }\r\n\r\n    const createControl = (value: string) =>\r\n      new FormControl({ value, disabled: !this.haveEditAccess });\r\n\r\n    this.form = this.fb.group<PropertyForm>({\r\n      name: createControl(name),\r\n      description: createControl(description),\r\n      volumetry: createControl(volumetry),\r\n      tag: createControl(tag),\r\n      color: createControl(color),\r\n      dataType: createControl(dataType?.toString()),\r\n      category: createControl(category),\r\n      fromComment: createControl(fromComment),\r\n      toComment: createControl(toComment),\r\n    });\r\n\r\n    this.color = color;\r\n    this.form.valueChanges\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((res: PropertyFormData) => {\r\n        this.inputSubject.next(res);\r\n      });\r\n  }\r\n\r\n  changeColor(color: string) {\r\n    this.color = color;\r\n    this.form.controls['color'].setValue(color, { emitEvent: true });\r\n  }\r\n  onSelectionChange(event: any) {\r\n    this.form.controls['dataType'].setValue(event.value);\r\n  }\r\n\r\n  toggleColorPicker() {\r\n    this.showColorPicker = !this.showColorPicker;\r\n    this.colorPickerSelectEmitter.emit(this.showColorPicker);\r\n\r\n    // Ensure the color is properly initialized\r\n    if (this.showColorPicker && !this.color) {\r\n      // Set a default color if none is selected\r\n      this.color = 'rgba(229, 57, 53, 0.8)';\r\n      this.form.controls['color'].setValue(this.color, { emitEvent: false });\r\n    }\r\n  }\r\n\r\n  onColorSelected(color: string) {\r\n    this.color = color;\r\n    this.changeColor(color);\r\n    this.showColorPicker = false;\r\n\r\n    // Update the form control value without displaying it in the input\r\n    this.form.controls['color'].setValue(color, { emitEvent: false });\r\n  }\r\n\r\n  onCustomColorSelected(color: string) {\r\n    this.color = color;\r\n    this.changeColor(color);\r\n\r\n    // Update the form control value without displaying it in the input\r\n    this.form.controls['color'].setValue(color, { emitEvent: false });\r\n\r\n    // Note: We don't close the color picker here\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  handleDocumentClick(event: MouseEvent) {\r\n    // Close color picker when clicking outside the component\r\n    if (this.showColorPicker) {\r\n      const clickedInside = this.elementRef.nativeElement.contains(\r\n        event.target\r\n      );\r\n      if (!clickedInside) {\r\n        this.showColorPicker = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  getColorValues() {\r\n    return colorList.map((c) => c.value);\r\n  }\r\n\r\n  getColorGroups() {\r\n    const groups: { [key: string]: string[] } = {};\r\n    colorGroups.forEach((group) => {\r\n      groups[group.name] = group.colors.map((c) => c.value);\r\n    });\r\n    return groups;\r\n  }\r\n}\r\n\r\nexport interface PropertyForm {\r\n  name: FormControl<string | null>;\r\n  description: FormControl<string | null>;\r\n  volumetry: FormControl<string | null>;\r\n  tag: FormControl<string | null>;\r\n  color: FormControl<string | null>;\r\n  dataType: FormControl<string | null>;\r\n  category: FormControl<string | null>;\r\n  fromComment: FormControl<string | null>;\r\n  toComment: FormControl<string | null>;\r\n}\r\n", "<div class=\"properties\" *ngIf=\"showPropertyForm\">\r\n  <span class=\"property-title\">{{ \"diagram.properties\" | translate }}</span>\r\n  <form [formGroup]=\"form\" class=\"property-form\">\r\n    <div style=\"position: relative\">\r\n      <div class=\"property-field\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>{{ \"property.name\" | translate }}</mat-label>\r\n          <input\r\n            type=\"text\"\r\n            matInput\r\n            name=\"nodeName\"\r\n            formControlName=\"name\"\r\n            (value)=\"(form.value.name)\"\r\n            #nameInput\r\n            (click)=\"nameInput.select()\"\r\n          />\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"property-field\" *ngIf=\"form.value.dataType\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-select\r\n            (selectionChange)=\"onSelectionChange($event)\"\r\n            [(value)]=\"form.value.dataType\"\r\n          >\r\n            <mat-option *ngFor=\"let type of attributeTypes\" [value]=\"type.id\"\r\n              >{{ type.name }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"description-textArea\" *ngIf=\"isGroupNode || isAttributeNode\">\r\n        <mat-form-field appearance=\"outline\" class=\"description-field\">\r\n          <mat-label>{{ \"property.description\" | translate }}</mat-label>\r\n          <textarea matInput formControlName=\"description\" rows=\"2\"></textarea>\r\n        </mat-form-field>\r\n      </div>\r\n      <ng-container *ngIf=\"isGroupNode\">\r\n        <div class=\"property-field\">\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>{{ \"property.volumetry\" | translate }}</mat-label>\r\n            <input\r\n              type=\"text\"\r\n              matInput\r\n              formControlName=\"volumetry\"\r\n              (value)=\"(form.value.volumetry)\"\r\n            />\r\n          </mat-form-field>\r\n        </div>\r\n        <div class=\"property-field\">\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>{{ \"property.tag\" | translate }}</mat-label>\r\n            <input\r\n              type=\"text\"\r\n              matInput\r\n              formControlName=\"tag\"\r\n              (value)=\"(form.value.tag)\"\r\n            />\r\n          </mat-form-field>\r\n        </div>\r\n      </ng-container>\r\n      <div class=\"property-field\" *ngIf=\"isLinkNode\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>{{ \"property.fromComment\" | translate }}</mat-label>\r\n          <input\r\n            type=\"text\"\r\n            matInput\r\n            formControlName=\"fromComment\"\r\n            (value)=\"(form.value.fromComment)\"\r\n          />\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"property-field\" *ngIf=\"isLinkNode\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>{{ \"property.toComment\" | translate }}</mat-label>\r\n          <input\r\n            type=\"text\"\r\n            matInput\r\n            formControlName=\"toComment\"\r\n            (value)=\"(form.value.toComment)\"\r\n          />\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"property-field\" *ngIf=\"isGroupNode || isLinkNode\">\r\n        <mat-form-field appearance=\"outline\" class=\"color-field-form\">\r\n          <mat-label>{{ \"property.color\" | translate }}</mat-label>\r\n          <div class=\"color-field-container\">\r\n            <!-- Hidden input to store the color value -->\r\n            <input\r\n              matInput\r\n              [value]=\"color\"\r\n              style=\"display: none\"\r\n              formControlName=\"color\"\r\n              readonly=\"true\"\r\n            />\r\n            <!-- Simple color display -->\r\n            <div\r\n              class=\"color-display\"\r\n              [style.background-color]=\"color\"\r\n              (click)=\"toggleColorPicker()\"\r\n            ></div>\r\n          </div>\r\n        </mat-form-field>\r\n        <div *ngIf=\"showColorPicker\" class=\"color-picker-popup\">\r\n          <div class=\"color-picker-header\">\r\n            <span>{{ \"colorPicker.selectColor\" | translate }}</span>\r\n            <button class=\"close-button\" (click)=\"toggleColorPicker()\">\r\n              &times;\r\n            </button>\r\n          </div>\r\n          <app-color-groups\r\n            [selectedColor]=\"color\"\r\n            (colorSelected)=\"onColorSelected($event)\"\r\n            (customColorSelected)=\"onCustomColorSelected($event)\"\r\n          ></app-color-groups>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n  <ng-template #noProperty>\r\n    <p>{{ \"diagram.propertyDescription\" | translate }}</p>\r\n  </ng-template>\r\n</div>\r\n"], "mappings": "AAAA,SAGEA,YAAY,QAMP,eAAe;AACtB,SAAsBC,WAAW,QAAmB,gBAAgB;AAEpE,SAASC,OAAO,EAAEC,YAAY,EAAEC,SAAS,QAAQ,MAAM;AACvD,SAASC,WAAW,EAAEC,SAAS,QAAQ,oCAAoC;AAC3E,SAGEC,aAAa,QACR,gCAAgC;AAGvC,SAOEC,gBAAgB,QACX,2BAA2B;;;;;;;;;;;;;;;ICLtBC,EAAA,CAAAC,cAAA,qBACG;IAAAD,EAAA,CAAAE,MAAA,GACH;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,EAAA,CAAiB;IAC9DN,EAAA,CAAAO,SAAA,GACH;IADGP,EAAA,CAAAQ,kBAAA,KAAAH,QAAA,CAAAI,IAAA,MACH;;;;;;IARNT,EAAA,CAAAC,cAAA,aAAwD;IAGlDD,EAAA,CAAAU,UAAA,6BAAAC,gFAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAmBhB,EAAA,CAAAiB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAN,MAAA,CAAyB;IAAA,EAAC,yBAAAO,4EAAAP,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAApB,EAAA,CAAAgB,aAAA;MAAA,OAClChB,EAAA,CAAAiB,WAAA,CAAAG,OAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,GAAAX,MAAA,CAClB;IAAA,EAFoD;IAG7CZ,EAAA,CAAAwB,UAAA,IAAAC,sDAAA,yBAEa;IACfzB,EAAA,CAAAG,YAAA,EAAa;;;;IALXH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,UAAAsB,MAAA,CAAAL,IAAA,CAAAC,KAAA,CAAAC,QAAA,CAA+B;IAEFvB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAsB,MAAA,CAAAC,cAAA,CAAiB;;;;;IAMpD3B,EAAA,CAAAC,cAAA,cAAyE;IAE1DD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/DH,EAAA,CAAA4B,SAAA,mBAAqE;IACvE5B,EAAA,CAAAG,YAAA,EAAiB;;;IAFJH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,+BAAwC;;;;;;IAIvD9B,EAAA,CAAA+B,uBAAA,GAAkC;IAChC/B,EAAA,CAAAC,cAAA,aAA4B;IAEbD,EAAA,CAAAE,MAAA,GAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7DH,EAAA,CAAAC,cAAA,gBAKE;IADAD,EAAA,CAAAU,UAAA,mBAAAsB,0EAAA;MAAAhC,EAAA,CAAAa,aAAA,CAAAoB,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAiB,OAAA,CAAAb,IAAA,CAAAC,KAAA,CAAAa,SAAA,CAAoB;IAAA,EAAE;IAJlCnC,EAAA,CAAAG,YAAA,EAKE;IAGNH,EAAA,CAAAC,cAAA,aAA4B;IAEbD,EAAA,CAAAE,MAAA,IAAgC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvDH,EAAA,CAAAC,cAAA,iBAKE;IADAD,EAAA,CAAAU,UAAA,mBAAA0B,2EAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAoB,IAAA;MAAA,MAAAI,OAAA,GAAArC,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAoB,OAAA,CAAAhB,IAAA,CAAAC,KAAA,CAAAgB,GAAA,CAAc;IAAA,EAAE;IAJ5BtC,EAAA,CAAAG,YAAA,EAKE;IAGRH,EAAA,CAAAuC,qBAAA,EAAe;;;IApBEvC,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,6BAAsC;IAWtC9B,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,wBAAgC;;;;;;IAUjD9B,EAAA,CAAAC,cAAA,aAA+C;IAEhCD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/DH,EAAA,CAAAC,cAAA,gBAKE;IADAD,EAAA,CAAAU,UAAA,mBAAA8B,iEAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAyB,OAAA,CAAArB,IAAA,CAAAC,KAAA,CAAAqB,WAAA,CAAsB;IAAA,EAAE;IAJpC3C,EAAA,CAAAG,YAAA,EAKE;;;IANSH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,+BAAwC;;;;;;IASvD9B,EAAA,CAAAC,cAAA,aAA+C;IAEhCD,EAAA,CAAAE,MAAA,GAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7DH,EAAA,CAAAC,cAAA,gBAKE;IADAD,EAAA,CAAAU,UAAA,mBAAAkC,iEAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAA6B,OAAA,CAAAzB,IAAA,CAAAC,KAAA,CAAAyB,SAAA,CAAoB;IAAA,EAAE;IAJlC/C,EAAA,CAAAG,YAAA,EAKE;;;IANSH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,6BAAsC;;;;;;IA6BnD9B,EAAA,CAAAC,cAAA,cAAwD;IAE9CD,EAAA,CAAAE,MAAA,GAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,iBAA2D;IAA9BD,EAAA,CAAAU,UAAA,mBAAAsC,wEAAA;MAAAhD,EAAA,CAAAa,aAAA,CAAAoC,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAiC,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACxDnD,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,2BAIC;IAFCD,EAAA,CAAAU,UAAA,2BAAA0C,0FAAAxC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAoC,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAAgB,aAAA;MAAA,OAAiBhB,EAAA,CAAAiB,WAAA,CAAAoC,OAAA,CAAAC,eAAA,CAAA1C,MAAA,CAAuB;IAAA,EAAC,iCAAA2C,gGAAA3C,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAoC,IAAA;MAAA,MAAAO,OAAA,GAAAxD,EAAA,CAAAgB,aAAA;MAAA,OAClBhB,EAAA,CAAAiB,WAAA,CAAAuC,OAAA,CAAAC,qBAAA,CAAA7C,MAAA,CAA6B;IAAA,EADX;IAE1CZ,EAAA,CAAAG,YAAA,EAAmB;;;;IATZH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,kCAA2C;IAMjD9B,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,kBAAAsD,OAAA,CAAAC,KAAA,CAAuB;;;;;;IA5B7B3D,EAAA,CAAAC,cAAA,aAA8D;IAE/CD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzDH,EAAA,CAAAC,cAAA,cAAmC;IAEjCD,EAAA,CAAA4B,SAAA,gBAME;IAEF5B,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAU,UAAA,mBAAAkD,+DAAA;MAAA5D,EAAA,CAAAa,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAA6C,OAAA,CAAAX,iBAAA,EAAmB;IAAA,EAAC;IAC9BnD,EAAA,CAAAG,YAAA,EAAM;IAGXH,EAAA,CAAAwB,UAAA,IAAAuC,+CAAA,kBAYM;IACR/D,EAAA,CAAAG,YAAA,EAAM;;;;IA/BSH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,yBAAkC;IAKzC9B,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,UAAA4D,MAAA,CAAAL,KAAA,CAAe;IAQf3D,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAiE,WAAA,qBAAAD,MAAA,CAAAL,KAAA,CAAgC;IAKhC3D,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,SAAA4D,MAAA,CAAAE,eAAA,CAAqB;;;;;IAiB/BlE,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IAAnDH,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,sCAA+C;;;;;;IAvHtD9B,EAAA,CAAAC,cAAA,aAAiD;IAClBD,EAAA,CAAAE,MAAA,GAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1EH,EAAA,CAAAC,cAAA,cAA+C;IAI5BD,EAAA,CAAAE,MAAA,GAAiC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxDH,EAAA,CAAAC,cAAA,mBAQE;IAHAD,EAAA,CAAAU,UAAA,mBAAAyD,2DAAA;MAAAnE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAoD,OAAA,CAAAhD,IAAA,CAAAC,KAAA,CAAAb,IAAA,CAAe;IAAA,EAAE,mBAAA6D,2DAAA;MAAAtE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAG,GAAA,GAAAvE,EAAA,CAAAwE,WAAA;MAAA,OAElBxE,EAAA,CAAAiB,WAAA,CAAAsD,GAAA,CAAAE,MAAA,EAAkB;IAAA,EAFA;IAL7BzE,EAAA,CAAAG,YAAA,EAQE;IAGNH,EAAA,CAAAwB,UAAA,KAAAkD,yCAAA,iBAWM;IACN1E,EAAA,CAAAwB,UAAA,KAAAmD,yCAAA,kBAKM;IACN3E,EAAA,CAAAwB,UAAA,KAAAoD,kDAAA,4BAuBe;IACf5E,EAAA,CAAAwB,UAAA,KAAAqD,yCAAA,iBAUM;IACN7E,EAAA,CAAAwB,UAAA,KAAAsD,yCAAA,iBAUM;IACN9E,EAAA,CAAAwB,UAAA,KAAAuD,yCAAA,iBAiCM;IACR/E,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAwB,UAAA,KAAAwD,iDAAA,iCAAAhF,EAAA,CAAAiF,sBAAA,CAEc;IAChBjF,EAAA,CAAAG,YAAA,EAAM;;;;IAxHyBH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,6BAAsC;IAC7D9B,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,cAAA8E,MAAA,CAAA7D,IAAA,CAAkB;IAILrB,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,0BAAiC;IAYnB9B,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAA8E,MAAA,CAAA7D,IAAA,CAAAC,KAAA,CAAAC,QAAA,CAAyB;IAYnBvB,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAI,UAAA,SAAA8E,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAE,eAAA,CAAoC;IAMxDpF,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,SAAA8E,MAAA,CAAAC,WAAA,CAAiB;IAwBHnF,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAI,UAAA,SAAA8E,MAAA,CAAAG,UAAA,CAAgB;IAWhBrF,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAI,UAAA,SAAA8E,MAAA,CAAAG,UAAA,CAAgB;IAWhBrF,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAA8E,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAG,UAAA,CAA+B;;;ADpClE,OAAM,MAAOC,mBAAmB;EAkC9BC,YACUC,EAAe,EACfC,gBAAkC,EAClCC,kBAAsC,EACtCC,aAA4B,EAC5BC,YAA0B,EAC1BC,cAA8B,EAC9BC,eAAgC,EAChCC,iBAAoC,EACpCC,WAA+B,EAC/BC,YAA0B,EAC1BC,UAAsB,EACtBC,gBAAkC;IAXlC,KAAAX,EAAE,GAAFA,EAAE;IACF,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA3C1B,KAAAhB,WAAW,GAAY,KAAK;IAC5B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAgB,gBAAgB,GAAY,KAAK;IACjC,KAAAf,UAAU,GAAY,KAAK;IAC3B,KAAAnB,eAAe,GAAY,KAAK;IAChC,KAAAmC,aAAa,GAAa,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC;IAC5C,KAAAC,YAAY,GAAG,IAAI7G,OAAO,EAAoB;IAC9C,KAAA8G,QAAQ,GAAG,IAAI9G,OAAO,EAAQ;IACrB,KAAA+G,cAAc,GAAG,GAAG;IAC9B,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,iBAAiB,GAAQ,EAAE;IAQb,KAAAC,cAAc,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAW,CAAC;IAChB,KAAAC,yBAAyB,GAAG,IAAItH,YAAY,EAOzE;IAC8B,KAAAuH,wBAAwB,GACvD,IAAIvH,YAAY,EAAW;IACT,KAAAoC,cAAc,GAAsB,EAAE;IAexD,IAAI,CAAC8E,YAAY,GAAG,IAAI,CAACM,cAAc,EAAE;IACzC,IAAI,CAACL,iBAAiB,GAAG,IAAI,CAACM,cAAc,EAAE;EAChD;EACAC,QAAQA,CAAA;IACN,IAAI,CAACX,YAAY,CACdY,IAAI,CAACxH,YAAY,CAAC,IAAI,CAAC8G,cAAc,CAAC,EAAE7G,SAAS,CAAC,IAAI,CAAC4G,QAAQ,CAAC,CAAC,CACjEY,SAAS,CAAEC,UAAU,IAAI;MACxB,IAAIA,UAAU,CAAC3G,IAAI,CAAC4G,IAAI,EAAE,IAAI,EAAE,EAAE;QAChC,IAAI,CAACC,YAAY,GAAG;UAAE,GAAG,IAAI,CAACA,YAAY;UAAE,GAAGF;QAAU,CAMvC;QAClB,IACEA,UAAU,CAACG,QAAQ,IAAIxH,gBAAgB,CAACyH,KAAK,IAC7CJ,UAAU,CAACG,QAAQ,IAAIxH,gBAAgB,CAAC0H,gBAAgB,EACxD;UACA,IACE,IAAI,CAAC1B,iBAAiB,CAAC2B,sBAAsB,CAAC,IAAI,CAACJ,YAAY,CAAC,EAChE;YACA,IAAI,CAACK,mBAAmB,CAAC,IAAI,CAACL,YAAY,CAAC;;SAE9C,MAAM,IAAIF,UAAU,CAACG,QAAQ,IAAIxH,gBAAgB,CAAC6H,WAAW,EAAE;UAC9D,IACE,IAAI,CAAC7B,iBAAiB,CAAC8B,4BAA4B,CACjD,IAAI,CAACP,YAAY,CAClB,EACD;YACA,IAAI,CAACQ,yBAAyB,CAAC,IAAI,CAACR,YAAY,CAAC;;SAEpD,MAAM,IACLF,UAAU,CAACG,QAAQ,IAAIxH,gBAAgB,CAACgI,SAAS,IACjDX,UAAU,CAACG,QAAQ,IAAIxH,gBAAgB,CAACiI,SAAS,EACjD;UACA,IACE,IAAI,CAACjC,iBAAiB,CAACkC,0BAA0B,CAC/C,IAAI,CAACX,YAAY,CAClB,EACD;YACA,IAAI,CAACY,eAAe,CAAC,IAAI,CAACZ,YAAY,CAAC;;SAE1C,MAAM,IACLF,UAAU,CAACG,QAAQ,IAAIxH,gBAAgB,CAACoI,kBAAkB,EAC1D;UACA,IACE,IAAI,CAACpC,iBAAiB,CAACqC,wBAAwB,CAAC,IAAI,CAACd,YAAY,CAAC,EAClE;YACA,IAAI,CAACe,aAAa,CAAC,IAAI,CAACf,YAAY,CAAC;;SAExC,MAAM,IAAI,IAAI,CAACA,YAAY,CAACC,QAAQ,IAAIxH,gBAAgB,CAACuI,MAAM,EAAE;UAChE,IACE,IAAI,CAACvC,iBAAiB,CAACwC,uBAAuB,CAAC,IAAI,CAACjB,YAAY,CAAC,EAEjE,IAAI,CAACkB,YAAY,CAAC;YAChBlI,EAAE,EAAE,IAAI,CAACgH,YAAY,CAACmB,QAAQ;YAC9BhI,IAAI,EAAE2G,UAAU,CAAC3G,IAAI;YACrBiI,IAAI,EAAE,IAAI,CAACpB,YAAY,CAACoB;WACf,CAAC;SACf,MAAM,IACL,IAAI,CAACpB,YAAY,CAACC,QAAQ,IAAIxH,gBAAgB,CAAC4I,WAAW,EAC1D;UACA,IAAI,CAACC,cAAc,CAAC;YAClB,GAAG,IAAI,CAACtB,YAAY;YACpB7G,IAAI,EAAE2G,UAAU,CAAC3G,IAAI;YACrBkD,KAAK,EAAG,IAAI,CAAC2D,YAA6B,CAAC3D,KAAK;YAChDkF,eAAe,EAAG,IAAI,CAACvB,YAA6B,CACjDuB,eAAe;YAClBC,aAAa,EAAG,IAAI,CAACxB,YAA6B,CAACwB,aAAa;YAChEC,aAAa,EAAG,IAAI,CAACzB,YAA6B,CAACyB;WACpC,CAAC;;;IAGxB,CAAC,CAAC;IACJ,IAAI,CAACC,eAAe,EAAE;EACxB;EACAX,aAAaA,CAACf,YAAoC;IAChD,IAAI,CAACzB,cAAc,CAChBoD,wBAAwB,CAAC;MACxB3I,EAAE,EAAEgH,YAAY,CAAChH,EAAE;MACnBG,IAAI,EAAE6G,YAAY,CAAC7G,IAAI;MACvByI,GAAG,EAAE5B,YAAY,CAAC4B;KACR,CAAC,CACZ/B,SAAS,CAAEgC,GAAG,IAAI;MACjB,IAAI,CAACtC,yBAAyB,CAACuC,IAAI,CAAC;QAClC,GAAG9B,YAAY;QACf,GAAG6B;OACJ,CAAC;IACJ,CAAC,CAAC;EACN;EACArB,yBAAyBA,CAACV,UAAsC;IAC9D,IAAI,CAAC1B,kBAAkB,CACpB2D,qBAAqB,CACpB;MACE/I,EAAE,EAAE8G,UAAU,CAACkC,qBAAqB;MACpC7I,IAAI,EAAE2G,UAAU,CAAC3G,IAAI;MACrByI,GAAG,EAAE,IAAI,CAAC5B,YAAY,CAAC4B,GAAG;MAC1BR,IAAI,EAAEtB,UAAU,CAACsB,IAAI;MACrB/E,KAAK,EAAEyD,UAAU,CAACzD,KAAK;MACvB4F,WAAW,EAAEnC,UAAU,CAACmC,WAAW;MACnCpH,SAAS,EAAEiF,UAAU,CAACjF,SAAS;MAC/BG,GAAG,EAAE8E,UAAU,CAAC9E;KACjB,EACD,IAAI,CAACsE,gBAAgB,CACtB,CACAO,SAAS,CAAEgC,GAAG,IAAI;MACjB,IAAI,CAACtC,yBAAyB,CAACuC,IAAI,CAAC;QAClC,GAAG,IAAI,CAAC9B,YAAY;QACpB,GAAG6B;OACJ,CAAC;MACF,IAAI,CAAClD,YAAY,CAACuD,mBAAmB,CAACL,GAAG,CAAC7I,EAAE,EAAEmJ,QAAQ,EAAG,EAAEN,GAAG,CAAC1I,IAAI,CAAC;IACtE,CAAC,CAAC;EACN;EACAkH,mBAAmBA,CAAC+B,aAAmC;IACrD,IAAI,CAAC9D,YAAY,CACd+B,mBAAmB,CAClB;MACErH,EAAE,EAAEoJ,aAAa,CAACC,eAAe;MACjClJ,IAAI,EAAEiJ,aAAa,CAACjJ,IAAI;MACxByI,GAAG,EAAEQ,aAAa,EAAER,GAAG,EAAEO,QAAQ,EAAE,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDlB,IAAI,EAAEgB,aAAa,CAAChB,IAAI;MACxB/E,KAAK,EAAE+F,aAAa,CAAC/F,KAAK;MAC1BrB,GAAG,EAAEoH,aAAa,EAAEpH,GAAG;MACvBH,SAAS,EAAEuH,aAAa,EAAEvH,SAAS;MACnCoH,WAAW,EAAEG,aAAa,EAAEH,WAAW;MACvCM,aAAa,EACXH,aAAa,CAACnC,QAAQ,IAAIxH,gBAAgB,CAAC0H,gBAAgB,GACvD,IAAI,GACJ;KACP,EACD,IAAI,CAACb,gBAAgB,CACtB,CACAO,SAAS,CAAE2C,CAAC,IAAI;MACf,IAAI,CAACjD,yBAAyB,CAACuC,IAAI,CAAC;QAClC,GAAGM,aAAa;QAChBpJ,EAAE,EAAEoJ,aAAa,CAACC;OACnB,CAAC;IACJ,CAAC,CAAC;EACN;EACAzB,eAAeA,CAAC6B,SAAmC;IACjD,MAAMC,eAAe,GAAG,IAAI,CAACrI,cAAc,CAACsI,IAAI,CAC7CC,CAAC,IAAKA,CAAC,CAAC5J,EAAE,KAAKyJ,SAAS,CAACxI,QAAQ,CACnC;IACD,IAAI,CAACkE,gBAAgB,CAClByC,eAAe,CAAC;MACf5H,EAAE,EAAEyJ,SAAS,CAACzJ,EAAE;MAChBG,IAAI,EAAEsJ,SAAS,CAACtJ,IAAI;MACpB8I,WAAW,EAAEQ,SAAS,EAAER,WAAW;MACnChC,QAAQ,EAAEwC,SAAS,CAACI,UAAU;MAC9BC,aAAa,EAAE,CAACJ,eAAe,EAAEK,aAAa,GAC1C,IAAI,CAAC5E,gBAAgB,CAAC6E,qBAAqB,CACzCP,SAAS,CAACxI,QAAQ,CAACkI,QAAQ,EAAE,CAC9B,GACD3J,aAAa,CAACyK,SAAS;MAC3BjB,qBAAqB,EAAEU,eAAe,EAAEK,aAAa,GACjDL,eAAe,EAAE1J,EAAE,GACnB,CAAC;MACL4I,GAAG,EAAEa,SAAS,CAACb;KACH,CAAC,CACd/B,SAAS,CAAEqD,IAAI,IAAI;MAClB,IAAI,CAAC3D,yBAAyB,CAACuC,IAAI,CAAC;QAClC,GAAGW,SAAS;QACZxI,QAAQ,EACNiJ,IAAI,CAAClB,qBAAqB,IAAI,CAAC,GAC3B,GAAGkB,IAAI,CAACJ,aAAa,IAAItK,aAAa,CAAC0K,IAAI,CAACJ,aAAa,CAAC,EAAE,GAC5DI,IAAI,CAAClB,qBAAqB,EAAEG,QAAQ;OAC3C,CAAC;IACJ,CAAC,CAAC;EACN;EACAgB,WAAWA,CAAA;IACT,IAAI,CAAClE,QAAQ,CAACmE,IAAI,EAAE;IACpB,IAAI,CAACnE,QAAQ,CAACoE,QAAQ,EAAE;EAC1B;EACQnC,YAAYA,CAACoC,YAAoB;IACvC,IAAI,CAACjF,aAAa,CAAC6C,YAAY,CAACoC,YAAY,CAAC,CAACzD,SAAS,CAAE0D,aAAa,IAAI;MACxE,IAAI,CAAChE,yBAAyB,CAACuC,IAAI,CAAC;QAClC,GAAG,IAAI,CAAC9B,YAAY;QACpB,GAAGuD;OACJ,CAAC;IACJ,CAAC,CAAC;EACJ;EACAjC,cAAcA,CAACkC,QAAsB;IACnC,IAAI,CAAC9E,WAAW,CACb+E,UAAU,CAAC;MACVC,UAAU,EAAEF,QAAQ,CAACE,UAAU;MAC/BrH,KAAK,EAAEmH,QAAQ,CAACnH,KAAK;MACrBrD,EAAE,EAAE,CAACwK,QAAQ,EAAE5B,GAAI;MACnBzI,IAAI,EAAEqK,QAAQ,CAACrK,IAAI;MACnBkC,WAAW,EAAEmI,QAAQ,CAACnI,WAAW;MACjCI,SAAS,EAAE+H,QAAQ,CAAC/H,SAAS;MAC7BkI,iBAAiB,EAAEH,QAAQ,CAACG,iBAAiB;MAC7CC,sBAAsB,EAAEJ,QAAQ,CAACI,sBAAsB;MACvDnC,aAAa,EAAE+B,QAAQ,CAAC/B,aAAa;MACrCoC,QAAQ,EAAE;QACRC,SAAS,EAAE,IAAI,CAACxE,gBAAgB;QAChCyE,eAAe,EAAEP,QAAQ,CAACQ,MAAO;QACjCC,UAAU,EAAET,QAAQ,CAACU,QAAS;QAC9BC,MAAM,EAAEX,QAAQ,CAACxK,EAAG;QACpByI,aAAa,EAAE+B,QAAQ,CAAC/B;;KAE3B,CAAC,CACD5B,SAAS,CAAEuE,WAAW,IAAI;MACzB,IAAI,CAAC7E,yBAAyB,CAACuC,IAAI,CAAC;QAClC,GAAG,IAAI,CAAC9B,YAAY;QACpB,GAAGoE;OACJ,CAAC;IACJ,CAAC,CAAC;EACN;EACA1C,eAAeA,CAAA;IACb,IAAI,CAAClD,eAAe,CACjB6F,mBAAmB,EAAE,CACrBzE,IAAI,CAACvH,SAAS,CAAC,IAAI,CAAC4G,QAAQ,CAAC,CAAC,CAC9BY,SAAS,CAAEyE,IAAI,IAAI;MAClB,IAAIA,IAAI,EAAE;QACR,IAAIC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAAC3F,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAACkB,YAAY,GAAGsE,IAAI;UACxB,IAAI,CAACvG,UAAU,GAAG2G,OAAO,CACvB,IAAI,CAAC1E,YAAY,CAACC,QAAQ,IAAIxH,gBAAgB,CAAC4I,WAAW,CAC3D;UACD,IAAI,CAACvD,eAAe,GAAG4G,OAAO,CAC5B,IAAI,CAAC1E,YAAY,CAACC,QAAQ,KAAKxH,gBAAgB,CAACgI,SAAS,IACvD,IAAI,CAACT,YAAY,CAACC,QAAQ,KAAKxH,gBAAgB,CAACiI,SAAS,CAC5D;UACD,IACE,IAAI,CAACV,YAAY,CAACC,QAAQ,KAAKxH,gBAAgB,CAAC6H,WAAW,IAC3D,IAAI,CAACN,YAAY,CAACC,QAAQ,KAAKxH,gBAAgB,CAACyH,KAAK,IACrD,IAAI,CAACF,YAAY,CAACC,QAAQ,KAAKxH,gBAAgB,CAAC0H,gBAAgB,EAEhE,IAAI,CAACtC,WAAW,GAAG,IAAI,CAAC,KACrB,IAAI,CAACA,WAAW,GAAG,KAAK;UAC7B,IAAI,CAAC8G,QAAQ,CAACL,IAAI,CAAC;SACpB,MAAM,IAAI,CAACxF,gBAAgB,GAAG,KAAK;OACrC,MAAM,IAAI,CAACA,gBAAgB,GAAG,KAAK;IACtC,CAAC,CAAC;EACN;EACA6F,QAAQA,CAACL,IAAS;IAChB,MAAM;MACJrE,QAAQ;MACR9G,IAAI;MACJ8I,WAAW;MACXpH,SAAS;MACTG,GAAG;MACHqB,KAAK;MACLpC,QAAQ;MACRoB,WAAW;MACXI;IAAS,CACV,GAAG6I,IAAI;IACR,IACErE,QAAQ,KAAKxH,gBAAgB,CAACiI,SAAS,IACvCT,QAAQ,KAAKxH,gBAAgB,CAACgI,SAAS,EACvC;MACA,MAAMiC,eAAe,GAAG,IAAI,CAACrI,cAAc,CAACsI,IAAI,CAC7CiC,MAAM,IAAKA,MAAM,CAAC5L,EAAE,KAAKiB,QAAQ,CAACkI,QAAQ,EAAE,CAC9C;MACDmC,IAAI,CAACrK,QAAQ,GAAGyI,eAAe,GAC3BA,eAAe,CAAC1J,EAAE,GAClB,KAAKR,aAAa,CAACA,aAAa,CAACyK,SAAS,CAAC,EAAE;;IAGnD,MAAM4B,aAAa,GAAI7K,KAAa,IAClC,IAAI9B,WAAW,CAAC;MAAE8B,KAAK;MAAE8K,QAAQ,EAAE,CAAC,IAAI,CAACzF;IAAc,CAAE,CAAC;IAE5D,IAAI,CAACtF,IAAI,GAAG,IAAI,CAACmE,EAAE,CAAC6G,KAAK,CAAe;MACtC5L,IAAI,EAAE0L,aAAa,CAAC1L,IAAI,CAAC;MACzB8I,WAAW,EAAE4C,aAAa,CAAC5C,WAAW,CAAC;MACvCpH,SAAS,EAAEgK,aAAa,CAAChK,SAAS,CAAC;MACnCG,GAAG,EAAE6J,aAAa,CAAC7J,GAAG,CAAC;MACvBqB,KAAK,EAAEwI,aAAa,CAACxI,KAAK,CAAC;MAC3BpC,QAAQ,EAAE4K,aAAa,CAAC5K,QAAQ,EAAEkI,QAAQ,EAAE,CAAC;MAC7ClC,QAAQ,EAAE4E,aAAa,CAAC5E,QAAQ,CAAC;MACjC5E,WAAW,EAAEwJ,aAAa,CAACxJ,WAAW,CAAC;MACvCI,SAAS,EAAEoJ,aAAa,CAACpJ,SAAS;KACnC,CAAC;IAEF,IAAI,CAACY,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACtC,IAAI,CAACiL,YAAY,CACnBpF,IAAI,CAACvH,SAAS,CAAC,IAAI,CAAC4G,QAAQ,CAAC,CAAC,CAC9BY,SAAS,CAAEgC,GAAqB,IAAI;MACnC,IAAI,CAAC7C,YAAY,CAACoE,IAAI,CAACvB,GAAG,CAAC;IAC7B,CAAC,CAAC;EACN;EAEAoD,WAAWA,CAAC5I,KAAa;IACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACtC,IAAI,CAACmL,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC9I,KAAK,EAAE;MAAE+I,SAAS,EAAE;IAAI,CAAE,CAAC;EAClE;EACAxL,iBAAiBA,CAACyL,KAAU;IAC1B,IAAI,CAACtL,IAAI,CAACmL,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAACE,KAAK,CAACrL,KAAK,CAAC;EACtD;EAEA6B,iBAAiBA,CAAA;IACf,IAAI,CAACe,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAAC4C,wBAAwB,CAACsC,IAAI,CAAC,IAAI,CAAClF,eAAe,CAAC;IAExD;IACA,IAAI,IAAI,CAACA,eAAe,IAAI,CAAC,IAAI,CAACP,KAAK,EAAE;MACvC;MACA,IAAI,CAACA,KAAK,GAAG,wBAAwB;MACrC,IAAI,CAACtC,IAAI,CAACmL,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC9I,KAAK,EAAE;QAAE+I,SAAS,EAAE;MAAK,CAAE,CAAC;;EAE1E;EAEApJ,eAAeA,CAACK,KAAa;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4I,WAAW,CAAC5I,KAAK,CAAC;IACvB,IAAI,CAACO,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAAC7C,IAAI,CAACmL,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC9I,KAAK,EAAE;MAAE+I,SAAS,EAAE;IAAK,CAAE,CAAC;EACnE;EAEAjJ,qBAAqBA,CAACE,KAAa;IACjC,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4I,WAAW,CAAC5I,KAAK,CAAC;IAEvB;IACA,IAAI,CAACtC,IAAI,CAACmL,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC9I,KAAK,EAAE;MAAE+I,SAAS,EAAE;IAAK,CAAE,CAAC;IAEjE;EACF;EAGAE,mBAAmBA,CAACD,KAAiB;IACnC;IACA,IAAI,IAAI,CAACzI,eAAe,EAAE;MACxB,MAAM2I,aAAa,GAAG,IAAI,CAAC3G,UAAU,CAAC4G,aAAa,CAACC,QAAQ,CAC1DJ,KAAK,CAACK,MAAM,CACb;MACD,IAAI,CAACH,aAAa,EAAE;QAClB,IAAI,CAAC3I,eAAe,GAAG,KAAK;;;EAGlC;EAEA6C,cAAcA,CAAA;IACZ,OAAOlH,SAAS,CAACoN,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC5L,KAAK,CAAC;EACtC;EAEA0F,cAAcA,CAAA;IACZ,MAAMmG,MAAM,GAAgC,EAAE;IAC9CvN,WAAW,CAACwN,OAAO,CAAEf,KAAK,IAAI;MAC5Bc,MAAM,CAACd,KAAK,CAAC5L,IAAI,CAAC,GAAG4L,KAAK,CAACgB,MAAM,CAACJ,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC5L,KAAK,CAAC;IACvD,CAAC,CAAC;IACF,OAAO6L,MAAM;EACf;EAAC,QAAArD,CAAA,G;qBA1YUxE,mBAAmB,EAAAtF,EAAA,CAAAsN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxN,EAAA,CAAAsN,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1N,EAAA,CAAAsN,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAA5N,EAAA,CAAAsN,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAA9N,EAAA,CAAAsN,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAhO,EAAA,CAAAsN,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAlO,EAAA,CAAAsN,iBAAA,CAAAa,EAAA,CAAAC,eAAA,GAAApO,EAAA,CAAAsN,iBAAA,CAAAe,EAAA,CAAAC,iBAAA,GAAAtO,EAAA,CAAAsN,iBAAA,CAAAiB,EAAA,CAAAC,kBAAA,GAAAxO,EAAA,CAAAsN,iBAAA,CAAAmB,GAAA,CAAAC,YAAA,GAAA1O,EAAA,CAAAsN,iBAAA,CAAAtN,EAAA,CAAA2O,UAAA,GAAA3O,EAAA,CAAAsN,iBAAA,CAAAsB,GAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBxJ,mBAAmB;IAAAyJ,SAAA;IAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAnBC,GAAA,CAAAvC,mBAAA,CAAAhM,MAAA,CAA2B;QAAA,UAAAZ,EAAA,CAAAoP,iBAAA;;;;;;;;;;;;;;;;;;QC9CxCpP,EAAA,CAAAwB,UAAA,IAAA6N,kCAAA,mBAyHM;;;QAzHmBrP,EAAA,CAAAI,UAAA,SAAA+O,GAAA,CAAA/I,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}