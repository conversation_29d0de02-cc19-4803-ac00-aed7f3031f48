import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable } from 'rxjs';
import { Literal, LiteralDTO } from 'src/app/shared/model/literal';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class LiteralApiService {
  private backendUrl: string = environment.backEndUrl;
  constructor(private http: HttpClient) {}
  createEnumerationLiteral(literalObj: LiteralDTO): Observable<LiteralDTO> {
    return this.http
      .post<LiteralDTO>(this.backendUrl + `/EnumerationLiteral`, literalObj)
      .pipe(
        catchError((error) => {
          console.error('Error for creating literal:', error);
          throw error;
        })
      );
  }
  deleteEnumLiterals(enumLiteralIds: number[]): Observable<void> {
    return this.http
      .post<void>(
        this.backendUrl + `/EnumerationLiteral/deleteMultiple`,
        enumLiteralIds
      )
      .pipe(
        catchError((error) => {
          console.error('Error for deleting literal:', error);
          throw error;
        })
      );
  }
  updateEnumerationLiteral(literalObj: Literal): Observable<Literal> {
    return this.http
      .patch<Literal>(this.backendUrl + `/EnumerationLiteral`, literalObj)
      .pipe(
        catchError((error) => {
          console.error('Error for updating literal:', error);
          throw error;
        })
      );
  }

  /**
   * Alias methods to match DatabaseSyncService interface
   */
  createLiteral(literalObj: LiteralDTO): Observable<LiteralDTO> {
    return this.createEnumerationLiteral(literalObj);
  }

  updateLiteral(literalObj: Literal): Observable<Literal> {
    return this.updateEnumerationLiteral(literalObj);
  }

  deleteLiteral(enumLiteralIds: number[]): Observable<void> {
    return this.deleteEnumLiterals(enumLiteralIds);
  }

  /**
   * Undo literal deletion
   *
   * @param {number} idLiteral literal id
   * @returns {Observable<Literal>}
   *
   * @memberOf LiteralApiService
   */
  undoLiteralDelete(idLiteral: number): Observable<Literal> {
    return this.http
      .patch<Literal>(this.backendUrl + `/EnumerationLiteral/${idLiteral}`, {})
      .pipe(
        catchError((error) => {
          console.error('Error undo literal deletion:', error);
          throw error;
        })
      );
  }
}
