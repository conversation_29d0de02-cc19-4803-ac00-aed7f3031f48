import { Injectable, NgZone } from '@angular/core';
import { BehaviorSubject, Observable, Subject, fromEvent } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import * as go from 'gojs';
import { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';
import { PropertyService } from '../property/property.service';
import { TreeNodeService } from '../treeNode/tree-node.service';
import { DiagramUtils } from '../../../shared/utils/diagram-utils';

export interface UndoRedoState {
  canUndo: boolean;
  canRedo: boolean;
  undoText: string;
  redoText: string;
  isProcessing: boolean;
}

export interface UndoRedoOperation {
  id: string;
  type: 'database' | 'ui' | 'both';
  operation: () => Promise<void> | void;
  rollback: () => Promise<void> | void;
  description: string;
  timestamp: number;
}

export interface TransactionContext {
  id: string;
  name: string;
  operations: UndoRedoOperation[];
  isActive: boolean;
  startTime: number;
}

@Injectable({
  providedIn: 'root'
})
export class UndoRedoService {
  private _diagram: go.Diagram | null = null;
  private _stateSubject = new BehaviorSubject<UndoRedoState>({
    canUndo: false,
    canRedo: false,
    undoText: '',
    redoText: '',
    isProcessing: false
  });

  private _operationSubject = new Subject<{ type: 'undo' | 'redo', operation: any }>();
  private _pendingOperations: Map<string, UndoRedoOperation> = new Map();
  private _activeTransactions: Map<string, TransactionContext> = new Map();
  private _destroy$ = new Subject<void>();
  private _isInitialized = false;

  constructor(
    private gojsCommonService: GojsCommonService,
    private propertyService: PropertyService,
    private treeNodeService: TreeNodeService,
    private diagramUtils: DiagramUtils,
    private ngZone: NgZone
  ) {
    this.initializeService();
  }

  /**
   * Observable for undo/redo state changes
   */
  get state$(): Observable<UndoRedoState> {
    return this._stateSubject.asObservable();
  }

  /**
   * Observable for undo/redo operations
   */
  get operations$(): Observable<{ type: 'undo' | 'redo', operation: any }> {
    return this._operationSubject.asObservable();
  }

  /**
   * Initialize the service and set up diagram listeners
   */
  private initializeService(): void {
    if (this._isInitialized) return;

    this.gojsCommonService.gojsDiagramChanges()
      .pipe(takeUntil(this._destroy$))
      .subscribe(diagram => {
        if (diagram) {
          this.setDiagram(diagram);
        }
      });

    this.setupKeyboardShortcuts();
    this._isInitialized = true;
  }

  /**
   * Set up keyboard shortcuts for undo/redo
   */
  private setupKeyboardShortcuts(): void {
    fromEvent<KeyboardEvent>(document, 'keydown')
      .pipe(
        filter(event => (event.ctrlKey || event.metaKey) && !event.shiftKey),
        filter(event => event.key === 'z' || event.key === 'Z'),
        takeUntil(this._destroy$)
      )
      .subscribe(event => {
        event.preventDefault();
        this.undo();
      });

    fromEvent<KeyboardEvent>(document, 'keydown')
      .pipe(
        filter(event => (event.ctrlKey || event.metaKey) && event.shiftKey),
        filter(event => event.key === 'z' || event.key === 'Z'),
        takeUntil(this._destroy$)
      )
      .subscribe(event => {
        event.preventDefault();
        this.redo();
      });

    fromEvent<KeyboardEvent>(document, 'keydown')
      .pipe(
        filter(event => (event.ctrlKey || event.metaKey) && !event.shiftKey),
        filter(event => event.key === 'y' || event.key === 'Y'),
        takeUntil(this._destroy$)
      )
      .subscribe(event => {
        event.preventDefault();
        this.redo();
      });
  }

  /**
   * Set the current diagram and configure undo manager
   */
  setDiagram(diagram: go.Diagram): void {
    if (this._diagram) {
      this.removeListeners();
    }

    this._diagram = diagram;
    this.setupUndoManager();
    this.addListeners();
    this.updateState();
  }

  /**
   * Configure GoJS UndoManager with optimal settings
   */
  private setupUndoManager(): void {
    if (!this._diagram) return;

    const undoManager = this._diagram.undoManager;
    undoManager.isEnabled = true;
    undoManager.maxHistoryLength = 100; // Increased for better user experience
    
    // Configure what should be included in undo/redo
    undoManager.includesModel = true;
    undoManager.includesSelection = false; // Don't include selection changes
    undoManager.includesLayout = true; // Include layout changes
  }

  /**
   * Add event listeners for model changes and undo/redo operations
   */
  private addListeners(): void {
    if (!this._diagram) return;

    // Listen for model changes to update state
    this._diagram.addModelChangedListener(this.onModelChanged.bind(this));
    
    // Listen for undo manager changes
    this._diagram.undoManager.addChangedListener(this.onUndoManagerChanged.bind(this));
  }

  /**
   * Remove event listeners
   */
  private removeListeners(): void {
    if (!this._diagram) return;

    this._diagram.removeModelChangedListener(this.onModelChanged.bind(this));
    this._diagram.undoManager.removeChangedListener(this.onUndoManagerChanged.bind(this));
  }

  /**
   * Handle model changes
   */
  private onModelChanged(event: go.ChangedEvent): void {
    if (event.isTransactionFinished) {
      this.updateState();
      
      // Handle specific undo/redo events
      if (event.propertyName === 'FinishedUndo') {
        this.handleUndoOperation(event);
      } else if (event.propertyName === 'FinishedRedo') {
        this.handleRedoOperation(event);
      }
    }
  }

  /**
   * Handle undo manager changes
   */
  private onUndoManagerChanged(event: go.ChangedEvent): void {
    this.ngZone.run(() => {
      this.updateState();
    });
  }

  /**
   * Update the current undo/redo state
   */
  private updateState(): void {
    if (!this._diagram) return;

    const undoManager = this._diagram.undoManager;
    const state: UndoRedoState = {
      canUndo: undoManager.canUndo(),
      canRedo: undoManager.canRedo(),
      undoText: this.getUndoText(),
      redoText: this.getRedoText(),
      isProcessing: this._stateSubject.value.isProcessing
    };

    this._stateSubject.next(state);
  }

  /**
   * Get descriptive text for undo operation
   */
  private getUndoText(): string {
    if (!this._diagram?.undoManager.canUndo()) return '';
    
    const transaction = this._diagram.undoManager.transactionToUndo;
    return transaction?.name || 'Undo';
  }

  /**
   * Get descriptive text for redo operation
   */
  private getRedoText(): string {
    if (!this._diagram?.undoManager.canRedo()) return '';
    
    const transaction = this._diagram.undoManager.transactionToRedo;
    return transaction?.name || 'Redo';
  }

  /**
   * Perform undo operation
   */
  async undo(): Promise<void> {
    if (!this._diagram?.undoManager.canUndo() || this._stateSubject.value.isProcessing) return;

    this.setProcessingState(true);
    
    try {
      this._diagram.undoManager.undo();
      await this.refreshAngularState();
    } catch (error) {
      console.error('Error during undo operation:', error);
    } finally {
      this.setProcessingState(false);
    }
  }

  /**
   * Perform redo operation
   */
  async redo(): Promise<void> {
    if (!this._diagram?.undoManager.canRedo() || this._stateSubject.value.isProcessing) return;

    this.setProcessingState(true);
    
    try {
      this._diagram.undoManager.redo();
      await this.refreshAngularState();
    } catch (error) {
      console.error('Error during redo operation:', error);
    } finally {
      this.setProcessingState(false);
    }
  }

  /**
   * Set processing state
   */
  private setProcessingState(isProcessing: boolean): void {
    const currentState = this._stateSubject.value;
    this._stateSubject.next({
      ...currentState,
      isProcessing
    });
  }

  /**
   * Start a new transaction with a descriptive name
   */
  startTransaction(name: string): void {
    if (!this._diagram) return;
    this._diagram.startTransaction(name);
  }

  /**
   * Commit the current transaction
   */
  commitTransaction(name?: string): void {
    if (!this._diagram) return;
    this._diagram.commitTransaction(name);
  }

  /**
   * Rollback the current transaction
   */
  rollbackTransaction(): void {
    if (!this._diagram) return;
    this._diagram.rollbackTransaction();
  }

  /**
   * Execute an operation within a transaction
   */
  executeInTransaction<T>(name: string, operation: () => T): T {
    if (!this._diagram) throw new Error('No diagram available');

    this.startTransaction(name);
    try {
      const result = operation();
      this.commitTransaction(name);
      return result;
    } catch (error) {
      this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * Handle undo operation and refresh Angular state
   */
  private handleUndoOperation(event: go.ChangedEvent): void {
    this._operationSubject.next({ type: 'undo', operation: event });
  }

  /**
   * Handle redo operation and refresh Angular state
   */
  private handleRedoOperation(event: go.ChangedEvent): void {
    this._operationSubject.next({ type: 'redo', operation: event });
  }

  /**
   * Refresh Angular component state after undo/redo
   */
  private async refreshAngularState(): Promise<void> {
    return new Promise<void>((resolve) => {
      this.ngZone.run(() => {
        // Clear property panel
        this.propertyService.setPropertyData(null);
        
        // Refresh tree view
        this.treeNodeService.refreshTreeView();
        
        // Force change detection
        setTimeout(() => {
          this.updateState();
          resolve();
        }, 0);
      });
    });
  }

  /**
   * Clear undo/redo history
   */
  clearHistory(): void {
    if (!this._diagram) return;
    this._diagram.undoManager.clear();
    this.updateState();
  }

  /**
   * Get current undo/redo state
   */
  getCurrentState(): UndoRedoState {
    return this._stateSubject.value;
  }

  /**
   * Check if undo is available
   */
  canUndo(): boolean {
    return this._diagram?.undoManager.canUndo() || false;
  }

  /**
   * Check if redo is available
   */
  canRedo(): boolean {
    return this._diagram?.undoManager.canRedo() || false;
  }

  /**
   * Cleanup resources
   */
  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
    this.removeListeners();
  }
}
