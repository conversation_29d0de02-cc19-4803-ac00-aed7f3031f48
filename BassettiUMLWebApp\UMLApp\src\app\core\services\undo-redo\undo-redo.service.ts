import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import * as go from 'gojs';
import { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';
import { PropertyService } from '../property/property.service';
import { TreeNodeService } from '../treeNode/tree-node.service';
import { DiagramUtils } from '../../../shared/utils/diagram-utils';

export interface UndoRedoState {
  canUndo: boolean;
  canRedo: boolean;
  undoText: string;
  redoText: string;
}

export interface UndoRedoOperation {
  type: 'database' | 'ui' | 'both';
  operation: () => Promise<void> | void;
  rollback: () => Promise<void> | void;
  description: string;
}

@Injectable({
  providedIn: 'root'
})
export class UndoRedoService {
  private _diagram: go.Diagram | null = null;
  private _stateSubject = new BehaviorSubject<UndoRedoState>({
    canUndo: false,
    canRedo: false,
    undoText: '',
    redoText: ''
  });

  private _operationSubject = new Subject<{ type: 'undo' | 'redo', operation: any }>();
  private _pendingOperations: Map<string, UndoRedoOperation> = new Map();

  constructor(
    private gojsCommonService: GojsCommonService,
    private propertyService: PropertyService,
    private treeNodeService: TreeNodeService,
    private diagramUtils: DiagramUtils
  ) {
    this.initializeService();
  }

  /**
   * Observable for undo/redo state changes
   */
  get state$(): Observable<UndoRedoState> {
    return this._stateSubject.asObservable();
  }

  /**
   * Observable for undo/redo operations
   */
  get operations$(): Observable<{ type: 'undo' | 'redo', operation: any }> {
    return this._operationSubject.asObservable();
  }

  /**
   * Initialize the service and set up diagram listeners
   */
  private initializeService(): void {
    this.gojsCommonService.gojsDiagramChanges().subscribe(diagram => {
      if (diagram) {
        this.setDiagram(diagram);
      }
    });
  }

  /**
   * Set the current diagram and configure undo manager
   */
  setDiagram(diagram: go.Diagram): void {
    if (this._diagram) {
      this.removeListeners();
    }

    this._diagram = diagram;
    this.setupUndoManager();
    this.addListeners();
    this.updateState();
  }

  /**
   * Configure GoJS UndoManager with optimal settings
   */
  private setupUndoManager(): void {
    if (!this._diagram) return;

    const undoManager = this._diagram.undoManager;
    undoManager.isEnabled = true;
    undoManager.maxHistoryLength = 50; // Limit history for performance
    
    // Configure what should be included in undo/redo
    undoManager.includesModel = true;
    undoManager.includesSelection = false; // Don't include selection changes
  }

  /**
   * Add event listeners for model changes and undo/redo operations
   */
  private addListeners(): void {
    if (!this._diagram) return;

    // Listen for model changes to update state
    this._diagram.addModelChangedListener(this.onModelChanged.bind(this));
    
    // Listen for undo manager changes
    this._diagram.undoManager.addChangedListener(this.onUndoManagerChanged.bind(this));
  }

  /**
   * Remove event listeners
   */
  private removeListeners(): void {
    if (!this._diagram) return;

    this._diagram.removeModelChangedListener(this.onModelChanged.bind(this));
    this._diagram.undoManager.removeChangedListener(this.onUndoManagerChanged.bind(this));
  }

  /**
   * Handle model change events
   */
  private onModelChanged(event: go.ChangedEvent): void {
    if (event.isTransactionFinished) {
      this.updateState();
      
      // Handle specific undo/redo operations
      if (event.propertyName === 'FinishedUndo') {
        this.handleUndoOperation(event);
      } else if (event.propertyName === 'FinishedRedo') {
        this.handleRedoOperation(event);
      }
    }
  }

  /**
   * Handle undo manager change events
   */
  private onUndoManagerChanged(event: go.ChangedEvent): void {
    this.updateState();
  }

  /**
   * Update the current undo/redo state
   */
  private updateState(): void {
    if (!this._diagram) return;

    const undoManager = this._diagram.undoManager;
    const state: UndoRedoState = {
      canUndo: undoManager.canUndo(),
      canRedo: undoManager.canRedo(),
      undoText: this.getUndoText(),
      redoText: this.getRedoText()
    };

    this._stateSubject.next(state);
  }

  /**
   * Get descriptive text for undo operation
   */
  private getUndoText(): string {
    if (!this._diagram?.undoManager.canUndo()) return '';
    
    const transaction = this._diagram.undoManager.transactionToUndo;
    return transaction?.name || 'Undo';
  }

  /**
   * Get descriptive text for redo operation
   */
  private getRedoText(): string {
    if (!this._diagram?.undoManager.canRedo()) return '';
    
    const transaction = this._diagram.undoManager.transactionToRedo;
    return transaction?.name || 'Redo';
  }

  /**
   * Perform undo operation
   */
  undo(): void {
    if (!this._diagram?.undoManager.canUndo()) return;

    this._diagram.undoManager.undo();
    this.refreshAngularState();
  }

  /**
   * Perform redo operation
   */
  redo(): void {
    if (!this._diagram?.undoManager.canRedo()) return;

    this._diagram.undoManager.redo();
    this.refreshAngularState();
  }

  /**
   * Start a new transaction with a descriptive name
   */
  startTransaction(name: string): void {
    if (!this._diagram) return;
    this._diagram.startTransaction(name);
  }

  /**
   * Commit the current transaction
   */
  commitTransaction(name?: string): void {
    if (!this._diagram) return;
    this._diagram.commitTransaction(name);
  }

  /**
   * Rollback the current transaction
   */
  rollbackTransaction(): void {
    if (!this._diagram) return;
    this._diagram.rollbackTransaction();
  }

  /**
   * Execute an operation within a transaction
   */
  executeInTransaction<T>(name: string, operation: () => T): T {
    if (!this._diagram) throw new Error('No diagram available');

    this.startTransaction(name);
    try {
      const result = operation();
      this.commitTransaction(name);
      return result;
    } catch (error) {
      this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * Handle undo operation and refresh Angular state
   */
  private handleUndoOperation(event: go.ChangedEvent): void {
    this._operationSubject.next({ type: 'undo', operation: event });
    this.refreshAngularState();
  }

  /**
   * Handle redo operation and refresh Angular state
   */
  private handleRedoOperation(event: go.ChangedEvent): void {
    this._operationSubject.next({ type: 'redo', operation: event });
    this.refreshAngularState();
  }

  /**
   * Refresh Angular component state after undo/redo
   */
  private refreshAngularState(): void {
    // Clear property panel
    this.propertyService.setPropertyData(null);
    
    // Refresh tree view if needed
    this.treeNodeService.refreshTreeView();
    
    // Trigger change detection
    setTimeout(() => {
      // Force Angular change detection
      this.updateState();
    }, 0);
  }

  /**
   * Clear undo/redo history
   */
  clearHistory(): void {
    if (!this._diagram) return;
    this._diagram.undoManager.clear();
    this.updateState();
  }

  /**
   * Get current undo/redo state
   */
  getCurrentState(): UndoRedoState {
    return this._stateSubject.value;
  }

  /**
   * Check if undo is available
   */
  canUndo(): boolean {
    return this._diagram?.undoManager.canUndo() || false;
  }

  /**
   * Check if redo is available
   */
  canRedo(): boolean {
    return this._diagram?.undoManager.canRedo() || false;
  }
}
