{"ast": null, "code": "import { SelectionModel } from '@angular/cdk/collections';\nimport { NestedTreeControl } from '@angular/cdk/tree';\nimport { effect } from '@angular/core';\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\nimport { TreeNodeContextMenu } from 'src/app/shared/configs/contextMenuConfig';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { ClassWrapperCategory, DiagramWrapperCategory, EnumWrapperCategory } from 'src/app/shared/utils/constants';\nimport { DialogConfirmationComponent } from '../dialog-confirmation/dialog-confirmation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/treeNode/tree-node.service\";\nimport * as i3 from \"src/app/shared/utils/diagram-utils\";\nimport * as i4 from \"../../services/contextMenuAction/context-menu-action.service\";\nimport * as i5 from \"../../services/access/access.service\";\nimport * as i6 from \"../../services/property/property.service\";\nimport * as i7 from \"../../services/navbar/navbar.service\";\nimport * as i8 from \"@angular/router\";\nfunction LibraryTreeComponent_app_search_bar_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-search-bar\", 9);\n    i0.ɵɵlistener(\"searchChanged\", function LibraryTreeComponent_app_search_bar_0_Template_app_search_bar_searchChanged_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSearch($event));\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(1, 1, \"diagram.search\"));\n  }\n}\nfunction LibraryTreeComponent_div_1_mat_list_item_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 12);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_div_1_mat_list_item_5_Template_mat_list_item_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const diagram_r11 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.selectDiagramNode($event, diagram_r11));\n    });\n    i0.ɵɵelement(1, \"span\", 13);\n    i0.ɵɵelementStart(2, \"span\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"truncate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const diagram_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected-diagram\", diagram_r11.tag === ctx_r10.currentDiagramTag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", diagram_r11.icon, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r10.shouldShowTooltip(diagram_r11.name, 25) ? diagram_r11.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 5, diagram_r11.name, 25), \" \");\n  }\n}\nfunction LibraryTreeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-list\");\n    i0.ɵɵtemplate(5, LibraryTreeComponent_div_1_mat_list_item_5_Template, 5, 8, \"mat-list-item\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"diagram.diagramsList\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.searchResults);\n  }\n}\nfunction LibraryTreeComponent_div_2_mat_list_item_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const result_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r16.shouldShowTooltip(result_r15.name, 25) ? result_r15.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 2, result_r15.name, 25), \"\");\n  }\n}\nfunction LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 21);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(result_r15.name = $event);\n    })(\"blur\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const _r20 = i0.ɵɵreference(1);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.saveRename(result_r15, _r20.value));\n    })(\"keydown.enter\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const _r20 = i0.ɵɵreference(1);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.saveRename(result_r15, _r20.value));\n    })(\"keydown.escape\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.cancelRename(result_r15));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", result_r15.name);\n  }\n}\nfunction LibraryTreeComponent_div_2_mat_list_item_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 17);\n    i0.ɵɵlistener(\"contextmenu\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_contextmenu_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.onRightClick($event, result_r15));\n    })(\"dragstart\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dragstart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.onDragStart($event, result_r15));\n    })(\"click\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.selectDiagramNode($event, result_r15));\n    })(\"dragover\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dragover_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.onDragOver($event, result_r15));\n    })(\"drop\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.onMove($event, result_r15));\n    })(\"dblclick\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dblclick_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.enableRename(result_r15));\n    });\n    i0.ɵɵelement(1, \"span\", 13);\n    i0.ɵɵtemplate(2, LibraryTreeComponent_div_2_mat_list_item_2_ng_container_2_Template, 4, 5, \"ng-container\", 18);\n    i0.ɵɵtemplate(3, LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template, 2, 1, \"ng-template\", null, 19, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r15 = ctx.$implicit;\n    const _r17 = i0.ɵɵreference(4);\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r14.selection.isSelected(result_r15))(\"selected-diagram\", result_r15.tag === ctx_r14.currentDiagramTag);\n    i0.ɵɵproperty(\"draggable\", result_r15.isDraggable && ctx_r14.hasEditAccessOnly && ctx_r14.hasDiagram);\n    i0.ɵɵattribute(\"data-node-tag\", result_r15.tag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", result_r15.icon, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !result_r15.isRenaming)(\"ngIfElse\", _r17);\n  }\n}\nfunction LibraryTreeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-list\");\n    i0.ɵɵtemplate(2, LibraryTreeComponent_div_2_mat_list_item_2_Template, 5, 9, \"mat-list-item\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchResults);\n  }\n}\nfunction LibraryTreeComponent_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"p\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"diagram.notFound\"));\n  }\n}\nfunction LibraryTreeComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LibraryTreeComponent_ng_template_3_div_0_Template, 4, 3, \"div\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.searchResults.length === 0 && ctx_r4.isSearching && !ctx_r4.showOnlyDiagrams);\n  }\n}\nfunction LibraryTreeComponent_mat_tree_node_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r39 = i0.ɵɵnextContext().$implicit;\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r40.shouldShowTooltip(node_r39.name, 25) ? node_r39.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 2, node_r39.name, 25));\n  }\n}\nfunction LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 21);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(node_r39.name = $event);\n    })(\"blur\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const _r44 = i0.ɵɵreference(1);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.saveRename(node_r39, _r44.value));\n    })(\"keydown.enter\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const _r44 = i0.ɵɵreference(1);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.saveRename(node_r39, _r44.value));\n    })(\"keydown.escape\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.cancelRename(node_r39));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r39 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", node_r39.name);\n  }\n}\nfunction LibraryTreeComponent_mat_tree_node_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tree-node\", 24);\n    i0.ɵɵlistener(\"contextmenu\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_contextmenu_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onRightClick($event, node_r39));\n    })(\"dragstart\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dragstart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.onDragStart($event, node_r39));\n    })(\"click\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.selectDiagramNode($event, node_r39));\n    })(\"dragover\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dragover_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.onDragOver($event, node_r39));\n    })(\"drop\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.onMove($event, node_r39));\n    })(\"dblclick\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dblclick_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.enableRename(node_r39));\n    });\n    i0.ɵɵelementStart(1, \"span\", 25);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_mat_tree_node_7_Template_span_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.treeControl.toggle(node_r39));\n    });\n    i0.ɵɵelement(2, \"span\", 26);\n    i0.ɵɵtemplate(3, LibraryTreeComponent_mat_tree_node_7_ng_container_3_Template, 4, 5, \"ng-container\", 18);\n    i0.ɵɵtemplate(4, LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template, 2, 1, \"ng-template\", null, 19, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r39 = ctx.$implicit;\n    const _r41 = i0.ɵɵreference(5);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r5.selection.isSelected(node_r39))(\"selected-diagram\", node_r39.tag === ctx_r5.currentDiagramTag);\n    i0.ɵɵproperty(\"draggable\", node_r39.isDraggable && ctx_r5.hasEditAccessOnly && ctx_r5.hasDiagram);\n    i0.ɵɵattribute(\"data-node-tag\", node_r39.tag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", node_r39.icon, i0.ɵɵsanitizeHtml)(\"draggable\", node_r39.isDraggable && ctx_r5.hasEditAccessOnly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !node_r39.isRenaming)(\"ngIfElse\", _r41);\n  }\n}\nfunction LibraryTreeComponent_mat_nested_tree_node_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r63 = i0.ɵɵnextContext().$implicit;\n    const ctx_r64 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r64.shouldShowTooltip(node_r63.name, 25) ? node_r63.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 2, node_r63.name, 25));\n  }\n}\nfunction LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 21);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(node_r63.name = $event);\n    })(\"blur\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const _r68 = i0.ɵɵreference(1);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.saveRename(node_r63, _r68.value));\n    })(\"keydown.enter\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const _r68 = i0.ɵɵreference(1);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.saveRename(node_r63, _r68.value));\n    })(\"keydown.escape\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.cancelRename(node_r63));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r63 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", node_r63.name);\n  }\n}\nfunction LibraryTreeComponent_mat_nested_tree_node_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-nested-tree-node\", 27);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_mat_nested_tree_node_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.selectDiagramNode($event, node_r63));\n    });\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.treeControl.toggle(node_r63));\n    })(\"contextmenu\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_contextmenu_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.onRightClick($event, node_r63));\n    })(\"dragstart\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragstart_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onDragStart($event, node_r63));\n    })(\"dragenter\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragenter_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.onDragEnter($event, node_r63));\n    })(\"dragleave\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragleave_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onDragLeave($event, node_r63));\n    })(\"dragover\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragover_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.onDragOver($event, node_r63));\n    })(\"drop\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_drop_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r87 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r87.onMove($event, node_r63));\n    })(\"dblclick\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dblclick_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.enableRename(node_r63));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 30);\n    i0.ɵɵelement(5, \"span\", 26);\n    i0.ɵɵtemplate(6, LibraryTreeComponent_mat_nested_tree_node_8_ng_container_6_Template, 4, 5, \"ng-container\", 18);\n    i0.ɵɵtemplate(7, LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template, 2, 1, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 32);\n    i0.ɵɵelementContainer(10, 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r63 = ctx.$implicit;\n    const _r65 = i0.ɵɵreference(8);\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-node-tag\", node_r63.tag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"drop-target\", ctx_r6.isDraggedOver)(\"can-drop\", ctx_r6.isDraggedOver && ctx_r6.isValidDropTarget(node_r63))(\"cannot-drop\", ctx_r6.isDraggedOver && !ctx_r6.isValidDropTarget(node_r63))(\"dragging\", ctx_r6.isDragging && ctx_r6.draggedNode === node_r63)(\"selected\", ctx_r6.selection.isSelected(node_r63));\n    i0.ɵɵproperty(\"draggable\", node_r63.isDraggable && ctx_r6.hasEditAccessOnly && ctx_r6.hasDiagram);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", \"Toggle \" + node_r63.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.treeControl.isExpanded(node_r63) ? \"expand_more\" : \"chevron_right\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", node_r63.icon, i0.ɵɵsanitizeHtml)(\"draggable\", node_r63.isDraggable && ctx_r6.hasEditAccessOnly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !node_r63.isRenaming)(\"ngIfElse\", _r65);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"library-tree-invisible\", !ctx_r6.treeControl.isExpanded(node_r63));\n  }\n}\nfunction LibraryTreeComponent_ng_container_9_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_ng_container_9_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const option_r90 = restoredCtx.$implicit;\n      const ctx_r91 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r91.onAction(option_r90.actionName));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r90 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r90.label, \" \");\n  }\n}\nfunction LibraryTreeComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵtemplate(2, LibraryTreeComponent_ng_container_9_button_2_Template, 2, 1, \"button\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r7.getRightClickMenuStyle());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.contextMenuOptions);\n  }\n}\nexport class LibraryTreeComponent {\n  // Add host listener for delete key\n  onDeleteKey() {\n    if (this.hasEditAccessOnly && this.selection.selected.length > 0) {\n      this.deleteSelectedNodes();\n    }\n  }\n  set expandNodeTag(tag) {\n    if (tag) this.expandNodeByTag(tag);\n  }\n  constructor(_dialog, treeNodeService, diagramUtils, _contextMenuActionService, _accessService, _propertyService, _navbarService, _ngZone, router) {\n    this._dialog = _dialog;\n    this.treeNodeService = treeNodeService;\n    this.diagramUtils = diagramUtils;\n    this._contextMenuActionService = _contextMenuActionService;\n    this._accessService = _accessService;\n    this._propertyService = _propertyService;\n    this._navbarService = _navbarService;\n    this._ngZone = _ngZone;\n    this.router = router;\n    this.treeControl = new NestedTreeControl(node => node.children);\n    this.dataSource = new MatTreeNestedDataSource();\n    this.isDisplayContextMenu = false;\n    this.hasEditAccessOnly = false;\n    this.selectedTreeNode = null;\n    this.selectedNodes = new Set();\n    this.isRenaming = false;\n    this.previousName = '';\n    this.currentDiagramTag = '';\n    this.selection = new SelectionModel(true, []); // true enables multi-select\n    this.searchResults = [];\n    this.hasDiagram = true;\n    this.isSearching = false;\n    this.showOnlyDiagrams = false;\n    this.isDraggedOver = false;\n    this.isDragging = false;\n    this.currentDragTarget = null;\n    this.hasChild = (_, node) => {\n      return !!node.children && node.children.length > 0;\n    };\n    effect(() => {\n      const isRightPanelOpened = this._navbarService.showVersionHistory();\n      this._ngZone.run(() => {\n        this.toggleDiagramsView(isRightPanelOpened);\n      });\n    });\n  }\n  ngOnInit() {\n    this.treeNodeService.getLibraryDetails().subscribe(treeNode => {\n      if (treeNode) {\n        this.treeNodeData = treeNode;\n        this.dataSource.data = [];\n        this.dataSource.data = [treeNode];\n        const nodes = this.treeControl.getDescendants(treeNode);\n        this.treeNodeService.descendantTreeNodes = nodes;\n      }\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) {\n        this.hasDiagram = true;\n        this.currentDiagram = diagram;\n        this.currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${diagram.id}`;\n        // When a diagram is activated, expand the tree to show it\n        this.expandNodeByTag(this.currentDiagramTag);\n      } else {\n        this._propertyService.setPropertyData(null);\n        this.hasDiagram = false;\n      }\n    });\n    this._accessService.accessTypeChanges().subscribe(response => {\n      this.hasEditAccessOnly = response != AccessType.Viewer;\n    });\n  }\n  onSearch(searchTerm) {\n    if (searchTerm) {\n      this.isSearching = true;\n      this.searchResults = this.searchNodes(searchTerm);\n      this._propertyService.setPropertyData(null);\n    } else {\n      this.isSearching = false;\n      this.searchResults = [];\n    }\n  }\n  toggleDiagramsView(showOnlyDiagrams) {\n    this.showOnlyDiagrams = showOnlyDiagrams;\n    // If toggling to diagram-only view, populate the diagrams list\n    if (this.showOnlyDiagrams) {\n      this.populateDiagramsList();\n    }\n  }\n  populateDiagramsList() {\n    this.searchResults = this.getAllNodes().filter(node => node.category === GojsNodeCategory.Diagram);\n  }\n  // Search through nodes recursively\n  searchNodes(searchTerm, nodes = this.dataSource.data) {\n    let results = [];\n    for (const node of nodes) {\n      // Check if current node matches search\n      if (this.nodeFilterCriteria(node) && node.name.toLowerCase().includes(searchTerm.toLowerCase())) {\n        results.push(node);\n      }\n      // If node has children, search them too\n      if (node.children && node.children.length > 0) {\n        results = results.concat(this.searchNodes(searchTerm, node.children));\n      }\n    }\n    return results;\n  }\n  nodeFilterCriteria(node) {\n    return node.category !== ClassWrapperCategory && node.category !== EnumWrapperCategory && node.category !== DiagramWrapperCategory && node.category !== GojsNodeCategory.Project;\n  }\n  // Clear search and reset view\n  clearSearch() {\n    this.searchResults = [];\n    this._propertyService.setPropertyData(null);\n  }\n  // Handle selection of search result\n  selectSearchResult(event, node) {\n    this.selection.select(node);\n    this.selectDiagramNode(event, node);\n  }\n  // Method to delete selected nodes\n  deleteSelectedNodes() {\n    const selectedNodes = this.selection.selected;\n    if (selectedNodes.length === 0) return;\n    const dialogRef = this._dialog.open(DialogConfirmationComponent, {\n      width: '320px',\n      data: {\n        title: 'dialog.title',\n        reject: 'dialog.no',\n        confirm: 'dialog.yes'\n      }\n    });\n    dialogRef.afterClosed().subscribe(isConfirm => {\n      if (isConfirm) {\n        // Delete each selected node\n        this._contextMenuActionService.deleteSelectedNodes(selectedNodes);\n        // Clear selection after deletion\n        this.selection.clear();\n      }\n    });\n  }\n  canDiagramDraggable(node) {\n    if (node && node.category === GojsNodeCategory.Diagram) {\n      const parentFolderNode = this.treeNodeService.findParentNode(node.parentTag, this.treeNodeData);\n      if (parentFolderNode && parentFolderNode.category === GojsNodeCategory.Folder) return true;else return true;\n    }\n    return true;\n  }\n  onRightClick(event, node) {\n    event.stopPropagation();\n    event.preventDefault();\n    this._currentNode = node;\n    const contextMenu = TreeNodeContextMenu.find(menu => menu.category === node.category && menu.isDisplay);\n    // If the node is not already selected and we have multiple nodes selected,\n    // clear the selection and select only this node\n    if (this.selection.selected.length > 1 && !this.selection.isSelected(node)) {\n      this.selection.clear();\n      this.selection.select(node);\n    }\n    if (contextMenu) {\n      this.contextMenuOptions = this.selection.selected.length <= 1 ? contextMenu.options : contextMenu.options.filter(option => option.label == 'Delete');\n      this.isDisplayContextMenu = true;\n      this.rightClickMenuPositionX = event.clientX;\n      this.rightClickMenuPositionY = event.clientY;\n    }\n  }\n  documentClick() {\n    this.isDisplayContextMenu = false;\n    this.selection.clear();\n  }\n  getRightClickMenuStyle() {\n    return {\n      position: 'fixed',\n      left: `${this.rightClickMenuPositionX}px`,\n      top: `${this.rightClickMenuPositionY}px`\n    };\n  }\n  selectDiagramNode(event, treeNode) {\n    event.stopPropagation();\n    this.isDisplayContextMenu = false;\n    if (treeNode.category === ClassWrapperCategory || treeNode.category === EnumWrapperCategory || treeNode.category === DiagramWrapperCategory || treeNode.category === GojsNodeCategory.Project || !this.hasDiagram) {\n      this._propertyService.setPropertyData(null);\n      return;\n    }\n    this.selectedTreeNode = treeNode;\n    if (!this.isDiagram(treeNode.data) && treeNode.data) {\n      this._propertyService.setPropertyData(treeNode.data);\n      if (event.ctrlKey || event.metaKey) {\n        // Toggle selection for ctrl/cmd + click\n        this.selection.toggle(treeNode);\n      } else if (event.shiftKey && this.selection.selected.length > 0) {\n        // Handle shift + click for range selection\n        // This is a basic implementation - you might want to enhance it based on your needs\n        const lastSelected = this.selection.selected[this.selection.selected.length - 1];\n        const nodes = this.getAllNodes();\n        const startIdx = nodes.indexOf(lastSelected);\n        const endIdx = nodes.indexOf(treeNode);\n        const range = nodes.slice(Math.min(startIdx, endIdx), Math.max(startIdx, endIdx) + 1);\n        range.forEach(n => {\n          if (n.category === ClassWrapperCategory || n.category === EnumWrapperCategory || n.category === DiagramWrapperCategory || n.category === GojsNodeCategory.Project || n.category === GojsNodeCategory.Diagram) {\n            return;\n          }\n          this.selection.select(n);\n        });\n      } else {\n        // Single selection\n        this.selection.clear();\n        this.selection.select(treeNode);\n      }\n    }\n    if (treeNode.category === GojsNodeCategory.Diagram && this.currentDiagram.id !== treeNode.data?.id) {\n      this._propertyService.setPropertyData(null);\n      const diagram = treeNode.data;\n      this.diagramUtils.setActiveDiagram(diagram);\n      // Update the URL to include the selected diagram ID\n      const projectId = diagram.idProject;\n      const diagramId = diagram.id;\n      if (projectId && diagramId) {\n        this.router.navigate([`/editor/${projectId}/diagram/${diagramId}`], {\n          replaceUrl: true // Replace the current URL instead of adding a new history entry\n        });\n        // The expandNodeByTag will be called automatically when the diagram is activated\n        // through the activeDiagramChanges subscription in ngOnInit\n      }\n    }\n  }\n  // Helper method to get all nodes in a flat array\n  getAllNodes() {\n    const nodes = [];\n    const getNodesRecursively = nodesList => {\n      nodesList.forEach(node => {\n        nodes.push(node);\n        if (node.children) {\n          getNodesRecursively(node.children);\n        }\n      });\n    };\n    getNodesRecursively(this.dataSource.data);\n    return nodes;\n  }\n  isDiagram(data) {\n    return data.id !== undefined && data.idProject !== undefined && data.category === undefined;\n  }\n  onDragStart(event, node) {\n    if (event.dataTransfer) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(node));\n      this.draggedNode = node;\n      this.isDragging = true;\n      // Add a class to the dragged element\n      const element = event.target;\n      element.classList.add('dragging');\n    }\n  }\n  onAction(action) {\n    this._contextMenuActionService.executeAction(action, this._currentNode, this.selection);\n  }\n  onDragOver(event, node) {\n    event.preventDefault();\n    if (this.isValidDropTarget(node)) {\n      event.dataTransfer.dropEffect = 'move';\n    } else {\n      event.dataTransfer.dropEffect = 'none';\n    }\n  }\n  // Handle the node drop\n  onMove(event, targetNode) {\n    event.preventDefault();\n    this.isDraggedOver = false;\n    this.isDragging = false;\n    this.currentDragTarget = null;\n    if (this.isValidDropTarget(targetNode)) {\n      this.treeNodeService.moveNode(targetNode, this.draggedNode);\n    }\n  }\n  isSelected(node) {\n    return this.selectedNodes.has(node);\n  }\n  onRenameF2Key() {\n    if (this.hasEditAccessOnly && this.selectedTreeNode && this.hasDiagram) {\n      this.enableRename(this.selectedTreeNode);\n    }\n  }\n  canRename(node) {\n    if (node.category == DiagramWrapperCategory || node.category == ClassWrapperCategory || node.category == EnumWrapperCategory || node.category === GojsNodeCategory.Project) return false;else return true;\n  }\n  enableRename(node) {\n    if (!this.hasEditAccessOnly || !this.hasDiagram) return;\n    if (this.canRename(node)) {\n      node.isRenaming = true;\n      this.previousName = node.name;\n    }\n    // Set a slight timeout to focus on the input after it appears\n    setTimeout(() => {\n      const inputElement = document.querySelector('.rename-input');\n      if (inputElement && this.selectedTreeNode) {\n        inputElement.focus();\n        inputElement.select(); // Select text to allow easy overwrite\n      }\n    });\n  }\n  saveRename(node, newName) {\n    const trimmedName = newName.trim();\n    if (trimmedName === '' || trimmedName === this.previousName) {\n      // If input is empty or only whitespace, restore the previous name\n      node.name = this.previousName || node.name;\n      const inputElement = document.querySelector('.rename-input');\n      if (inputElement) {\n        inputElement.blur();\n      }\n    } else {\n      // Update with new name if valid\n      node.name = trimmedName;\n      this._contextMenuActionService.renameNode(node);\n    }\n    node.isRenaming = false;\n    this.previousName = '';\n  }\n  cancelRename(node) {\n    node.isRenaming = false;\n  }\n  shouldShowTooltip(name, length) {\n    return name.length > length;\n  }\n  onDragEnter(event, node) {\n    event.preventDefault();\n    this.currentDragTarget = node;\n    this.isDraggedOver = true;\n  }\n  onDragLeave(event, node) {\n    event.preventDefault();\n    if (this.currentDragTarget === node) {\n      this.isDraggedOver = false;\n      this.currentDragTarget = null;\n    }\n  }\n  isValidDropTarget(node) {\n    return this.draggedNode && node.supportingNodes?.includes(this.draggedNode.category) && !node.category.includes('Wrapper') && this.hasDiagram && this.canDiagramDraggable(this.draggedNode);\n  }\n  ngOnDestroy() {\n    this.isDraggedOver = false;\n    this.isDragging = false;\n    this.currentDragTarget = null;\n  }\n  /**\n   * Expands the tree to show a node with the given tag\n   * Also expands all parent nodes to make the node visible\n   * @param tag The tag of the node to expand to\n   */\n  expandNodeByTag(tag) {\n    const node = this.treeNodeService.findNodeByTag(tag);\n    if (node) {\n      // First expand all parent nodes to make this node visible\n      this.expandParentNodes(node);\n      // If the node itself has children, expand it too\n      if (this.hasChild(0, node) && !this.treeControl.isExpanded(node)) {\n        this.treeControl.expand(node);\n      }\n      // Scroll to the node to make it visible in the viewport\n      setTimeout(() => {\n        const nodeElement = document.querySelector(`[data-node-tag=\"${tag}\"]`);\n        if (nodeElement) {\n          nodeElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center'\n          });\n        }\n      }, 100);\n    }\n  }\n  /**\n   * Expands all parent nodes of the given node\n   * @param node The node whose parents should be expanded\n   */\n  expandParentNodes(node) {\n    // Get the parent chain from root to the node\n    const parentChain = this.getParentChain(node);\n    // Expand each parent in the chain\n    parentChain.forEach(parent => {\n      if (!this.treeControl.isExpanded(parent)) {\n        this.treeControl.expand(parent);\n      }\n    });\n  }\n  /**\n   * Gets the chain of parent nodes from root to the given node\n   * @param node The node to find parents for\n   * @returns Array of parent nodes in order from root to immediate parent\n   */\n  getParentChain(node) {\n    const parentChain = [];\n    let currentNode = node;\n    // Traverse up the tree until we reach the root\n    while (currentNode.parentTag) {\n      const parentNode = this.treeNodeService.findNodeByTag(currentNode.parentTag);\n      if (parentNode) {\n        parentChain.unshift(parentNode); // Add to beginning of array\n        currentNode = parentNode;\n      } else {\n        break;\n      }\n    }\n    return parentChain;\n  }\n  static #_ = this.ɵfac = function LibraryTreeComponent_Factory(t) {\n    return new (t || LibraryTreeComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.TreeNodeService), i0.ɵɵdirectiveInject(i3.DiagramUtils), i0.ɵɵdirectiveInject(i4.ContextMenuActionService), i0.ɵɵdirectiveInject(i5.AccessService), i0.ɵɵdirectiveInject(i6.PropertyService), i0.ɵɵdirectiveInject(i7.NavbarService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i8.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LibraryTreeComponent,\n    selectors: [[\"app-library-tree\"]],\n    hostBindings: function LibraryTreeComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keyup.delete\", function LibraryTreeComponent_keyup_delete_HostBindingHandler($event) {\n          return ctx.onDeleteKey($event);\n        }, false, i0.ɵɵresolveWindow)(\"click\", function LibraryTreeComponent_click_HostBindingHandler() {\n          return ctx.documentClick();\n        }, false, i0.ɵɵresolveDocument)(\"keyup.f2\", function LibraryTreeComponent_keyup_f2_HostBindingHandler() {\n          return ctx.onRenameF2Key();\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      expandNodeTag: \"expandNodeTag\"\n    },\n    decls: 10,\n    vars: 9,\n    consts: [[\"class\", \"search-container\", \"backgroundColor\", \"white\", 3, \"placeholder\", \"searchChanged\", 4, \"ngIf\"], [\"class\", \"diagrams-list-view\", 4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\", \"ngIfElse\"], [\"notFoundTemplate\", \"\"], [3, \"hidden\"], [1, \"library-tree\", 3, \"dataSource\", \"treeControl\"], [\"matTreeNodeToggle\", \"\", \"class\", \"tree-node node-content\", 3, \"selected\", \"selected-diagram\", \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\", 4, \"matTreeNodeDef\"], [\"class\", \"tree-node\", 3, \"click\", 4, \"matTreeNodeDef\", \"matTreeNodeDefWhen\"], [4, \"ngIf\"], [\"backgroundColor\", \"white\", 1, \"search-container\", 3, \"placeholder\", \"searchChanged\"], [1, \"diagrams-list-view\"], [\"class\", \"diagram-list-item\", 3, \"selected-diagram\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"diagram-list-item\", 3, \"click\"], [1, \"nodeIcon\", 3, \"innerHTML\"], [3, \"matTooltip\"], [1, \"search-results\"], [\"class\", \"search-result-item\", 3, \"selected\", \"selected-diagram\", \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-result-item\", 3, \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\"], [4, \"ngIf\", \"ngIfElse\"], [\"renameInput\", \"\"], [\"type\", \"text\", 1, \"rename-input\", 3, \"ngModel\", \"ngModelChange\", \"blur\", \"keydown.enter\", \"keydown.escape\"], [\"renameInputElement\", \"\"], [\"class\", \"search-results\", 4, \"ngIf\"], [1, \"not-found-text\"], [\"matTreeNodeToggle\", \"\", 1, \"tree-node\", \"node-content\", 3, \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\"], [1, \"node-content\", 3, \"click\"], [1, \"nodeIcon\", 3, \"innerHTML\", \"draggable\"], [1, \"tree-node\", 3, \"click\"], [1, \"mat-tree-node\", \"node-content\", 3, \"draggable\", \"click\", \"contextmenu\", \"dragstart\", \"dragenter\", \"dragleave\", \"dragover\", \"drop\", \"dblclick\"], [\"matTreeNodeToggle\", \"\"], [1, \"node-content\"], [\"nestedRenameInput\", \"\"], [\"role\", \"group\"], [\"matTreeNodeOutlet\", \"\"], [1, \"menu-link\", \"mat-elevation-z4\", 3, \"ngStyle\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-menu-item\", \"\", 3, \"click\"]],\n    template: function LibraryTreeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, LibraryTreeComponent_app_search_bar_0_Template, 2, 3, \"app-search-bar\", 0);\n        i0.ɵɵtemplate(1, LibraryTreeComponent_div_1_Template, 6, 4, \"div\", 1);\n        i0.ɵɵtemplate(2, LibraryTreeComponent_div_2_Template, 3, 1, \"div\", 2);\n        i0.ɵɵtemplate(3, LibraryTreeComponent_ng_template_3_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"mat-tree\", 5);\n        i0.ɵɵtemplate(7, LibraryTreeComponent_mat_tree_node_7_Template, 6, 10, \"mat-tree-node\", 6);\n        i0.ɵɵtemplate(8, LibraryTreeComponent_mat_nested_tree_node_8_Template, 11, 20, \"mat-nested-tree-node\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(9, LibraryTreeComponent_ng_container_9_Template, 3, 2, \"ng-container\", 8);\n      }\n      if (rf & 2) {\n        const _r3 = i0.ɵɵreference(4);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showOnlyDiagrams);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showOnlyDiagrams);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.searchResults.length > 0 && ctx.isSearching && !ctx.showOnlyDiagrams)(\"ngIfElse\", _r3);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"hidden\", ctx.isSearching || ctx.showOnlyDiagrams);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"dataSource\", ctx.dataSource)(\"treeControl\", ctx.treeControl);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matTreeNodeDefWhen\", ctx.hasChild);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isDisplayContextMenu && ctx.hasEditAccessOnly);\n      }\n    },\n    styles: [\".library-tree[_ngcontent-%COMP%] {\\n  padding-inline: 0.2rem;\\n  background-color: transparent;\\n  font-size: 13px;\\n  align-items: center;\\n  max-height: 300px;\\n}\\n.library-tree-invisible[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.library-tree[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .library-tree[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0;\\n  list-style-type: none;\\n}\\n.library-tree[_ngcontent-%COMP%]   .mat-nested-tree-node[_ngcontent-%COMP%]   div[role=group][_ngcontent-%COMP%] {\\n  padding-left: 0.9rem;\\n}\\n.library-tree[_ngcontent-%COMP%]   div[role=group][_ngcontent-%COMP%]    > .mat-tree-node[_ngcontent-%COMP%] {\\n  padding-left: 1.4rem;\\n  margin-top: 0.18rem;\\n}\\n.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-content[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n  transition: background-color 0.3s ease;\\n}\\n.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-content[_ngcontent-%COMP%]:hover {\\n  background-color: #e0e0e0;\\n}\\n.library-tree[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%] {\\n  background-color: lightblue;\\n}\\n.library-tree[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.08);\\n}\\n.library-tree[_ngcontent-%COMP%]   .selected-diagram[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.09);\\n}\\n\\n.mat-tree-node[_ngcontent-%COMP%] {\\n  min-height: 1.5rem;\\n  padding-bottom: 0.2rem;\\n  z-index: 100000;\\n}\\n\\n.nodeIcon[_ngcontent-%COMP%] {\\n  height: 13px;\\n  width: 13px;\\n  margin-right: 0.5rem;\\n  font-family: \\\"FontAwesome\\\";\\n}\\n\\n.rename-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background-color: #fff;\\n  border: 1px solid #0069b4;\\n  padding: 0.2rem;\\n  transition: box-shadow 0.3s ease;\\n}\\n\\n.menu-link[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 4px;\\n  font-size: 13px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n  position: absolute;\\n  z-index: 1000;\\n  cursor: pointer;\\n}\\n.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-height: -moz-fit-content;\\n  max-height: fit-content;\\n  height: 1rem;\\n  border: none;\\n  cursor: pointer;\\n}\\n.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background-color: #e0e0e0;\\n}\\n\\n.drag-root[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100vw;\\n  height: 100vh;\\n}\\n\\n.cdk-drag-preview[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  border-radius: 4px;\\n}\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n.cdk-drag-animating[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  top: 0;\\n  position: sticky;\\n}\\n\\n.search-results[_ngcontent-%COMP%] {\\n  max-height: 276px;\\n  overflow-y: auto;\\n  font-size: 13px;\\n}\\n\\n.search-result-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  height: 32px !important;\\n  transition: background-color 0.2s;\\n}\\n.search-result-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n.search-result-item.selected[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.08);\\n}\\n\\n.result-path[_ngcontent-%COMP%] {\\n  font-size: 0.85em;\\n  opacity: 0.7;\\n}\\n\\n.not-found-text[_ngcontent-%COMP%] {\\n  margin-block: 0.5rem;\\n  padding-inline: 0.7rem;\\n}\\n\\n.diagrams-list-view[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  margin-top: 8px;\\n  max-height: calc(100vh - 200px);\\n  overflow-y: auto;\\n  font-size: 14px;\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  margin: 0 0 8px 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.87);\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .mat-list[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 4px 16px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  height: 36px !important;\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item.selected-diagram[_ngcontent-%COMP%] {\\n  background-color: rgba(25, 118, 210, 0.12);\\n  color: #1976d2;\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   .nodeIcon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   .nodeIcon[_ngcontent-%COMP%]     svg {\\n  width: 20px;\\n  height: 20px;\\n}\\n.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n  .diagrams-list-view .mat-list-item-content {\\n  padding: 0 !important;\\n}\\n\\n.selected-diagram[_ngcontent-%COMP%] {\\n  background-color: rgba(25, 118, 210, 0.12);\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n\\n.drop-target[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.drop-target.can-drop[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n}\\n.drop-target.can-drop[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n}\\n.drop-target.cannot-drop[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.drop-target.cannot-drop[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 4px;\\n  pointer-events: none;\\n}\\n\\n.dragging[_ngcontent-%COMP%] {\\n  cursor: move;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["SelectionModel", "NestedTreeControl", "effect", "MatTreeNestedDataSource", "TreeNodeContextMenu", "GojsNodeCategory", "AccessType", "ClassWrapperCategory", "DiagramWrapperCategory", "EnumWrapperCategory", "DialogConfirmationComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "LibraryTreeComponent_app_search_bar_0_Template_app_search_bar_searchChanged_0_listener", "$event", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onSearch", "ɵɵelementEnd", "ɵɵpropertyInterpolate", "ɵɵpipeBind1", "LibraryTreeComponent_div_1_mat_list_item_5_Template_mat_list_item_click_0_listener", "restoredCtx", "_r13", "diagram_r11", "$implicit", "ctx_r12", "selectDiagramNode", "ɵɵelement", "ɵɵtext", "ɵɵclassProp", "tag", "ctx_r10", "currentDiagramTag", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵsanitizeHtml", "shouldShowTooltip", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ɵɵtemplate", "LibraryTreeComponent_div_1_mat_list_item_5_Template", "ɵɵtextInterpolate", "ctx_r1", "searchResults", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r16", "result_r15", "LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_ngModelChange_0_listener", "_r23", "LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_blur_0_listener", "_r20", "ɵɵreference", "ctx_r24", "save<PERSON><PERSON><PERSON>", "value", "LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_keydown_enter_0_listener", "ctx_r26", "LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_keydown_escape_0_listener", "ctx_r28", "cancelRename", "LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_contextmenu_0_listener", "_r32", "ctx_r31", "onRightClick", "LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dragstart_0_listener", "ctx_r33", "onDragStart", "LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_click_0_listener", "ctx_r34", "LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dragover_0_listener", "ctx_r35", "onDragOver", "LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_drop_0_listener", "ctx_r36", "onMove", "LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dblclick_0_listener", "ctx_r37", "enableRename", "LibraryTreeComponent_div_2_mat_list_item_2_ng_container_2_Template", "LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template", "ɵɵtemplateRefExtractor", "ctx_r14", "selection", "isSelected", "isDraggable", "hasEditAccessOnly", "hasDiagram", "ɵɵattribute", "isRenaming", "_r17", "LibraryTreeComponent_div_2_mat_list_item_2_Template", "ctx_r2", "LibraryTreeComponent_ng_template_3_div_0_Template", "ctx_r4", "length", "isSearching", "showOnlyDiagrams", "ctx_r40", "node_r39", "LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_ngModelChange_0_listener", "_r47", "LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_blur_0_listener", "_r44", "ctx_r48", "LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_keydown_enter_0_listener", "ctx_r50", "LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_keydown_escape_0_listener", "ctx_r52", "LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_contextmenu_0_listener", "_r56", "ctx_r55", "LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dragstart_0_listener", "ctx_r57", "LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_click_0_listener", "ctx_r58", "LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dragover_0_listener", "ctx_r59", "LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_drop_0_listener", "ctx_r60", "LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dblclick_0_listener", "ctx_r61", "LibraryTreeComponent_mat_tree_node_7_Template_span_click_1_listener", "ctx_r62", "treeControl", "toggle", "LibraryTreeComponent_mat_tree_node_7_ng_container_3_Template", "LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template", "ctx_r5", "_r41", "ctx_r64", "node_r63", "LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_ngModelChange_0_listener", "_r71", "LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_blur_0_listener", "_r68", "ctx_r72", "LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_keydown_enter_0_listener", "ctx_r74", "LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_keydown_escape_0_listener", "ctx_r76", "LibraryTreeComponent_mat_nested_tree_node_8_Template_mat_nested_tree_node_click_0_listener", "_r80", "ctx_r79", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_click_1_listener", "ctx_r81", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_contextmenu_1_listener", "ctx_r82", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragstart_1_listener", "ctx_r83", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragenter_1_listener", "ctx_r84", "onDragEnter", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragleave_1_listener", "ctx_r85", "onDragLeave", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragover_1_listener", "ctx_r86", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_drop_1_listener", "ctx_r87", "LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dblclick_1_listener", "ctx_r88", "LibraryTreeComponent_mat_nested_tree_node_8_ng_container_6_Template", "LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template", "ɵɵelementContainer", "ctx_r6", "isDraggedOver", "isValidDropTarget", "isDragging", "draggedNode", "isExpanded", "_r65", "LibraryTreeComponent_ng_container_9_button_2_Template_button_click_0_listener", "_r92", "option_r90", "ctx_r91", "onAction", "actionName", "label", "LibraryTreeComponent_ng_container_9_button_2_Template", "ctx_r7", "getRightClickMenuStyle", "contextMenuOptions", "LibraryTreeComponent", "onDeleteKey", "selected", "deleteSelectedNodes", "expandNodeTag", "expandNodeByTag", "constructor", "_dialog", "treeNodeService", "diagramUtils", "_contextMenuActionService", "_accessService", "_propertyService", "_navbarService", "_ngZone", "router", "node", "children", "dataSource", "isDisplayContextMenu", "selectedTreeNode", "selectedNodes", "Set", "previousName", "currentDragTarget", "<PERSON><PERSON><PERSON><PERSON>", "_", "isRightPanelOpened", "showVersionHistory", "run", "toggleDiagramsView", "ngOnInit", "getLibraryDetails", "subscribe", "treeNode", "treeNodeData", "data", "nodes", "getDescendants", "descendantTreeNodes", "activeDiagramChanges", "diagram", "currentDiagram", "Diagram", "id", "setPropertyData", "accessTypeChanges", "response", "Viewer", "searchTerm", "searchNodes", "populateDiagramsList", "getAllNodes", "filter", "category", "results", "nodeFilterCriteria", "toLowerCase", "includes", "push", "concat", "Project", "clearSearch", "selectSearchResult", "event", "select", "dialogRef", "open", "width", "title", "reject", "confirm", "afterClosed", "isConfirm", "clear", "canDiagramDraggable", "parentFolderNode", "findParentNode", "parentTag", "Folder", "stopPropagation", "preventDefault", "_currentNode", "contextMenu", "find", "menu", "isDisplay", "options", "option", "rightClickMenuPositionX", "clientX", "rightClickMenuPositionY", "clientY", "documentClick", "position", "left", "top", "isDiagram", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "lastSelected", "startIdx", "indexOf", "endIdx", "range", "slice", "Math", "min", "max", "for<PERSON>ach", "n", "setActiveDiagram", "projectId", "idProject", "diagramId", "navigate", "replaceUrl", "getNodesRecursively", "nodesList", "undefined", "dataTransfer", "setData", "JSON", "stringify", "element", "target", "classList", "add", "action", "executeAction", "dropEffect", "targetNode", "moveNode", "has", "onRenameF2Key", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "inputElement", "document", "querySelector", "focus", "newName", "trimmedName", "trim", "blur", "renameNode", "supportingNodes", "ngOnDestroy", "findNodeByTag", "expandParentNodes", "expand", "nodeElement", "scrollIntoView", "behavior", "block", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "currentNode", "parentNode", "unshift", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "TreeNodeService", "i3", "DiagramUtils", "i4", "ContextMenuActionService", "i5", "AccessService", "i6", "PropertyService", "i7", "NavbarService", "NgZone", "i8", "Router", "_2", "selectors", "hostBindings", "LibraryTreeComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "LibraryTreeComponent_click_HostBindingHandler", "ɵɵresolveDocument", "LibraryTreeComponent_keyup_f2_HostBindingHandler", "LibraryTreeComponent_app_search_bar_0_Template", "LibraryTreeComponent_div_1_Template", "LibraryTreeComponent_div_2_Template", "LibraryTreeComponent_ng_template_3_Template", "LibraryTreeComponent_mat_tree_node_7_Template", "LibraryTreeComponent_mat_nested_tree_node_8_Template", "LibraryTreeComponent_ng_container_9_Template", "_r3"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\library-tree\\library-tree.component.ts", "D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\library-tree\\library-tree.component.html"], "sourcesContent": ["import { SelectionModel } from '@angular/cdk/collections';\r\nimport { NestedTreeControl } from '@angular/cdk/tree';\r\nimport {\r\n  Component,\r\n  effect,\r\n  HostListener,\r\n  Input,\r\n  NgZone,\r\n  OnDestroy,\r\n  OnInit,\r\n} from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\r\nimport { Router } from '@angular/router';\r\nimport { TreeNodeContextMenu } from 'src/app/shared/configs/contextMenuConfig';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { ConfirmDialogData } from 'src/app/shared/model/dialog';\r\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\r\nimport { AccessType } from 'src/app/shared/model/project';\r\nimport {\r\n  ContextMenu,\r\n  ContextMenuAction,\r\n  TreeNode,\r\n} from 'src/app/shared/model/treeNode';\r\nimport {\r\n  ClassWrapperCategory,\r\n  DiagramWrapperCategory,\r\n  EnumWrapperCategory,\r\n} from 'src/app/shared/utils/constants';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../../services/access/access.service';\r\nimport { ContextMenuActionService } from '../../services/contextMenuAction/context-menu-action.service';\r\nimport { NavbarService } from '../../services/navbar/navbar.service';\r\nimport { PropertyService } from '../../services/property/property.service';\r\nimport { TreeNodeService } from '../../services/treeNode/tree-node.service';\r\nimport { DialogConfirmationComponent } from '../dialog-confirmation/dialog-confirmation.component';\r\n@Component({\r\n  selector: 'app-library-tree',\r\n  templateUrl: './library-tree.component.html',\r\n  styleUrls: ['./library-tree.component.scss'],\r\n})\r\nexport class LibraryTreeComponent implements OnInit, OnDestroy {\r\n  treeControl = new NestedTreeControl<TreeNode>((node) => node.children);\r\n  dataSource = new MatTreeNestedDataSource<TreeNode>();\r\n  isDisplayContextMenu: boolean = false;\r\n  rightClickMenuPositionX!: number;\r\n  rightClickMenuPositionY!: number;\r\n  currentDiagram!: Diagram;\r\n  private _currentNode!: TreeNode;\r\n  contextMenuOptions!: ContextMenu[];\r\n  hasEditAccessOnly: boolean = false;\r\n  draggedNode!: TreeNode;\r\n  treeNodeData!: TreeNode;\r\n  selectedTreeNode: TreeNode | null = null;\r\n  selectedNodes: Set<TreeNode> = new Set();\r\n  isRenaming: boolean = false;\r\n  previousName: string = '';\r\n  currentDiagramTag: string = '';\r\n  selection = new SelectionModel<TreeNode>(true, []); // true enables multi-select\r\n  searchResults: TreeNode[] = [];\r\n  hasDiagram: boolean = true;\r\n  isSearching: boolean = false;\r\n  showOnlyDiagrams: boolean = false;\r\n  isDraggedOver: boolean = false;\r\n  isDragging: boolean = false;\r\n  currentDragTarget: TreeNode | null = null;\r\n  // Add host listener for delete key\r\n  @HostListener('window:keyup.delete', ['$event'])\r\n  onDeleteKey() {\r\n    if (this.hasEditAccessOnly && this.selection.selected.length > 0) {\r\n      this.deleteSelectedNodes();\r\n    }\r\n  }\r\n  @Input() set expandNodeTag(tag: string) {\r\n    if (tag) this.expandNodeByTag(tag);\r\n  }\r\n\r\n  constructor(\r\n    private _dialog: MatDialog,\r\n    private treeNodeService: TreeNodeService,\r\n    private diagramUtils: DiagramUtils,\r\n    private _contextMenuActionService: ContextMenuActionService,\r\n    private _accessService: AccessService,\r\n    private _propertyService: PropertyService,\r\n    private _navbarService: NavbarService,\r\n    private _ngZone: NgZone,\r\n    private router: Router\r\n  ) {\r\n    effect(() => {\r\n      const isRightPanelOpened = this._navbarService.showVersionHistory();\r\n      this._ngZone.run(() => {\r\n        this.toggleDiagramsView(isRightPanelOpened);\r\n      });\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.treeNodeService.getLibraryDetails().subscribe((treeNode) => {\r\n      if (treeNode) {\r\n        this.treeNodeData = treeNode;\r\n        this.dataSource.data = [];\r\n        this.dataSource.data = [treeNode];\r\n        const nodes = this.treeControl.getDescendants(treeNode);\r\n        this.treeNodeService.descendantTreeNodes = nodes;\r\n      }\r\n    });\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) {\r\n        this.hasDiagram = true;\r\n        this.currentDiagram = diagram;\r\n        this.currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${diagram.id}`;\r\n\r\n        // When a diagram is activated, expand the tree to show it\r\n        this.expandNodeByTag(this.currentDiagramTag);\r\n      } else {\r\n        this._propertyService.setPropertyData(null);\r\n        this.hasDiagram = false;\r\n      }\r\n    });\r\n    this._accessService.accessTypeChanges().subscribe((response) => {\r\n      this.hasEditAccessOnly = response != AccessType.Viewer;\r\n    });\r\n  }\r\n\r\n  onSearch(searchTerm: string | null) {\r\n    if (searchTerm) {\r\n      this.isSearching = true;\r\n      this.searchResults = this.searchNodes(searchTerm);\r\n      this._propertyService.setPropertyData(null);\r\n    } else {\r\n      this.isSearching = false;\r\n      this.searchResults = [];\r\n    }\r\n  }\r\n\r\n  toggleDiagramsView(showOnlyDiagrams: boolean): void {\r\n    this.showOnlyDiagrams = showOnlyDiagrams;\r\n\r\n    // If toggling to diagram-only view, populate the diagrams list\r\n    if (this.showOnlyDiagrams) {\r\n      this.populateDiagramsList();\r\n    }\r\n  }\r\n\r\n  private populateDiagramsList(): void {\r\n    this.searchResults = this.getAllNodes().filter(\r\n      (node) => node.category === GojsNodeCategory.Diagram\r\n    );\r\n  }\r\n  // Search through nodes recursively\r\n  private searchNodes(\r\n    searchTerm: string,\r\n    nodes: TreeNode[] = this.dataSource.data\r\n  ): TreeNode[] {\r\n    let results: TreeNode[] = [];\r\n    for (const node of nodes) {\r\n      // Check if current node matches search\r\n      if (\r\n        this.nodeFilterCriteria(node) &&\r\n        node.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n      ) {\r\n        results.push(node);\r\n      }\r\n      // If node has children, search them too\r\n      if (node.children && node.children.length > 0) {\r\n        results = results.concat(this.searchNodes(searchTerm, node.children));\r\n      }\r\n    }\r\n    return results;\r\n  }\r\n\r\n  private nodeFilterCriteria(node: TreeNode): boolean {\r\n    return (\r\n      node.category !== ClassWrapperCategory &&\r\n      node.category !== EnumWrapperCategory &&\r\n      node.category !== DiagramWrapperCategory &&\r\n      node.category !== GojsNodeCategory.Project\r\n    );\r\n  }\r\n\r\n  // Clear search and reset view\r\n  clearSearch() {\r\n    this.searchResults = [];\r\n    this._propertyService.setPropertyData(null);\r\n  }\r\n\r\n  // Handle selection of search result\r\n  selectSearchResult(event: MouseEvent, node: TreeNode) {\r\n    this.selection.select(node);\r\n    this.selectDiagramNode(event, node);\r\n  }\r\n\r\n  // Method to delete selected nodes\r\n  deleteSelectedNodes() {\r\n    const selectedNodes = this.selection.selected;\r\n    if (selectedNodes.length === 0) return;\r\n    const dialogRef = this._dialog.open<\r\n      DialogConfirmationComponent,\r\n      ConfirmDialogData,\r\n      boolean\r\n    >(DialogConfirmationComponent, {\r\n      width: '320px',\r\n      data: {\r\n        title: 'dialog.title',\r\n        reject: 'dialog.no',\r\n        confirm: 'dialog.yes',\r\n      },\r\n    });\r\n    dialogRef.afterClosed().subscribe((isConfirm) => {\r\n      if (isConfirm) {\r\n        // Delete each selected node\r\n        this._contextMenuActionService.deleteSelectedNodes(selectedNodes);\r\n        // Clear selection after deletion\r\n        this.selection.clear();\r\n      }\r\n    });\r\n  }\r\n\r\n  hasChild = (_: number, node: TreeNode) => {\r\n    return !!node.children && node.children.length > 0;\r\n  };\r\n  canDiagramDraggable(node: TreeNode): boolean {\r\n    if (node && node.category === GojsNodeCategory.Diagram) {\r\n      const parentFolderNode = this.treeNodeService.findParentNode(\r\n        node.parentTag!,\r\n        this.treeNodeData\r\n      );\r\n      if (\r\n        parentFolderNode &&\r\n        parentFolderNode.category === GojsNodeCategory.Folder\r\n      )\r\n        return true;\r\n      else return true;\r\n    }\r\n    return true;\r\n  }\r\n  onRightClick(event: MouseEvent, node: TreeNode): void {\r\n    event.stopPropagation();\r\n    event.preventDefault();\r\n    this._currentNode = node;\r\n    const contextMenu = TreeNodeContextMenu.find(\r\n      (menu) => menu.category === node.category && menu.isDisplay\r\n    );\r\n\r\n    // If the node is not already selected and we have multiple nodes selected,\r\n    // clear the selection and select only this node\r\n    if (\r\n      this.selection.selected.length > 1 &&\r\n      !this.selection.isSelected(node)\r\n    ) {\r\n      this.selection.clear();\r\n      this.selection.select(node);\r\n    }\r\n\r\n    if (contextMenu) {\r\n      this.contextMenuOptions =\r\n        this.selection.selected.length <= 1\r\n          ? contextMenu.options!\r\n          : contextMenu.options!.filter((option) => option.label == 'Delete');\r\n      this.isDisplayContextMenu = true;\r\n      this.rightClickMenuPositionX = event.clientX;\r\n      this.rightClickMenuPositionY = event.clientY;\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click')\r\n  documentClick(): void {\r\n    this.isDisplayContextMenu = false;\r\n    this.selection.clear();\r\n  }\r\n\r\n  getRightClickMenuStyle() {\r\n    return {\r\n      position: 'fixed',\r\n      left: `${this.rightClickMenuPositionX}px`,\r\n      top: `${this.rightClickMenuPositionY}px`,\r\n    };\r\n  }\r\n\r\n  selectDiagramNode(event: MouseEvent, treeNode: TreeNode) {\r\n    event.stopPropagation();\r\n    this.isDisplayContextMenu = false;\r\n    if (\r\n      treeNode.category === ClassWrapperCategory ||\r\n      treeNode.category === EnumWrapperCategory ||\r\n      treeNode.category === DiagramWrapperCategory ||\r\n      treeNode.category === GojsNodeCategory.Project ||\r\n      !this.hasDiagram\r\n    ) {\r\n      this._propertyService.setPropertyData(null);\r\n      return;\r\n    }\r\n    this.selectedTreeNode = treeNode;\r\n    if (!this.isDiagram(treeNode.data) && treeNode.data) {\r\n      this._propertyService.setPropertyData(treeNode.data);\r\n      if (event.ctrlKey || event.metaKey) {\r\n        // Toggle selection for ctrl/cmd + click\r\n        this.selection.toggle(treeNode);\r\n      } else if (event.shiftKey && this.selection.selected.length > 0) {\r\n        // Handle shift + click for range selection\r\n        // This is a basic implementation - you might want to enhance it based on your needs\r\n        const lastSelected =\r\n          this.selection.selected[this.selection.selected.length - 1];\r\n        const nodes = this.getAllNodes();\r\n        const startIdx = nodes.indexOf(lastSelected);\r\n        const endIdx = nodes.indexOf(treeNode);\r\n        const range = nodes.slice(\r\n          Math.min(startIdx, endIdx),\r\n          Math.max(startIdx, endIdx) + 1\r\n        );\r\n        range.forEach((n) => {\r\n          if (\r\n            n.category === ClassWrapperCategory ||\r\n            n.category === EnumWrapperCategory ||\r\n            n.category === DiagramWrapperCategory ||\r\n            n.category === GojsNodeCategory.Project ||\r\n            n.category === GojsNodeCategory.Diagram\r\n          ) {\r\n            return;\r\n          }\r\n          this.selection.select(n);\r\n        });\r\n      } else {\r\n        // Single selection\r\n        this.selection.clear();\r\n        this.selection.select(treeNode);\r\n      }\r\n    }\r\n    if (\r\n      treeNode.category === GojsNodeCategory.Diagram &&\r\n      this.currentDiagram.id !== treeNode.data?.id\r\n    ) {\r\n      this._propertyService.setPropertyData(null);\r\n      const diagram = treeNode.data as Diagram;\r\n      this.diagramUtils.setActiveDiagram(diagram);\r\n\r\n      // Update the URL to include the selected diagram ID\r\n      const projectId = diagram.idProject;\r\n      const diagramId = diagram.id;\r\n      if (projectId && diagramId) {\r\n        this.router.navigate([`/editor/${projectId}/diagram/${diagramId}`], {\r\n          replaceUrl: true, // Replace the current URL instead of adding a new history entry\r\n        });\r\n\r\n        // The expandNodeByTag will be called automatically when the diagram is activated\r\n        // through the activeDiagramChanges subscription in ngOnInit\r\n      }\r\n    }\r\n  }\r\n\r\n  // Helper method to get all nodes in a flat array\r\n  private getAllNodes(): TreeNode[] {\r\n    const nodes: TreeNode[] = [];\r\n    const getNodesRecursively = (nodesList: TreeNode[]) => {\r\n      nodesList.forEach((node) => {\r\n        nodes.push(node);\r\n        if (node.children) {\r\n          getNodesRecursively(node.children);\r\n        }\r\n      });\r\n    };\r\n    getNodesRecursively(this.dataSource.data);\r\n    return nodes;\r\n  }\r\n\r\n  private isDiagram(data: any): data is Diagram {\r\n    return (\r\n      (data as Diagram).id !== undefined &&\r\n      (data as Diagram).idProject !== undefined &&\r\n      data.category === undefined\r\n    );\r\n  }\r\n\r\n  onDragStart(event: DragEvent, node: TreeNode) {\r\n    if (event.dataTransfer) {\r\n      event.dataTransfer.setData('text/plain', JSON.stringify(node));\r\n      this.draggedNode = node;\r\n      this.isDragging = true;\r\n\r\n      // Add a class to the dragged element\r\n      const element = event.target as HTMLElement;\r\n      element.classList.add('dragging');\r\n    }\r\n  }\r\n\r\n  onAction(action: ContextMenuAction): void {\r\n    this._contextMenuActionService.executeAction(\r\n      action,\r\n      this._currentNode,\r\n      this.selection\r\n    );\r\n  }\r\n\r\n  onDragOver(event: DragEvent, node: TreeNode) {\r\n    event.preventDefault();\r\n    if (this.isValidDropTarget(node)) {\r\n      event.dataTransfer!.dropEffect = 'move';\r\n    } else {\r\n      event.dataTransfer!.dropEffect = 'none';\r\n    }\r\n  }\r\n\r\n  // Handle the node drop\r\n  onMove(event: DragEvent, targetNode: TreeNode) {\r\n    event.preventDefault();\r\n    this.isDraggedOver = false;\r\n    this.isDragging = false;\r\n    this.currentDragTarget = null;\r\n\r\n    if (this.isValidDropTarget(targetNode)) {\r\n      this.treeNodeService.moveNode(targetNode, this.draggedNode);\r\n    }\r\n  }\r\n\r\n  isSelected(node: TreeNode): boolean {\r\n    return this.selectedNodes.has(node);\r\n  }\r\n  @HostListener('window:keyup.f2', [])\r\n  onRenameF2Key() {\r\n    if (this.hasEditAccessOnly && this.selectedTreeNode && this.hasDiagram) {\r\n      this.enableRename(this.selectedTreeNode);\r\n    }\r\n  }\r\n  canRename(node: TreeNode): boolean {\r\n    if (\r\n      node.category == DiagramWrapperCategory ||\r\n      node.category == ClassWrapperCategory ||\r\n      node.category == EnumWrapperCategory ||\r\n      node.category === GojsNodeCategory.Project\r\n    )\r\n      return false;\r\n    else return true;\r\n  }\r\n  enableRename(node: TreeNode) {\r\n    if (!this.hasEditAccessOnly || !this.hasDiagram) return;\r\n    if (this.canRename(node)) {\r\n      node.isRenaming = true;\r\n      this.previousName = node.name;\r\n    }\r\n    // Set a slight timeout to focus on the input after it appears\r\n    setTimeout(() => {\r\n      const inputElement = document.querySelector(\r\n        '.rename-input'\r\n      ) as HTMLInputElement;\r\n      if (inputElement && this.selectedTreeNode) {\r\n        inputElement.focus();\r\n        inputElement.select(); // Select text to allow easy overwrite\r\n      }\r\n    });\r\n  }\r\n\r\n  saveRename(node: TreeNode, newName: string) {\r\n    const trimmedName = newName.trim();\r\n\r\n    if (trimmedName === '' || trimmedName === this.previousName) {\r\n      // If input is empty or only whitespace, restore the previous name\r\n      node.name = this.previousName || node.name;\r\n      const inputElement = document.querySelector(\r\n        '.rename-input'\r\n      ) as HTMLInputElement;\r\n      if (inputElement) {\r\n        inputElement.blur();\r\n      }\r\n    } else {\r\n      // Update with new name if valid\r\n      node.name = trimmedName;\r\n      this._contextMenuActionService.renameNode(node);\r\n    }\r\n\r\n    node.isRenaming = false;\r\n    this.previousName = '';\r\n  }\r\n\r\n  cancelRename(node: TreeNode) {\r\n    node.isRenaming = false;\r\n  }\r\n  shouldShowTooltip(name: string, length: number): boolean {\r\n    return name.length > length;\r\n  }\r\n\r\n  onDragEnter(event: DragEvent, node: TreeNode) {\r\n    event.preventDefault();\r\n    this.currentDragTarget = node;\r\n    this.isDraggedOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent, node: TreeNode) {\r\n    event.preventDefault();\r\n    if (this.currentDragTarget === node) {\r\n      this.isDraggedOver = false;\r\n      this.currentDragTarget = null;\r\n    }\r\n  }\r\n\r\n  isValidDropTarget(node: TreeNode): boolean | undefined {\r\n    return (\r\n      this.draggedNode &&\r\n      node.supportingNodes?.includes(this.draggedNode.category) &&\r\n      !node.category.includes('Wrapper') &&\r\n      this.hasDiagram &&\r\n      this.canDiagramDraggable(this.draggedNode)\r\n    );\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.isDraggedOver = false;\r\n    this.isDragging = false;\r\n    this.currentDragTarget = null;\r\n  }\r\n  /**\r\n   * Expands the tree to show a node with the given tag\r\n   * Also expands all parent nodes to make the node visible\r\n   * @param tag The tag of the node to expand to\r\n   */\r\n  expandNodeByTag(tag: string) {\r\n    const node = this.treeNodeService.findNodeByTag(tag);\r\n    if (node) {\r\n      // First expand all parent nodes to make this node visible\r\n      this.expandParentNodes(node);\r\n\r\n      // If the node itself has children, expand it too\r\n      if (this.hasChild(0, node) && !this.treeControl.isExpanded(node)) {\r\n        this.treeControl.expand(node);\r\n      }\r\n\r\n      // Scroll to the node to make it visible in the viewport\r\n      setTimeout(() => {\r\n        const nodeElement = document.querySelector(`[data-node-tag=\"${tag}\"]`);\r\n        if (nodeElement) {\r\n          nodeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n        }\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Expands all parent nodes of the given node\r\n   * @param node The node whose parents should be expanded\r\n   */\r\n  private expandParentNodes(node: TreeNode) {\r\n    // Get the parent chain from root to the node\r\n    const parentChain = this.getParentChain(node);\r\n\r\n    // Expand each parent in the chain\r\n    parentChain.forEach((parent) => {\r\n      if (!this.treeControl.isExpanded(parent)) {\r\n        this.treeControl.expand(parent);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the chain of parent nodes from root to the given node\r\n   * @param node The node to find parents for\r\n   * @returns Array of parent nodes in order from root to immediate parent\r\n   */\r\n  private getParentChain(node: TreeNode): TreeNode[] {\r\n    const parentChain: TreeNode[] = [];\r\n    let currentNode = node;\r\n\r\n    // Traverse up the tree until we reach the root\r\n    while (currentNode.parentTag) {\r\n      const parentNode = this.treeNodeService.findNodeByTag(\r\n        currentNode.parentTag\r\n      );\r\n      if (parentNode) {\r\n        parentChain.unshift(parentNode); // Add to beginning of array\r\n        currentNode = parentNode;\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    return parentChain;\r\n  }\r\n}\r\n", "<app-search-bar\r\n  class=\"search-container\"\r\n  *ngIf=\"!showOnlyDiagrams\"\r\n  (searchChanged)=\"onSearch($event)\"\r\n  placeholder=\"{{ 'diagram.search' | translate }}\"\r\n  backgroundColor=\"white\"\r\n></app-search-bar>\r\n\r\n<!-- Diagrams List View (when toggled) -->\r\n<div *ngIf=\"showOnlyDiagrams\" class=\"diagrams-list-view\">\r\n  <h3>{{ \"diagram.diagramsList\" | translate }}</h3>\r\n  <!-- <h3>{{ \"Diagrams\" | translate }}</h3> -->\r\n  <mat-list>\r\n    <mat-list-item\r\n      *ngFor=\"let diagram of searchResults\"\r\n      class=\"diagram-list-item\"\r\n      [class.selected-diagram]=\"diagram.tag === currentDiagramTag\"\r\n      (click)=\"selectDiagramNode($event, diagram)\"\r\n    >\r\n      <span class=\"nodeIcon\" [innerHTML]=\"diagram.icon\"></span>\r\n      <span\r\n        [matTooltip]=\"shouldShowTooltip(diagram.name, 25) ? diagram.name : ''\"\r\n      >\r\n        {{ diagram.name | truncate : 25 }}\r\n      </span>\r\n    </mat-list-item>\r\n  </mat-list>\r\n</div>\r\n\r\n<!-- Search Results List View -->\r\n<div\r\n  *ngIf=\"\r\n    searchResults.length > 0 && isSearching && !showOnlyDiagrams;\r\n    else notFoundTemplate\r\n  \"\r\n  class=\"search-results\"\r\n>\r\n  <mat-list>\r\n    <mat-list-item\r\n      *ngFor=\"let result of searchResults\"\r\n      class=\"search-result-item\"\r\n      [class.selected]=\"selection.isSelected(result)\"\r\n      [class.selected-diagram]=\"result.tag === currentDiagramTag\"\r\n      (contextmenu)=\"onRightClick($event, result)\"\r\n      [draggable]=\"result.isDraggable && hasEditAccessOnly && hasDiagram\"\r\n      (dragstart)=\"onDragStart($event, result)\"\r\n      (click)=\"selectDiagramNode($event, result)\"\r\n      (dragover)=\"onDragOver($event, result)\"\r\n      (drop)=\"onMove($event, result)\"\r\n      (dblclick)=\"enableRename(result)\"\r\n      [attr.data-node-tag]=\"result.tag\"\r\n    >\r\n      <span class=\"nodeIcon\" [innerHTML]=\"result.icon\"></span>\r\n      <ng-container *ngIf=\"!result.isRenaming; else renameInput\">\r\n        <span\r\n          [matTooltip]=\"shouldShowTooltip(result.name, 25) ? result.name : ''\"\r\n        >\r\n          {{ result.name | truncate : 25 }}</span\r\n        >\r\n      </ng-container>\r\n      <ng-template #renameInput>\r\n        <input\r\n          type=\"text\"\r\n          [(ngModel)]=\"result.name\"\r\n          (blur)=\"saveRename(result, renameInputElement.value)\"\r\n          (keydown.enter)=\"saveRename(result, renameInputElement.value)\"\r\n          (keydown.escape)=\"cancelRename(result)\"\r\n          class=\"rename-input\"\r\n          #renameInputElement\r\n        />\r\n      </ng-template>\r\n    </mat-list-item>\r\n  </mat-list>\r\n</div>\r\n<ng-template #notFoundTemplate>\r\n  <div\r\n    *ngIf=\"searchResults.length === 0 && isSearching && !showOnlyDiagrams\"\r\n    class=\"search-results\"\r\n  >\r\n    <p class=\"not-found-text\">{{ \"diagram.notFound\" | translate }}</p>\r\n  </div>\r\n</ng-template>\r\n\r\n<!-- Original Tree View (shown when not searching) -->\r\n<div [hidden]=\"isSearching || showOnlyDiagrams\">\r\n  <mat-tree\r\n    [dataSource]=\"dataSource\"\r\n    [treeControl]=\"treeControl\"\r\n    class=\"library-tree\"\r\n  >\r\n    <!-- Leaf node -->\r\n    <mat-tree-node\r\n      *matTreeNodeDef=\"let node\"\r\n      matTreeNodeToggle\r\n      class=\"tree-node node-content\"\r\n      [class.selected]=\"selection.isSelected(node)\"\r\n      [class.selected-diagram]=\"node.tag === currentDiagramTag\"\r\n      (contextmenu)=\"onRightClick($event, node)\"\r\n      [draggable]=\"node.isDraggable && hasEditAccessOnly && hasDiagram\"\r\n      (dragstart)=\"onDragStart($event, node)\"\r\n      (click)=\"selectDiagramNode($event, node)\"\r\n      (dragover)=\"onDragOver($event, node)\"\r\n      (drop)=\"onMove($event, node)\"\r\n      (dblclick)=\"enableRename(node)\"\r\n      [attr.data-node-tag]=\"node.tag\"\r\n    >\r\n      <span (click)=\"treeControl.toggle(node)\" class=\"node-content\">\r\n        <span\r\n          class=\"nodeIcon\"\r\n          [innerHTML]=\"node.icon\"\r\n          [draggable]=\"node.isDraggable && hasEditAccessOnly\"\r\n        ></span>\r\n        <ng-container *ngIf=\"!node.isRenaming; else renameInput\">\r\n          <span\r\n            [matTooltip]=\"shouldShowTooltip(node.name, 25) ? node.name : ''\"\r\n            >{{ node.name | truncate : 25 }}</span\r\n          >\r\n        </ng-container>\r\n        <ng-template #renameInput>\r\n          <input\r\n            type=\"text\"\r\n            [(ngModel)]=\"node.name\"\r\n            (blur)=\"saveRename(node, renameInputElement.value)\"\r\n            (keydown.enter)=\"saveRename(node, renameInputElement.value)\"\r\n            (keydown.escape)=\"cancelRename(node)\"\r\n            class=\"rename-input\"\r\n            #renameInputElement\r\n          />\r\n        </ng-template>\r\n      </span>\r\n    </mat-tree-node>\r\n\r\n    <!-- Nested node -->\r\n    <mat-nested-tree-node\r\n      *matTreeNodeDef=\"let node; when: hasChild\"\r\n      class=\"tree-node\"\r\n      (click)=\"selectDiagramNode($event, node)\"\r\n      [attr.data-node-tag]=\"node.tag\"\r\n    >\r\n      <div\r\n        class=\"mat-tree-node node-content\"\r\n        [class.drop-target]=\"isDraggedOver\"\r\n        [class.can-drop]=\"isDraggedOver && isValidDropTarget(node)\"\r\n        [class.cannot-drop]=\"isDraggedOver && !isValidDropTarget(node)\"\r\n        [class.dragging]=\"isDragging && draggedNode === node\"\r\n        (click)=\"treeControl.toggle(node)\"\r\n        [class.selected]=\"selection.isSelected(node)\"\r\n        (contextmenu)=\"onRightClick($event, node)\"\r\n        [draggable]=\"node.isDraggable && hasEditAccessOnly && hasDiagram\"\r\n        (dragstart)=\"onDragStart($event, node)\"\r\n        (dragenter)=\"onDragEnter($event, node)\"\r\n        (dragleave)=\"onDragLeave($event, node)\"\r\n        (dragover)=\"onDragOver($event, node)\"\r\n        (drop)=\"onMove($event, node)\"\r\n        (dblclick)=\"enableRename(node)\"\r\n      >\r\n        <mat-icon matTreeNodeToggle [attr.aria-label]=\"'Toggle ' + node.name\">\r\n          {{ treeControl.isExpanded(node) ? \"expand_more\" : \"chevron_right\" }}\r\n        </mat-icon>\r\n\r\n        <span class=\"node-content\">\r\n          <span\r\n            class=\"nodeIcon\"\r\n            [innerHTML]=\"node.icon\"\r\n            [draggable]=\"node.isDraggable && hasEditAccessOnly\"\r\n          ></span>\r\n          <ng-container *ngIf=\"!node.isRenaming; else nestedRenameInput\">\r\n            <span\r\n              [matTooltip]=\"shouldShowTooltip(node.name, 25) ? node.name : ''\"\r\n              >{{ node.name | truncate : 25 }}</span\r\n            >\r\n          </ng-container>\r\n          <ng-template #nestedRenameInput>\r\n            <input\r\n              type=\"text\"\r\n              [(ngModel)]=\"node.name\"\r\n              (blur)=\"saveRename(node, renameInputElement.value)\"\r\n              (keydown.enter)=\"saveRename(node, renameInputElement.value)\"\r\n              (keydown.escape)=\"cancelRename(node)\"\r\n              class=\"rename-input\"\r\n              #renameInputElement\r\n            />\r\n          </ng-template>\r\n        </span>\r\n      </div>\r\n\r\n      <div\r\n        [class.library-tree-invisible]=\"!treeControl.isExpanded(node)\"\r\n        role=\"group\"\r\n      >\r\n        <ng-container matTreeNodeOutlet></ng-container>\r\n      </div>\r\n    </mat-nested-tree-node>\r\n  </mat-tree>\r\n</div>\r\n\r\n<!-- Context menu -->\r\n<ng-container *ngIf=\"isDisplayContextMenu && hasEditAccessOnly\">\r\n  <div class=\"menu-link mat-elevation-z4\" [ngStyle]=\"getRightClickMenuStyle()\">\r\n    <button\r\n      mat-menu-item\r\n      *ngFor=\"let option of contextMenuOptions\"\r\n      (click)=\"onAction(option.actionName)\"\r\n    >\r\n      {{ option.label }}\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAEEC,MAAM,QAMD,eAAe;AAEtB,SAASC,uBAAuB,QAAQ,wBAAwB;AAEhE,SAASC,mBAAmB,QAAQ,0CAA0C;AAG9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,UAAU,QAAQ,8BAA8B;AAMzD,SACEC,oBAAoB,EACpBC,sBAAsB,EACtBC,mBAAmB,QACd,gCAAgC;AAOvC,SAASC,2BAA2B,QAAQ,sDAAsD;;;;;;;;;;;;;ICnClGC,EAAA,CAAAC,cAAA,wBAMC;IAHCD,EAAA,CAAAE,UAAA,2BAAAC,uFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAiBR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;;IAGnCJ,EAAA,CAAAW,YAAA,EAAiB;;;IAFhBX,EAAA,CAAAY,qBAAA,gBAAAZ,EAAA,CAAAa,WAAA,yBAAgD;;;;;;IAS9Cb,EAAA,CAAAC,cAAA,wBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAAY,mFAAAV,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAW,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAU,OAAA,CAAAC,iBAAA,CAAAhB,MAAA,EAAAa,WAAA,CAAkC;IAAA,EAAC;IAE5CjB,EAAA,CAAAqB,SAAA,eAAyD;IACzDrB,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAsB,MAAA,GACF;;IAAAtB,EAAA,CAAAW,YAAA,EAAO;;;;;IARPX,EAAA,CAAAuB,WAAA,qBAAAN,WAAA,CAAAO,GAAA,KAAAC,OAAA,CAAAC,iBAAA,CAA4D;IAGrC1B,EAAA,CAAA2B,SAAA,GAA0B;IAA1B3B,EAAA,CAAA4B,UAAA,cAAAX,WAAA,CAAAY,IAAA,EAAA7B,EAAA,CAAA8B,cAAA,CAA0B;IAE/C9B,EAAA,CAAA2B,SAAA,GAAsE;IAAtE3B,EAAA,CAAA4B,UAAA,eAAAH,OAAA,CAAAM,iBAAA,CAAAd,WAAA,CAAAe,IAAA,QAAAf,WAAA,CAAAe,IAAA,MAAsE;IAEtEhC,EAAA,CAAA2B,SAAA,GACF;IADE3B,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,OAAAjB,WAAA,CAAAe,IAAA,WACF;;;;;IAfNhC,EAAA,CAAAC,cAAA,cAAyD;IACnDD,EAAA,CAAAsB,MAAA,GAAwC;;IAAAtB,EAAA,CAAAW,YAAA,EAAK;IAEjDX,EAAA,CAAAC,cAAA,eAAU;IACRD,EAAA,CAAAmC,UAAA,IAAAC,mDAAA,4BAYgB;IAClBpC,EAAA,CAAAW,YAAA,EAAW;;;;IAhBPX,EAAA,CAAA2B,SAAA,GAAwC;IAAxC3B,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAa,WAAA,+BAAwC;IAIpBb,EAAA,CAAA2B,SAAA,GAAgB;IAAhB3B,EAAA,CAAA4B,UAAA,YAAAU,MAAA,CAAAC,aAAA,CAAgB;;;;;IAuCpCvC,EAAA,CAAAwC,uBAAA,GAA2D;IACzDxC,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAsB,MAAA,GAAiC;;IAAAtB,EAAA,CAAAW,YAAA,EAClC;IACHX,EAAA,CAAAyC,qBAAA,EAAe;;;;;IAJXzC,EAAA,CAAA2B,SAAA,GAAoE;IAApE3B,EAAA,CAAA4B,UAAA,eAAAc,OAAA,CAAAX,iBAAA,CAAAY,UAAA,CAAAX,IAAA,QAAAW,UAAA,CAAAX,IAAA,MAAoE;IAEpEhC,EAAA,CAAA2B,SAAA,GAAiC;IAAjC3B,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,OAAAS,UAAA,CAAAX,IAAA,UAAiC;;;;;;IAInChC,EAAA,CAAAC,cAAA,oBAQE;IANAD,EAAA,CAAAE,UAAA,2BAAA0C,iGAAAxC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAF,UAAA,GAAA3C,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,OAAalB,EAAA,CAAAS,WAAA,CAAAkC,UAAA,CAAAX,IAAA,GAAA5B,MAAA,CAClB;IAAA,EAD8B,kBAAA0C,wFAAA;MAAA9C,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAE,IAAA,GAAA/C,EAAA,CAAAgD,WAAA;MAAA,MAAAL,UAAA,GAAA3C,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAA+B,OAAA,GAAAjD,EAAA,CAAAQ,aAAA;MAAA,OACjBR,EAAA,CAAAS,WAAA,CAAAwC,OAAA,CAAAC,UAAA,CAAAP,UAAA,EAAAI,IAAA,CAAAI,KAAA,CAA4C;IAAA,EAD3B,2BAAAC,iGAAA;MAAApD,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAE,IAAA,GAAA/C,EAAA,CAAAgD,WAAA;MAAA,MAAAL,UAAA,GAAA3C,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAmC,OAAA,GAAArD,EAAA,CAAAQ,aAAA;MAAA,OAERR,EAAA,CAAAS,WAAA,CAAA4C,OAAA,CAAAH,UAAA,CAAAP,UAAA,EAAAI,IAAA,CAAAI,KAAA,CAA4C;IAAA,EAFpC,4BAAAG,kGAAA;MAAAtD,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAF,UAAA,GAAA3C,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAqC,OAAA,GAAAvD,EAAA,CAAAQ,aAAA;MAAA,OAGPR,EAAA,CAAAS,WAAA,CAAA8C,OAAA,CAAAC,YAAA,CAAAb,UAAA,CAAoB;IAAA,EAHb;IAF3B3C,EAAA,CAAAW,YAAA,EAQE;;;;IANAX,EAAA,CAAA4B,UAAA,YAAAe,UAAA,CAAAX,IAAA,CAAyB;;;;;;IAzB/BhC,EAAA,CAAAC,cAAA,wBAaC;IARCD,EAAA,CAAAE,UAAA,yBAAAuD,yFAAArD,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAf,UAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAAyC,OAAA,GAAA3D,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAAkD,OAAA,CAAAC,YAAA,CAAAxD,MAAA,EAAAuC,UAAA,CAA4B;IAAA,EAAC,uBAAAkB,uFAAAzD,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAf,UAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAA4C,OAAA,GAAA9D,EAAA,CAAAQ,aAAA;MAAA,OAE/BR,EAAA,CAAAS,WAAA,CAAAqD,OAAA,CAAAC,WAAA,CAAA3D,MAAA,EAAAuC,UAAA,CAA2B;IAAA,EAFI,mBAAAqB,mFAAA5D,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAf,UAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAA+C,OAAA,GAAAjE,EAAA,CAAAQ,aAAA;MAAA,OAGnCR,EAAA,CAAAS,WAAA,CAAAwD,OAAA,CAAA7C,iBAAA,CAAAhB,MAAA,EAAAuC,UAAA,CAAiC;IAAA,EAHE,sBAAAuB,sFAAA9D,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAf,UAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAAiD,OAAA,GAAAnE,EAAA,CAAAQ,aAAA;MAAA,OAIhCR,EAAA,CAAAS,WAAA,CAAA0D,OAAA,CAAAC,UAAA,CAAAhE,MAAA,EAAAuC,UAAA,CAA0B;IAAA,EAJM,kBAAA0B,kFAAAjE,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAf,UAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAAoD,OAAA,GAAAtE,EAAA,CAAAQ,aAAA;MAAA,OAKpCR,EAAA,CAAAS,WAAA,CAAA6D,OAAA,CAAAC,MAAA,CAAAnE,MAAA,EAAAuC,UAAA,CAAsB;IAAA,EALc,sBAAA6B,sFAAA;MAAA,MAAAzD,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAf,UAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAAuD,OAAA,GAAAzE,EAAA,CAAAQ,aAAA;MAAA,OAMhCR,EAAA,CAAAS,WAAA,CAAAgE,OAAA,CAAAC,YAAA,CAAA/B,UAAA,CAAoB;IAAA,EANY;IAS5C3C,EAAA,CAAAqB,SAAA,eAAwD;IACxDrB,EAAA,CAAAmC,UAAA,IAAAwC,kEAAA,2BAMe;IACf3E,EAAA,CAAAmC,UAAA,IAAAyC,iEAAA,iCAAA5E,EAAA,CAAA6E,sBAAA,CAUc;IAChB7E,EAAA,CAAAW,YAAA,EAAgB;;;;;;IA9BdX,EAAA,CAAAuB,WAAA,aAAAuD,OAAA,CAAAC,SAAA,CAAAC,UAAA,CAAArC,UAAA,EAA+C,qBAAAA,UAAA,CAAAnB,GAAA,KAAAsD,OAAA,CAAApD,iBAAA;IAG/C1B,EAAA,CAAA4B,UAAA,cAAAe,UAAA,CAAAsC,WAAA,IAAAH,OAAA,CAAAI,iBAAA,IAAAJ,OAAA,CAAAK,UAAA,CAAmE;IAMnEnF,EAAA,CAAAoF,WAAA,kBAAAzC,UAAA,CAAAnB,GAAA,CAAiC;IAEVxB,EAAA,CAAA2B,SAAA,GAAyB;IAAzB3B,EAAA,CAAA4B,UAAA,cAAAe,UAAA,CAAAd,IAAA,EAAA7B,EAAA,CAAA8B,cAAA,CAAyB;IACjC9B,EAAA,CAAA2B,SAAA,GAA0B;IAA1B3B,EAAA,CAAA4B,UAAA,UAAAe,UAAA,CAAA0C,UAAA,CAA0B,aAAAC,IAAA;;;;;IAvB/CtF,EAAA,CAAAC,cAAA,cAMC;IAEGD,EAAA,CAAAmC,UAAA,IAAAoD,mDAAA,4BAiCgB;IAClBvF,EAAA,CAAAW,YAAA,EAAW;;;;IAjCYX,EAAA,CAAA2B,SAAA,GAAgB;IAAhB3B,EAAA,CAAA4B,UAAA,YAAA4D,MAAA,CAAAjD,aAAA,CAAgB;;;;;IAoCvCvC,EAAA,CAAAC,cAAA,cAGC;IAC2BD,EAAA,CAAAsB,MAAA,GAAoC;;IAAAtB,EAAA,CAAAW,YAAA,EAAI;;;IAAxCX,EAAA,CAAA2B,SAAA,GAAoC;IAApC3B,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAa,WAAA,2BAAoC;;;;;IAJhEb,EAAA,CAAAmC,UAAA,IAAAsD,iDAAA,kBAKM;;;;IAJHzF,EAAA,CAAA4B,UAAA,SAAA8D,MAAA,CAAAnD,aAAA,CAAAoD,MAAA,UAAAD,MAAA,CAAAE,WAAA,KAAAF,MAAA,CAAAG,gBAAA,CAAoE;;;;;IAoCjE7F,EAAA,CAAAwC,uBAAA,GAAyD;IACvDxC,EAAA,CAAAC,cAAA,eAEG;IAAAD,EAAA,CAAAsB,MAAA,GAA+B;;IAAAtB,EAAA,CAAAW,YAAA,EACjC;IACHX,EAAA,CAAAyC,qBAAA,EAAe;;;;;IAHXzC,EAAA,CAAA2B,SAAA,GAAgE;IAAhE3B,EAAA,CAAA4B,UAAA,eAAAkE,OAAA,CAAA/D,iBAAA,CAAAgE,QAAA,CAAA/D,IAAA,QAAA+D,QAAA,CAAA/D,IAAA,MAAgE;IAC/DhC,EAAA,CAAA2B,SAAA,GAA+B;IAA/B3B,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAkC,WAAA,OAAA6D,QAAA,CAAA/D,IAAA,MAA+B;;;;;;IAIlChC,EAAA,CAAAC,cAAA,oBAQE;IANAD,EAAA,CAAAE,UAAA,2BAAA8F,2FAAA5F,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAF,QAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,OAAalB,EAAA,CAAAS,WAAA,CAAAsF,QAAA,CAAA/D,IAAA,GAAA5B,MAAA,CACpB;IAAA,EAD8B,kBAAA8F,kFAAA;MAAAlG,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAE,IAAA,GAAAnG,EAAA,CAAAgD,WAAA;MAAA,MAAA+C,QAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAkF,OAAA,GAAApG,EAAA,CAAAQ,aAAA;MAAA,OACfR,EAAA,CAAAS,WAAA,CAAA2F,OAAA,CAAAlD,UAAA,CAAA6C,QAAA,EAAAI,IAAA,CAAAhD,KAAA,CAA0C;IAAA,EAD3B,2BAAAkD,2FAAA;MAAArG,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAE,IAAA,GAAAnG,EAAA,CAAAgD,WAAA;MAAA,MAAA+C,QAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAoF,OAAA,GAAAtG,EAAA,CAAAQ,aAAA;MAAA,OAENR,EAAA,CAAAS,WAAA,CAAA6F,OAAA,CAAApD,UAAA,CAAA6C,QAAA,EAAAI,IAAA,CAAAhD,KAAA,CAA0C;IAAA,EAFpC,4BAAAoD,4FAAA;MAAAvG,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAF,QAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAsF,OAAA,GAAAxG,EAAA,CAAAQ,aAAA;MAAA,OAGLR,EAAA,CAAAS,WAAA,CAAA+F,OAAA,CAAAhD,YAAA,CAAAuC,QAAA,CAAkB;IAAA,EAHb;IAFzB/F,EAAA,CAAAW,YAAA,EAQE;;;;IANAX,EAAA,CAAA4B,UAAA,YAAAmE,QAAA,CAAA/D,IAAA,CAAuB;;;;;;IA9B/BhC,EAAA,CAAAC,cAAA,wBAcC;IARCD,EAAA,CAAAE,UAAA,yBAAAuG,mFAAArG,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAAyF,OAAA,GAAA3G,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAAkG,OAAA,CAAA/C,YAAA,CAAAxD,MAAA,EAAA2F,QAAA,CAA0B;IAAA,EAAC,uBAAAa,iFAAAxG,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAA2F,OAAA,GAAA7G,EAAA,CAAAQ,aAAA;MAAA,OAE7BR,EAAA,CAAAS,WAAA,CAAAoG,OAAA,CAAA9C,WAAA,CAAA3D,MAAA,EAAA2F,QAAA,CAAyB;IAAA,EAFI,mBAAAe,6EAAA1G,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAA6F,OAAA,GAAA/G,EAAA,CAAAQ,aAAA;MAAA,OAGjCR,EAAA,CAAAS,WAAA,CAAAsG,OAAA,CAAA3F,iBAAA,CAAAhB,MAAA,EAAA2F,QAAA,CAA+B;IAAA,EAHE,sBAAAiB,gFAAA5G,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAA+F,OAAA,GAAAjH,EAAA,CAAAQ,aAAA;MAAA,OAI9BR,EAAA,CAAAS,WAAA,CAAAwG,OAAA,CAAA7C,UAAA,CAAAhE,MAAA,EAAA2F,QAAA,CAAwB;IAAA,EAJM,kBAAAmB,4EAAA9G,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAAiG,OAAA,GAAAnH,EAAA,CAAAQ,aAAA;MAAA,OAKlCR,EAAA,CAAAS,WAAA,CAAA0G,OAAA,CAAA5C,MAAA,CAAAnE,MAAA,EAAA2F,QAAA,CAAoB;IAAA,EALc,sBAAAqB,gFAAA;MAAA,MAAArG,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAAmG,OAAA,GAAArH,EAAA,CAAAQ,aAAA;MAAA,OAM9BR,EAAA,CAAAS,WAAA,CAAA4G,OAAA,CAAA3C,YAAA,CAAAqB,QAAA,CAAkB;IAAA,EANY;IAS1C/F,EAAA,CAAAC,cAAA,eAA8D;IAAxDD,EAAA,CAAAE,UAAA,mBAAAoH,oEAAA;MAAA,MAAAvG,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqG,IAAA;MAAA,MAAAX,QAAA,GAAAhF,WAAA,CAAAG,SAAA;MAAA,MAAAqG,OAAA,GAAAvH,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8G,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAA1B,QAAA,CAAwB;IAAA,EAAC;IACtC/F,EAAA,CAAAqB,SAAA,eAIQ;IACRrB,EAAA,CAAAmC,UAAA,IAAAuF,4DAAA,2BAKe;IACf1H,EAAA,CAAAmC,UAAA,IAAAwF,2DAAA,iCAAA3H,EAAA,CAAA6E,sBAAA,CAUc;IAChB7E,EAAA,CAAAW,YAAA,EAAO;;;;;;IAlCPX,EAAA,CAAAuB,WAAA,aAAAqG,MAAA,CAAA7C,SAAA,CAAAC,UAAA,CAAAe,QAAA,EAA6C,qBAAAA,QAAA,CAAAvE,GAAA,KAAAoG,MAAA,CAAAlG,iBAAA;IAG7C1B,EAAA,CAAA4B,UAAA,cAAAmE,QAAA,CAAAd,WAAA,IAAA2C,MAAA,CAAA1C,iBAAA,IAAA0C,MAAA,CAAAzC,UAAA,CAAiE;IAMjEnF,EAAA,CAAAoF,WAAA,kBAAAW,QAAA,CAAAvE,GAAA,CAA+B;IAK3BxB,EAAA,CAAA2B,SAAA,GAAuB;IAAvB3B,EAAA,CAAA4B,UAAA,cAAAmE,QAAA,CAAAlE,IAAA,EAAA7B,EAAA,CAAA8B,cAAA,CAAuB,cAAAiE,QAAA,CAAAd,WAAA,IAAA2C,MAAA,CAAA1C,iBAAA;IAGVlF,EAAA,CAAA2B,SAAA,GAAwB;IAAxB3B,EAAA,CAAA4B,UAAA,UAAAmE,QAAA,CAAAV,UAAA,CAAwB,aAAAwC,IAAA;;;;;IAsDrC7H,EAAA,CAAAwC,uBAAA,GAA+D;IAC7DxC,EAAA,CAAAC,cAAA,eAEG;IAAAD,EAAA,CAAAsB,MAAA,GAA+B;;IAAAtB,EAAA,CAAAW,YAAA,EACjC;IACHX,EAAA,CAAAyC,qBAAA,EAAe;;;;;IAHXzC,EAAA,CAAA2B,SAAA,GAAgE;IAAhE3B,EAAA,CAAA4B,UAAA,eAAAkG,OAAA,CAAA/F,iBAAA,CAAAgG,QAAA,CAAA/F,IAAA,QAAA+F,QAAA,CAAA/F,IAAA,MAAgE;IAC/DhC,EAAA,CAAA2B,SAAA,GAA+B;IAA/B3B,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAkC,WAAA,OAAA6F,QAAA,CAAA/F,IAAA,MAA+B;;;;;;IAIlChC,EAAA,CAAAC,cAAA,oBAQE;IANAD,EAAA,CAAAE,UAAA,2BAAA8H,kGAAA5H,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA4H,IAAA;MAAA,MAAAF,QAAA,GAAA/H,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,OAAalB,EAAA,CAAAS,WAAA,CAAAsH,QAAA,CAAA/F,IAAA,GAAA5B,MAAA,CACtB;IAAA,EADgC,kBAAA8H,yFAAA;MAAAlI,EAAA,CAAAK,aAAA,CAAA4H,IAAA;MAAA,MAAAE,IAAA,GAAAnI,EAAA,CAAAgD,WAAA;MAAA,MAAA+E,QAAA,GAAA/H,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAkH,OAAA,GAAApI,EAAA,CAAAQ,aAAA;MAAA,OACfR,EAAA,CAAAS,WAAA,CAAA2H,OAAA,CAAAlF,UAAA,CAAA6E,QAAA,EAAAI,IAAA,CAAAhF,KAAA,CAA0C;IAAA,EAD3B,2BAAAkF,kGAAA;MAAArI,EAAA,CAAAK,aAAA,CAAA4H,IAAA;MAAA,MAAAE,IAAA,GAAAnI,EAAA,CAAAgD,WAAA;MAAA,MAAA+E,QAAA,GAAA/H,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAoH,OAAA,GAAAtI,EAAA,CAAAQ,aAAA;MAAA,OAENR,EAAA,CAAAS,WAAA,CAAA6H,OAAA,CAAApF,UAAA,CAAA6E,QAAA,EAAAI,IAAA,CAAAhF,KAAA,CAA0C;IAAA,EAFpC,4BAAAoF,mGAAA;MAAAvI,EAAA,CAAAK,aAAA,CAAA4H,IAAA;MAAA,MAAAF,QAAA,GAAA/H,EAAA,CAAAQ,aAAA,GAAAU,SAAA;MAAA,MAAAsH,OAAA,GAAAxI,EAAA,CAAAQ,aAAA;MAAA,OAGLR,EAAA,CAAAS,WAAA,CAAA+H,OAAA,CAAAhF,YAAA,CAAAuE,QAAA,CAAkB;IAAA,EAHb;IAFzB/H,EAAA,CAAAW,YAAA,EAQE;;;;IANAX,EAAA,CAAA4B,UAAA,YAAAmG,QAAA,CAAA/F,IAAA,CAAuB;;;;;;IA1CjChC,EAAA,CAAAC,cAAA,+BAKC;IAFCD,EAAA,CAAAE,UAAA,mBAAAuI,2FAAArI,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAAyH,OAAA,GAAA3I,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkI,OAAA,CAAAvH,iBAAA,CAAAhB,MAAA,EAAA2H,QAAA,CAA+B;IAAA,EAAC;IAGzC/H,EAAA,CAAAC,cAAA,cAgBC;IAVCD,EAAA,CAAAE,UAAA,mBAAA0I,0EAAA;MAAA,MAAA7H,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAA2H,OAAA,GAAA7I,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAoI,OAAA,CAAArB,WAAA,CAAAC,MAAA,CAAAM,QAAA,CAAwB;IAAA,EAAC,yBAAAe,gFAAA1I,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAA6H,OAAA,GAAA/I,EAAA,CAAAQ,aAAA;MAAA,OAEnBR,EAAA,CAAAS,WAAA,CAAAsI,OAAA,CAAAnF,YAAA,CAAAxD,MAAA,EAAA2H,QAAA,CAA0B;IAAA,EAFP,uBAAAiB,8EAAA5I,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAA+H,OAAA,GAAAjJ,EAAA,CAAAQ,aAAA;MAAA,OAIrBR,EAAA,CAAAS,WAAA,CAAAwI,OAAA,CAAAlF,WAAA,CAAA3D,MAAA,EAAA2H,QAAA,CAAyB;IAAA,EAJJ,uBAAAmB,8EAAA9I,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAAiI,OAAA,GAAAnJ,EAAA,CAAAQ,aAAA;MAAA,OAKrBR,EAAA,CAAAS,WAAA,CAAA0I,OAAA,CAAAC,WAAA,CAAAhJ,MAAA,EAAA2H,QAAA,CAAyB;IAAA,EALJ,uBAAAsB,8EAAAjJ,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAAoI,OAAA,GAAAtJ,EAAA,CAAAQ,aAAA;MAAA,OAMrBR,EAAA,CAAAS,WAAA,CAAA6I,OAAA,CAAAC,WAAA,CAAAnJ,MAAA,EAAA2H,QAAA,CAAyB;IAAA,EANJ,sBAAAyB,6EAAApJ,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAAuI,OAAA,GAAAzJ,EAAA,CAAAQ,aAAA;MAAA,OAOtBR,EAAA,CAAAS,WAAA,CAAAgJ,OAAA,CAAArF,UAAA,CAAAhE,MAAA,EAAA2H,QAAA,CAAwB;IAAA,EAPF,kBAAA2B,yEAAAtJ,MAAA;MAAA,MAAAW,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAAyI,OAAA,GAAA3J,EAAA,CAAAQ,aAAA;MAAA,OAQ1BR,EAAA,CAAAS,WAAA,CAAAkJ,OAAA,CAAApF,MAAA,CAAAnE,MAAA,EAAA2H,QAAA,CAAoB;IAAA,EARM,sBAAA6B,6EAAA;MAAA,MAAA7I,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAX,QAAA,GAAAhH,WAAA,CAAAG,SAAA;MAAA,MAAA2I,OAAA,GAAA7J,EAAA,CAAAQ,aAAA;MAAA,OAStBR,EAAA,CAAAS,WAAA,CAAAoJ,OAAA,CAAAnF,YAAA,CAAAqD,QAAA,CAAkB;IAAA,EATI;IAWlC/H,EAAA,CAAAC,cAAA,mBAAsE;IACpED,EAAA,CAAAsB,MAAA,GACF;IAAAtB,EAAA,CAAAW,YAAA,EAAW;IAEXX,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAqB,SAAA,eAIQ;IACRrB,EAAA,CAAAmC,UAAA,IAAA2H,mEAAA,2BAKe;IACf9J,EAAA,CAAAmC,UAAA,IAAA4H,kEAAA,iCAAA/J,EAAA,CAAA6E,sBAAA,CAUc;IAChB7E,EAAA,CAAAW,YAAA,EAAO;IAGTX,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgK,kBAAA,QAA+C;IACjDhK,EAAA,CAAAW,YAAA,EAAM;;;;;;IAtDNX,EAAA,CAAAoF,WAAA,kBAAA2C,QAAA,CAAAvG,GAAA,CAA+B;IAI7BxB,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAuB,WAAA,gBAAA0I,MAAA,CAAAC,aAAA,CAAmC,aAAAD,MAAA,CAAAC,aAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAApC,QAAA,kBAAAkC,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,iBAAA,CAAApC,QAAA,eAAAkC,MAAA,CAAAG,UAAA,IAAAH,MAAA,CAAAI,WAAA,KAAAtC,QAAA,cAAAkC,MAAA,CAAAlF,SAAA,CAAAC,UAAA,CAAA+C,QAAA;IAOnC/H,EAAA,CAAA4B,UAAA,cAAAmG,QAAA,CAAA9C,WAAA,IAAAgF,MAAA,CAAA/E,iBAAA,IAAA+E,MAAA,CAAA9E,UAAA,CAAiE;IAQrCnF,EAAA,CAAA2B,SAAA,GAAyC;IAAzC3B,EAAA,CAAAoF,WAAA,2BAAA2C,QAAA,CAAA/F,IAAA,CAAyC;IACnEhC,EAAA,CAAA2B,SAAA,GACF;IADE3B,EAAA,CAAAiC,kBAAA,MAAAgI,MAAA,CAAAzC,WAAA,CAAA8C,UAAA,CAAAvC,QAAA,yCACF;IAKI/H,EAAA,CAAA2B,SAAA,GAAuB;IAAvB3B,EAAA,CAAA4B,UAAA,cAAAmG,QAAA,CAAAlG,IAAA,EAAA7B,EAAA,CAAA8B,cAAA,CAAuB,cAAAiG,QAAA,CAAA9C,WAAA,IAAAgF,MAAA,CAAA/E,iBAAA;IAGVlF,EAAA,CAAA2B,SAAA,GAAwB;IAAxB3B,EAAA,CAAA4B,UAAA,UAAAmG,QAAA,CAAA1C,UAAA,CAAwB,aAAAkF,IAAA;IAqBzCvK,EAAA,CAAA2B,SAAA,GAA8D;IAA9D3B,EAAA,CAAAuB,WAAA,4BAAA0I,MAAA,CAAAzC,WAAA,CAAA8C,UAAA,CAAAvC,QAAA,EAA8D;;;;;;IAYlE/H,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAsK,8EAAA;MAAA,MAAAzJ,WAAA,GAAAf,EAAA,CAAAK,aAAA,CAAAoK,IAAA;MAAA,MAAAC,UAAA,GAAA3J,WAAA,CAAAG,SAAA;MAAA,MAAAyJ,OAAA,GAAA3K,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkK,OAAA,CAAAC,QAAA,CAAAF,UAAA,CAAAG,UAAA,CAA2B;IAAA,EAAC;IAErC7K,EAAA,CAAAsB,MAAA,GACF;IAAAtB,EAAA,CAAAW,YAAA,EAAS;;;;IADPX,EAAA,CAAA2B,SAAA,GACF;IADE3B,EAAA,CAAAiC,kBAAA,MAAAyI,UAAA,CAAAI,KAAA,MACF;;;;;IARJ9K,EAAA,CAAAwC,uBAAA,GAAgE;IAC9DxC,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAmC,UAAA,IAAA4I,qDAAA,qBAMS;IACX/K,EAAA,CAAAW,YAAA,EAAM;IACRX,EAAA,CAAAyC,qBAAA,EAAe;;;;IAT2BzC,EAAA,CAAA2B,SAAA,GAAoC;IAApC3B,EAAA,CAAA4B,UAAA,YAAAoJ,MAAA,CAAAC,sBAAA,GAAoC;IAGrDjL,EAAA,CAAA2B,SAAA,GAAqB;IAArB3B,EAAA,CAAA4B,UAAA,YAAAoJ,MAAA,CAAAE,kBAAA,CAAqB;;;ADhK9C,OAAM,MAAOC,oBAAoB;EAyB/B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClG,iBAAiB,IAAI,IAAI,CAACH,SAAS,CAACsG,QAAQ,CAAC1F,MAAM,GAAG,CAAC,EAAE;MAChE,IAAI,CAAC2F,mBAAmB,EAAE;;EAE9B;EACA,IAAaC,aAAaA,CAAC/J,GAAW;IACpC,IAAIA,GAAG,EAAE,IAAI,CAACgK,eAAe,CAAChK,GAAG,CAAC;EACpC;EAEAiK,YACUC,OAAkB,EAClBC,eAAgC,EAChCC,YAA0B,EAC1BC,yBAAmD,EACnDC,cAA6B,EAC7BC,gBAAiC,EACjCC,cAA6B,EAC7BC,OAAe,EACfC,MAAc;IARd,KAAAR,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IA5ChB,KAAA1E,WAAW,GAAG,IAAIlI,iBAAiB,CAAY6M,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC;IACtE,KAAAC,UAAU,GAAG,IAAI7M,uBAAuB,EAAY;IACpD,KAAA8M,oBAAoB,GAAY,KAAK;IAMrC,KAAApH,iBAAiB,GAAY,KAAK;IAGlC,KAAAqH,gBAAgB,GAAoB,IAAI;IACxC,KAAAC,aAAa,GAAkB,IAAIC,GAAG,EAAE;IACxC,KAAApH,UAAU,GAAY,KAAK;IAC3B,KAAAqH,YAAY,GAAW,EAAE;IACzB,KAAAhL,iBAAiB,GAAW,EAAE;IAC9B,KAAAqD,SAAS,GAAG,IAAI1F,cAAc,CAAW,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IACpD,KAAAkD,aAAa,GAAe,EAAE;IAC9B,KAAA4C,UAAU,GAAY,IAAI;IAC1B,KAAAS,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAqE,aAAa,GAAY,KAAK;IAC9B,KAAAE,UAAU,GAAY,KAAK;IAC3B,KAAAuC,iBAAiB,GAAoB,IAAI;IAyJzC,KAAAC,QAAQ,GAAG,CAACC,CAAS,EAAEV,IAAc,KAAI;MACvC,OAAO,CAAC,CAACA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACzG,MAAM,GAAG,CAAC;IACpD,CAAC;IApICpG,MAAM,CAAC,MAAK;MACV,MAAMuN,kBAAkB,GAAG,IAAI,CAACd,cAAc,CAACe,kBAAkB,EAAE;MACnE,IAAI,CAACd,OAAO,CAACe,GAAG,CAAC,MAAK;QACpB,IAAI,CAACC,kBAAkB,CAACH,kBAAkB,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACvB,eAAe,CAACwB,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC9D,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACC,YAAY,GAAGD,QAAQ;QAC5B,IAAI,CAAChB,UAAU,CAACkB,IAAI,GAAG,EAAE;QACzB,IAAI,CAAClB,UAAU,CAACkB,IAAI,GAAG,CAACF,QAAQ,CAAC;QACjC,MAAMG,KAAK,GAAG,IAAI,CAAChG,WAAW,CAACiG,cAAc,CAACJ,QAAQ,CAAC;QACvD,IAAI,CAAC1B,eAAe,CAAC+B,mBAAmB,GAAGF,KAAK;;IAEpD,CAAC,CAAC;IACF,IAAI,CAAC5B,YAAY,CAAC+B,oBAAoB,EAAE,CAACP,SAAS,CAAEQ,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE;QACX,IAAI,CAACzI,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC0I,cAAc,GAAGD,OAAO;QAC7B,IAAI,CAAClM,iBAAiB,GAAG,QAAQhC,gBAAgB,CAACoO,OAAO,IAAIF,OAAO,CAACG,EAAE,EAAE;QAEzE;QACA,IAAI,CAACvC,eAAe,CAAC,IAAI,CAAC9J,iBAAiB,CAAC;OAC7C,MAAM;QACL,IAAI,CAACqK,gBAAgB,CAACiC,eAAe,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC7I,UAAU,GAAG,KAAK;;IAE3B,CAAC,CAAC;IACF,IAAI,CAAC2G,cAAc,CAACmC,iBAAiB,EAAE,CAACb,SAAS,CAAEc,QAAQ,IAAI;MAC7D,IAAI,CAAChJ,iBAAiB,GAAGgJ,QAAQ,IAAIvO,UAAU,CAACwO,MAAM;IACxD,CAAC,CAAC;EACJ;EAEAzN,QAAQA,CAAC0N,UAAyB;IAChC,IAAIA,UAAU,EAAE;MACd,IAAI,CAACxI,WAAW,GAAG,IAAI;MACvB,IAAI,CAACrD,aAAa,GAAG,IAAI,CAAC8L,WAAW,CAACD,UAAU,CAAC;MACjD,IAAI,CAACrC,gBAAgB,CAACiC,eAAe,CAAC,IAAI,CAAC;KAC5C,MAAM;MACL,IAAI,CAACpI,WAAW,GAAG,KAAK;MACxB,IAAI,CAACrD,aAAa,GAAG,EAAE;;EAE3B;EAEA0K,kBAAkBA,CAACpH,gBAAyB;IAC1C,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IAExC;IACA,IAAI,IAAI,CAACA,gBAAgB,EAAE;MACzB,IAAI,CAACyI,oBAAoB,EAAE;;EAE/B;EAEQA,oBAAoBA,CAAA;IAC1B,IAAI,CAAC/L,aAAa,GAAG,IAAI,CAACgM,WAAW,EAAE,CAACC,MAAM,CAC3CrC,IAAI,IAAKA,IAAI,CAACsC,QAAQ,KAAK/O,gBAAgB,CAACoO,OAAO,CACrD;EACH;EACA;EACQO,WAAWA,CACjBD,UAAkB,EAClBZ,KAAA,GAAoB,IAAI,CAACnB,UAAU,CAACkB,IAAI;IAExC,IAAImB,OAAO,GAAe,EAAE;IAC5B,KAAK,MAAMvC,IAAI,IAAIqB,KAAK,EAAE;MACxB;MACA,IACE,IAAI,CAACmB,kBAAkB,CAACxC,IAAI,CAAC,IAC7BA,IAAI,CAACnK,IAAI,CAAC4M,WAAW,EAAE,CAACC,QAAQ,CAACT,UAAU,CAACQ,WAAW,EAAE,CAAC,EAC1D;QACAF,OAAO,CAACI,IAAI,CAAC3C,IAAI,CAAC;;MAEpB;MACA,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACzG,MAAM,GAAG,CAAC,EAAE;QAC7C+I,OAAO,GAAGA,OAAO,CAACK,MAAM,CAAC,IAAI,CAACV,WAAW,CAACD,UAAU,EAAEjC,IAAI,CAACC,QAAQ,CAAC,CAAC;;;IAGzE,OAAOsC,OAAO;EAChB;EAEQC,kBAAkBA,CAACxC,IAAc;IACvC,OACEA,IAAI,CAACsC,QAAQ,KAAK7O,oBAAoB,IACtCuM,IAAI,CAACsC,QAAQ,KAAK3O,mBAAmB,IACrCqM,IAAI,CAACsC,QAAQ,KAAK5O,sBAAsB,IACxCsM,IAAI,CAACsC,QAAQ,KAAK/O,gBAAgB,CAACsP,OAAO;EAE9C;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC1M,aAAa,GAAG,EAAE;IACvB,IAAI,CAACwJ,gBAAgB,CAACiC,eAAe,CAAC,IAAI,CAAC;EAC7C;EAEA;EACAkB,kBAAkBA,CAACC,KAAiB,EAAEhD,IAAc;IAClD,IAAI,CAACpH,SAAS,CAACqK,MAAM,CAACjD,IAAI,CAAC;IAC3B,IAAI,CAAC/K,iBAAiB,CAAC+N,KAAK,EAAEhD,IAAI,CAAC;EACrC;EAEA;EACAb,mBAAmBA,CAAA;IACjB,MAAMkB,aAAa,GAAG,IAAI,CAACzH,SAAS,CAACsG,QAAQ;IAC7C,IAAImB,aAAa,CAAC7G,MAAM,KAAK,CAAC,EAAE;IAChC,MAAM0J,SAAS,GAAG,IAAI,CAAC3D,OAAO,CAAC4D,IAAI,CAIjCvP,2BAA2B,EAAE;MAC7BwP,KAAK,EAAE,OAAO;MACdhC,IAAI,EAAE;QACJiC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,WAAW;QACnBC,OAAO,EAAE;;KAEZ,CAAC;IACFL,SAAS,CAACM,WAAW,EAAE,CAACvC,SAAS,CAAEwC,SAAS,IAAI;MAC9C,IAAIA,SAAS,EAAE;QACb;QACA,IAAI,CAAC/D,yBAAyB,CAACP,mBAAmB,CAACkB,aAAa,CAAC;QACjE;QACA,IAAI,CAACzH,SAAS,CAAC8K,KAAK,EAAE;;IAE1B,CAAC,CAAC;EACJ;EAKAC,mBAAmBA,CAAC3D,IAAc;IAChC,IAAIA,IAAI,IAAIA,IAAI,CAACsC,QAAQ,KAAK/O,gBAAgB,CAACoO,OAAO,EAAE;MACtD,MAAMiC,gBAAgB,GAAG,IAAI,CAACpE,eAAe,CAACqE,cAAc,CAC1D7D,IAAI,CAAC8D,SAAU,EACf,IAAI,CAAC3C,YAAY,CAClB;MACD,IACEyC,gBAAgB,IAChBA,gBAAgB,CAACtB,QAAQ,KAAK/O,gBAAgB,CAACwQ,MAAM,EAErD,OAAO,IAAI,CAAC,KACT,OAAO,IAAI;;IAElB,OAAO,IAAI;EACb;EACAtM,YAAYA,CAACuL,KAAiB,EAAEhD,IAAc;IAC5CgD,KAAK,CAACgB,eAAe,EAAE;IACvBhB,KAAK,CAACiB,cAAc,EAAE;IACtB,IAAI,CAACC,YAAY,GAAGlE,IAAI;IACxB,MAAMmE,WAAW,GAAG7Q,mBAAmB,CAAC8Q,IAAI,CACzCC,IAAI,IAAKA,IAAI,CAAC/B,QAAQ,KAAKtC,IAAI,CAACsC,QAAQ,IAAI+B,IAAI,CAACC,SAAS,CAC5D;IAED;IACA;IACA,IACE,IAAI,CAAC1L,SAAS,CAACsG,QAAQ,CAAC1F,MAAM,GAAG,CAAC,IAClC,CAAC,IAAI,CAACZ,SAAS,CAACC,UAAU,CAACmH,IAAI,CAAC,EAChC;MACA,IAAI,CAACpH,SAAS,CAAC8K,KAAK,EAAE;MACtB,IAAI,CAAC9K,SAAS,CAACqK,MAAM,CAACjD,IAAI,CAAC;;IAG7B,IAAImE,WAAW,EAAE;MACf,IAAI,CAACpF,kBAAkB,GACrB,IAAI,CAACnG,SAAS,CAACsG,QAAQ,CAAC1F,MAAM,IAAI,CAAC,GAC/B2K,WAAW,CAACI,OAAQ,GACpBJ,WAAW,CAACI,OAAQ,CAAClC,MAAM,CAAEmC,MAAM,IAAKA,MAAM,CAAC7F,KAAK,IAAI,QAAQ,CAAC;MACvE,IAAI,CAACwB,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACsE,uBAAuB,GAAGzB,KAAK,CAAC0B,OAAO;MAC5C,IAAI,CAACC,uBAAuB,GAAG3B,KAAK,CAAC4B,OAAO;;EAEhD;EAGAC,aAAaA,CAAA;IACX,IAAI,CAAC1E,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACvH,SAAS,CAAC8K,KAAK,EAAE;EACxB;EAEA5E,sBAAsBA,CAAA;IACpB,OAAO;MACLgG,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE,GAAG,IAAI,CAACN,uBAAuB,IAAI;MACzCO,GAAG,EAAE,GAAG,IAAI,CAACL,uBAAuB;KACrC;EACH;EAEA1P,iBAAiBA,CAAC+N,KAAiB,EAAE9B,QAAkB;IACrD8B,KAAK,CAACgB,eAAe,EAAE;IACvB,IAAI,CAAC7D,oBAAoB,GAAG,KAAK;IACjC,IACEe,QAAQ,CAACoB,QAAQ,KAAK7O,oBAAoB,IAC1CyN,QAAQ,CAACoB,QAAQ,KAAK3O,mBAAmB,IACzCuN,QAAQ,CAACoB,QAAQ,KAAK5O,sBAAsB,IAC5CwN,QAAQ,CAACoB,QAAQ,KAAK/O,gBAAgB,CAACsP,OAAO,IAC9C,CAAC,IAAI,CAAC7J,UAAU,EAChB;MACA,IAAI,CAAC4G,gBAAgB,CAACiC,eAAe,CAAC,IAAI,CAAC;MAC3C;;IAEF,IAAI,CAACzB,gBAAgB,GAAGc,QAAQ;IAChC,IAAI,CAAC,IAAI,CAAC+D,SAAS,CAAC/D,QAAQ,CAACE,IAAI,CAAC,IAAIF,QAAQ,CAACE,IAAI,EAAE;MACnD,IAAI,CAACxB,gBAAgB,CAACiC,eAAe,CAACX,QAAQ,CAACE,IAAI,CAAC;MACpD,IAAI4B,KAAK,CAACkC,OAAO,IAAIlC,KAAK,CAACmC,OAAO,EAAE;QAClC;QACA,IAAI,CAACvM,SAAS,CAAC0C,MAAM,CAAC4F,QAAQ,CAAC;OAChC,MAAM,IAAI8B,KAAK,CAACoC,QAAQ,IAAI,IAAI,CAACxM,SAAS,CAACsG,QAAQ,CAAC1F,MAAM,GAAG,CAAC,EAAE;QAC/D;QACA;QACA,MAAM6L,YAAY,GAChB,IAAI,CAACzM,SAAS,CAACsG,QAAQ,CAAC,IAAI,CAACtG,SAAS,CAACsG,QAAQ,CAAC1F,MAAM,GAAG,CAAC,CAAC;QAC7D,MAAM6H,KAAK,GAAG,IAAI,CAACe,WAAW,EAAE;QAChC,MAAMkD,QAAQ,GAAGjE,KAAK,CAACkE,OAAO,CAACF,YAAY,CAAC;QAC5C,MAAMG,MAAM,GAAGnE,KAAK,CAACkE,OAAO,CAACrE,QAAQ,CAAC;QACtC,MAAMuE,KAAK,GAAGpE,KAAK,CAACqE,KAAK,CACvBC,IAAI,CAACC,GAAG,CAACN,QAAQ,EAAEE,MAAM,CAAC,EAC1BG,IAAI,CAACE,GAAG,CAACP,QAAQ,EAAEE,MAAM,CAAC,GAAG,CAAC,CAC/B;QACDC,KAAK,CAACK,OAAO,CAAEC,CAAC,IAAI;UAClB,IACEA,CAAC,CAACzD,QAAQ,KAAK7O,oBAAoB,IACnCsS,CAAC,CAACzD,QAAQ,KAAK3O,mBAAmB,IAClCoS,CAAC,CAACzD,QAAQ,KAAK5O,sBAAsB,IACrCqS,CAAC,CAACzD,QAAQ,KAAK/O,gBAAgB,CAACsP,OAAO,IACvCkD,CAAC,CAACzD,QAAQ,KAAK/O,gBAAgB,CAACoO,OAAO,EACvC;YACA;;UAEF,IAAI,CAAC/I,SAAS,CAACqK,MAAM,CAAC8C,CAAC,CAAC;QAC1B,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACnN,SAAS,CAAC8K,KAAK,EAAE;QACtB,IAAI,CAAC9K,SAAS,CAACqK,MAAM,CAAC/B,QAAQ,CAAC;;;IAGnC,IACEA,QAAQ,CAACoB,QAAQ,KAAK/O,gBAAgB,CAACoO,OAAO,IAC9C,IAAI,CAACD,cAAc,CAACE,EAAE,KAAKV,QAAQ,CAACE,IAAI,EAAEQ,EAAE,EAC5C;MACA,IAAI,CAAChC,gBAAgB,CAACiC,eAAe,CAAC,IAAI,CAAC;MAC3C,MAAMJ,OAAO,GAAGP,QAAQ,CAACE,IAAe;MACxC,IAAI,CAAC3B,YAAY,CAACuG,gBAAgB,CAACvE,OAAO,CAAC;MAE3C;MACA,MAAMwE,SAAS,GAAGxE,OAAO,CAACyE,SAAS;MACnC,MAAMC,SAAS,GAAG1E,OAAO,CAACG,EAAE;MAC5B,IAAIqE,SAAS,IAAIE,SAAS,EAAE;QAC1B,IAAI,CAACpG,MAAM,CAACqG,QAAQ,CAAC,CAAC,WAAWH,SAAS,YAAYE,SAAS,EAAE,CAAC,EAAE;UAClEE,UAAU,EAAE,IAAI,CAAE;SACnB,CAAC;QAEF;QACA;;;EAGN;EAEA;EACQjE,WAAWA,CAAA;IACjB,MAAMf,KAAK,GAAe,EAAE;IAC5B,MAAMiF,mBAAmB,GAAIC,SAAqB,IAAI;MACpDA,SAAS,CAACT,OAAO,CAAE9F,IAAI,IAAI;QACzBqB,KAAK,CAACsB,IAAI,CAAC3C,IAAI,CAAC;QAChB,IAAIA,IAAI,CAACC,QAAQ,EAAE;UACjBqG,mBAAmB,CAACtG,IAAI,CAACC,QAAQ,CAAC;;MAEtC,CAAC,CAAC;IACJ,CAAC;IACDqG,mBAAmB,CAAC,IAAI,CAACpG,UAAU,CAACkB,IAAI,CAAC;IACzC,OAAOC,KAAK;EACd;EAEQ4D,SAASA,CAAC7D,IAAS;IACzB,OACGA,IAAgB,CAACQ,EAAE,KAAK4E,SAAS,IACjCpF,IAAgB,CAAC8E,SAAS,KAAKM,SAAS,IACzCpF,IAAI,CAACkB,QAAQ,KAAKkE,SAAS;EAE/B;EAEA5O,WAAWA,CAACoL,KAAgB,EAAEhD,IAAc;IAC1C,IAAIgD,KAAK,CAACyD,YAAY,EAAE;MACtBzD,KAAK,CAACyD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC5G,IAAI,CAAC,CAAC;MAC9D,IAAI,CAAC9B,WAAW,GAAG8B,IAAI;MACvB,IAAI,CAAC/B,UAAU,GAAG,IAAI;MAEtB;MACA,MAAM4I,OAAO,GAAG7D,KAAK,CAAC8D,MAAqB;MAC3CD,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;EAErC;EAEAvI,QAAQA,CAACwI,MAAyB;IAChC,IAAI,CAACvH,yBAAyB,CAACwH,aAAa,CAC1CD,MAAM,EACN,IAAI,CAAC/C,YAAY,EACjB,IAAI,CAACtL,SAAS,CACf;EACH;EAEAX,UAAUA,CAAC+K,KAAgB,EAAEhD,IAAc;IACzCgD,KAAK,CAACiB,cAAc,EAAE;IACtB,IAAI,IAAI,CAACjG,iBAAiB,CAACgC,IAAI,CAAC,EAAE;MAChCgD,KAAK,CAACyD,YAAa,CAACU,UAAU,GAAG,MAAM;KACxC,MAAM;MACLnE,KAAK,CAACyD,YAAa,CAACU,UAAU,GAAG,MAAM;;EAE3C;EAEA;EACA/O,MAAMA,CAAC4K,KAAgB,EAAEoE,UAAoB;IAC3CpE,KAAK,CAACiB,cAAc,EAAE;IACtB,IAAI,CAAClG,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACuC,iBAAiB,GAAG,IAAI;IAE7B,IAAI,IAAI,CAACxC,iBAAiB,CAACoJ,UAAU,CAAC,EAAE;MACtC,IAAI,CAAC5H,eAAe,CAAC6H,QAAQ,CAACD,UAAU,EAAE,IAAI,CAAClJ,WAAW,CAAC;;EAE/D;EAEArF,UAAUA,CAACmH,IAAc;IACvB,OAAO,IAAI,CAACK,aAAa,CAACiH,GAAG,CAACtH,IAAI,CAAC;EACrC;EAEAuH,aAAaA,CAAA;IACX,IAAI,IAAI,CAACxO,iBAAiB,IAAI,IAAI,CAACqH,gBAAgB,IAAI,IAAI,CAACpH,UAAU,EAAE;MACtE,IAAI,CAACT,YAAY,CAAC,IAAI,CAAC6H,gBAAgB,CAAC;;EAE5C;EACAoH,SAASA,CAACxH,IAAc;IACtB,IACEA,IAAI,CAACsC,QAAQ,IAAI5O,sBAAsB,IACvCsM,IAAI,CAACsC,QAAQ,IAAI7O,oBAAoB,IACrCuM,IAAI,CAACsC,QAAQ,IAAI3O,mBAAmB,IACpCqM,IAAI,CAACsC,QAAQ,KAAK/O,gBAAgB,CAACsP,OAAO,EAE1C,OAAO,KAAK,CAAC,KACV,OAAO,IAAI;EAClB;EACAtK,YAAYA,CAACyH,IAAc;IACzB,IAAI,CAAC,IAAI,CAACjH,iBAAiB,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;IACjD,IAAI,IAAI,CAACwO,SAAS,CAACxH,IAAI,CAAC,EAAE;MACxBA,IAAI,CAAC9G,UAAU,GAAG,IAAI;MACtB,IAAI,CAACqH,YAAY,GAAGP,IAAI,CAACnK,IAAI;;IAE/B;IACA4R,UAAU,CAAC,MAAK;MACd,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CACzC,eAAe,CACI;MACrB,IAAIF,YAAY,IAAI,IAAI,CAACtH,gBAAgB,EAAE;QACzCsH,YAAY,CAACG,KAAK,EAAE;QACpBH,YAAY,CAACzE,MAAM,EAAE,CAAC,CAAC;;IAE3B,CAAC,CAAC;EACJ;EAEAlM,UAAUA,CAACiJ,IAAc,EAAE8H,OAAe;IACxC,MAAMC,WAAW,GAAGD,OAAO,CAACE,IAAI,EAAE;IAElC,IAAID,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAK,IAAI,CAACxH,YAAY,EAAE;MAC3D;MACAP,IAAI,CAACnK,IAAI,GAAG,IAAI,CAAC0K,YAAY,IAAIP,IAAI,CAACnK,IAAI;MAC1C,MAAM6R,YAAY,GAAGC,QAAQ,CAACC,aAAa,CACzC,eAAe,CACI;MACrB,IAAIF,YAAY,EAAE;QAChBA,YAAY,CAACO,IAAI,EAAE;;KAEtB,MAAM;MACL;MACAjI,IAAI,CAACnK,IAAI,GAAGkS,WAAW;MACvB,IAAI,CAACrI,yBAAyB,CAACwI,UAAU,CAAClI,IAAI,CAAC;;IAGjDA,IAAI,CAAC9G,UAAU,GAAG,KAAK;IACvB,IAAI,CAACqH,YAAY,GAAG,EAAE;EACxB;EAEAlJ,YAAYA,CAAC2I,IAAc;IACzBA,IAAI,CAAC9G,UAAU,GAAG,KAAK;EACzB;EACAtD,iBAAiBA,CAACC,IAAY,EAAE2D,MAAc;IAC5C,OAAO3D,IAAI,CAAC2D,MAAM,GAAGA,MAAM;EAC7B;EAEAyD,WAAWA,CAAC+F,KAAgB,EAAEhD,IAAc;IAC1CgD,KAAK,CAACiB,cAAc,EAAE;IACtB,IAAI,CAACzD,iBAAiB,GAAGR,IAAI;IAC7B,IAAI,CAACjC,aAAa,GAAG,IAAI;EAC3B;EAEAX,WAAWA,CAAC4F,KAAgB,EAAEhD,IAAc;IAC1CgD,KAAK,CAACiB,cAAc,EAAE;IACtB,IAAI,IAAI,CAACzD,iBAAiB,KAAKR,IAAI,EAAE;MACnC,IAAI,CAACjC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACyC,iBAAiB,GAAG,IAAI;;EAEjC;EAEAxC,iBAAiBA,CAACgC,IAAc;IAC9B,OACE,IAAI,CAAC9B,WAAW,IAChB8B,IAAI,CAACmI,eAAe,EAAEzF,QAAQ,CAAC,IAAI,CAACxE,WAAW,CAACoE,QAAQ,CAAC,IACzD,CAACtC,IAAI,CAACsC,QAAQ,CAACI,QAAQ,CAAC,SAAS,CAAC,IAClC,IAAI,CAAC1J,UAAU,IACf,IAAI,CAAC2K,mBAAmB,CAAC,IAAI,CAACzF,WAAW,CAAC;EAE9C;EAEAkK,WAAWA,CAAA;IACT,IAAI,CAACrK,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACuC,iBAAiB,GAAG,IAAI;EAC/B;EACA;;;;;EAKAnB,eAAeA,CAAChK,GAAW;IACzB,MAAM2K,IAAI,GAAG,IAAI,CAACR,eAAe,CAAC6I,aAAa,CAAChT,GAAG,CAAC;IACpD,IAAI2K,IAAI,EAAE;MACR;MACA,IAAI,CAACsI,iBAAiB,CAACtI,IAAI,CAAC;MAE5B;MACA,IAAI,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAET,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC3E,WAAW,CAAC8C,UAAU,CAAC6B,IAAI,CAAC,EAAE;QAChE,IAAI,CAAC3E,WAAW,CAACkN,MAAM,CAACvI,IAAI,CAAC;;MAG/B;MACAyH,UAAU,CAAC,MAAK;QACd,MAAMe,WAAW,GAAGb,QAAQ,CAACC,aAAa,CAAC,mBAAmBvS,GAAG,IAAI,CAAC;QACtE,IAAImT,WAAW,EAAE;UACfA,WAAW,CAACC,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAQ,CAAE,CAAC;;MAEvE,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEA;;;;EAIQL,iBAAiBA,CAACtI,IAAc;IACtC;IACA,MAAM4I,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC7I,IAAI,CAAC;IAE7C;IACA4I,WAAW,CAAC9C,OAAO,CAAEgD,MAAM,IAAI;MAC7B,IAAI,CAAC,IAAI,CAACzN,WAAW,CAAC8C,UAAU,CAAC2K,MAAM,CAAC,EAAE;QACxC,IAAI,CAACzN,WAAW,CAACkN,MAAM,CAACO,MAAM,CAAC;;IAEnC,CAAC,CAAC;EACJ;EAEA;;;;;EAKQD,cAAcA,CAAC7I,IAAc;IACnC,MAAM4I,WAAW,GAAe,EAAE;IAClC,IAAIG,WAAW,GAAG/I,IAAI;IAEtB;IACA,OAAO+I,WAAW,CAACjF,SAAS,EAAE;MAC5B,MAAMkF,UAAU,GAAG,IAAI,CAACxJ,eAAe,CAAC6I,aAAa,CACnDU,WAAW,CAACjF,SAAS,CACtB;MACD,IAAIkF,UAAU,EAAE;QACdJ,WAAW,CAACK,OAAO,CAACD,UAAU,CAAC,CAAC,CAAC;QACjCD,WAAW,GAAGC,UAAU;OACzB,MAAM;QACL;;;IAIJ,OAAOJ,WAAW;EACpB;EAAC,QAAAlI,CAAA,G;qBArhBU1B,oBAAoB,EAAAnL,EAAA,CAAAqV,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAvV,EAAA,CAAAqV,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzV,EAAA,CAAAqV,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA3V,EAAA,CAAAqV,iBAAA,CAAAO,EAAA,CAAAC,wBAAA,GAAA7V,EAAA,CAAAqV,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAA/V,EAAA,CAAAqV,iBAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAjW,EAAA,CAAAqV,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAnW,EAAA,CAAAqV,iBAAA,CAAArV,EAAA,CAAAoW,MAAA,GAAApW,EAAA,CAAAqV,iBAAA,CAAAgB,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBpL,oBAAoB;IAAAqL,SAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAApBC,GAAA,CAAAxL,WAAA,CAAAhL,MAAA,CAAmB;QAAA,UAAAJ,EAAA,CAAA6W,eAAA,oBAAAC,8CAAA;UAAA,OAAnBF,GAAA,CAAA5F,aAAA,EAAe;QAAA,UAAAhR,EAAA,CAAA+W,iBAAA,uBAAAC,iDAAA;UAAA,OAAfJ,GAAA,CAAAlD,aAAA,EAAe;QAAA,UAAA1T,EAAA,CAAA6W,eAAA;;;;;;;;;;;QCzC5B7W,EAAA,CAAAmC,UAAA,IAAA8U,8CAAA,4BAMkB;QAGlBjX,EAAA,CAAAmC,UAAA,IAAA+U,mCAAA,iBAkBM;QAGNlX,EAAA,CAAAmC,UAAA,IAAAgV,mCAAA,iBA2CM;QACNnX,EAAA,CAAAmC,UAAA,IAAAiV,2CAAA,gCAAApX,EAAA,CAAA6E,sBAAA,CAOc;QAGd7E,EAAA,CAAAC,cAAA,aAAgD;QAO5CD,EAAA,CAAAmC,UAAA,IAAAkV,6CAAA,4BAuCgB;QAGhBrX,EAAA,CAAAmC,UAAA,IAAAmV,oDAAA,oCA2DuB;QACzBtX,EAAA,CAAAW,YAAA,EAAW;QAIbX,EAAA,CAAAmC,UAAA,IAAAoV,4CAAA,0BAUe;;;;QA7MZvX,EAAA,CAAA4B,UAAA,UAAAgV,GAAA,CAAA/Q,gBAAA,CAAuB;QAOpB7F,EAAA,CAAA2B,SAAA,GAAsB;QAAtB3B,EAAA,CAAA4B,UAAA,SAAAgV,GAAA,CAAA/Q,gBAAA,CAAsB;QAsBzB7F,EAAA,CAAA2B,SAAA,GAEK;QAFL3B,EAAA,CAAA4B,UAAA,SAAAgV,GAAA,CAAArU,aAAA,CAAAoD,MAAA,QAAAiR,GAAA,CAAAhR,WAAA,KAAAgR,GAAA,CAAA/Q,gBAAA,CAEK,aAAA2R,GAAA;QAmDHxX,EAAA,CAAA2B,SAAA,GAA0C;QAA1C3B,EAAA,CAAA4B,UAAA,WAAAgV,GAAA,CAAAhR,WAAA,IAAAgR,GAAA,CAAA/Q,gBAAA,CAA0C;QAE3C7F,EAAA,CAAA2B,SAAA,GAAyB;QAAzB3B,EAAA,CAAA4B,UAAA,eAAAgV,GAAA,CAAAvK,UAAA,CAAyB,gBAAAuK,GAAA,CAAApP,WAAA;QAgDIxH,EAAA,CAAA2B,SAAA,GAAc;QAAd3B,EAAA,CAAA4B,UAAA,uBAAAgV,GAAA,CAAAhK,QAAA,CAAc;QA+DhC5M,EAAA,CAAA2B,SAAA,GAA+C;QAA/C3B,EAAA,CAAA4B,UAAA,SAAAgV,GAAA,CAAAtK,oBAAA,IAAAsK,GAAA,CAAA1R,iBAAA,CAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}