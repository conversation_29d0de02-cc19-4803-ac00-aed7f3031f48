{"ast": null, "code": "import { catchError } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FolderApiService {\n  constructor(http) {\n    this.http = http;\n    this.backendUrl = environment.backEndUrl;\n  }\n  createFolder(folderObj) {\n    return this.http.post(this.backendUrl + '/Folder', folderObj).pipe(catchError(error => {\n      console.error('Error creating folder:', error);\n      throw error;\n    }));\n  }\n  deleteFolder(folderIds) {\n    return this.http.post(this.backendUrl + `/Folder/deleteMultiple`, folderIds).pipe(catchError(error => {\n      console.error('Error deleting folder:', error);\n      throw error;\n    }));\n  }\n  updateFolder(folderObj) {\n    return this.http.patch(this.backendUrl + `/Folder`, folderObj).pipe(catchError(error => {\n      console.error('Error updating update folder:', error);\n      throw error;\n    }));\n  }\n  moveDiagramToFolder(moveDiagram) {\n    return this.http.patch(this.backendUrl + `/Diagram/moveToFolder`, moveDiagram).pipe(catchError(error => {\n      console.error('Error moving diagram:', error);\n      throw error;\n    }));\n  }\n  removeDiagramFromFolder(diagramId) {\n    return this.http.patch(this.backendUrl + `/Diagram/removeFromFolder`, {\n      id: diagramId\n    }).pipe(catchError(error => {\n      console.error('Error moving diagram:', error);\n      throw error;\n    }));\n  }\n  moveFolderToFolder(moveFolder) {\n    return this.http.patch(this.backendUrl + `/Folder/moveToFolder`, moveFolder).pipe(catchError(error => {\n      console.error('Error moving folder:', error);\n      throw error;\n    }));\n  }\n  removeFolderFromFolder(folderId) {\n    return this.http.patch(this.backendUrl + `/Folder/removeFromFolder`, {\n      id: folderId\n    }).pipe(catchError(error => {\n      console.error('Error moving folder:', error);\n      throw error;\n    }));\n  }\n  /**\n   * Alias methods to match DatabaseSyncService interface\n   */\n  deleteFolders(folderIds) {\n    return this.deleteFolder(folderIds);\n  }\n  /**\n   * Undo folder deletion\n   *\n   * @param {number} idFolder folder id\n   * @returns {Observable<Folder>}\n   *\n   * @memberOf FolderApiService\n   */\n  undoFolderDelete(idFolder) {\n    return this.http.patch(this.backendUrl + `/Folder/${idFolder}`, {}).pipe(catchError(error => {\n      console.error('Error undo folder deletion:', error);\n      throw error;\n    }));\n  }\n  static #_ = this.ɵfac = function FolderApiService_Factory(t) {\n    return new (t || FolderApiService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: FolderApiService,\n    factory: FolderApiService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["catchError", "environment", "FolderApiService", "constructor", "http", "backendUrl", "backEndUrl", "createFolder", "folderObj", "post", "pipe", "error", "console", "deleteFolder", "folderIds", "updateFolder", "patch", "moveDiagramToFolder", "moveDiagram", "removeDiagramFromFolder", "diagramId", "id", "moveFolderToFolder", "moveFolder", "removeFolderFromFolder", "folderId", "deleteFolders", "undoFolderDelete", "idFolder", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\api\\folder-api.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Diagram } from 'gojs';\r\nimport { Observable, catchError } from 'rxjs';\r\nimport { FolderUpdate, ProjectFolder } from 'src/app/shared/model/class';\r\nimport { Folder, FolderMove, MoveToFolder } from 'src/app/shared/model/folder';\r\nimport { environment } from 'src/environments/environment';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FolderApiService {\r\n  private backendUrl: string = environment.backEndUrl;\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createFolder(folderObj: ProjectFolder): Observable<ProjectFolder> {\r\n    return this.http\r\n      .post<ProjectFolder>(this.backendUrl + '/Folder', folderObj)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error creating folder:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  deleteFolder(folderIds: number[]): Observable<void> {\r\n    return this.http\r\n      .post<void>(this.backendUrl + `/Folder/deleteMultiple`, folderIds)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error deleting folder:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  updateFolder(folderObj: FolderUpdate): Observable<FolderUpdate> {\r\n    return this.http\r\n      .patch<FolderUpdate>(this.backendUrl + `/Folder`, folderObj)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error updating update folder:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n  moveDiagramToFolder(moveDiagram: MoveToFolder): Observable<Diagram> {\r\n    return this.http\r\n      .patch<Diagram>(this.backendUrl + `/Diagram/moveToFolder`, moveDiagram)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error moving diagram:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n  removeDiagramFromFolder(diagramId: number): Observable<Diagram> {\r\n    return this.http\r\n      .patch<Diagram>(this.backendUrl + `/Diagram/removeFromFolder`, {\r\n        id: diagramId,\r\n      })\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error moving diagram:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n  moveFolderToFolder(moveFolder: FolderMove): Observable<Folder> {\r\n    return this.http\r\n      .patch<Folder>(this.backendUrl + `/Folder/moveToFolder`, moveFolder)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error moving folder:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n  removeFolderFromFolder(folderId: number): Observable<Folder> {\r\n    return this.http\r\n      .patch<Folder>(this.backendUrl + `/Folder/removeFromFolder`, {\r\n        id: folderId,\r\n      })\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error moving folder:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Alias methods to match DatabaseSyncService interface\r\n   */\r\n  deleteFolders(folderIds: number[]): Observable<void> {\r\n    return this.deleteFolder(folderIds);\r\n  }\r\n\r\n  /**\r\n   * Undo folder deletion\r\n   *\r\n   * @param {number} idFolder folder id\r\n   * @returns {Observable<Folder>}\r\n   *\r\n   * @memberOf FolderApiService\r\n   */\r\n  undoFolderDelete(idFolder: number): Observable<Folder> {\r\n    return this.http\r\n      .patch<Folder>(this.backendUrl + `/Folder/${idFolder}`, {})\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error undo folder deletion:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAAqBA,UAAU,QAAQ,MAAM;AAG7C,SAASC,WAAW,QAAQ,8BAA8B;;;AAI1D,OAAM,MAAOC,gBAAgB;EAE3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IADhB,KAAAC,UAAU,GAAWJ,WAAW,CAACK,UAAU;EACZ;EAEvCC,YAAYA,CAACC,SAAwB;IACnC,OAAO,IAAI,CAACJ,IAAI,CACbK,IAAI,CAAgB,IAAI,CAACJ,UAAU,GAAG,SAAS,EAAEG,SAAS,CAAC,CAC3DE,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAE,YAAYA,CAACC,SAAmB;IAC9B,OAAO,IAAI,CAACV,IAAI,CACbK,IAAI,CAAO,IAAI,CAACJ,UAAU,GAAG,wBAAwB,EAAES,SAAS,CAAC,CACjEJ,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAI,YAAYA,CAACP,SAAuB;IAClC,OAAO,IAAI,CAACJ,IAAI,CACbY,KAAK,CAAe,IAAI,CAACX,UAAU,GAAG,SAAS,EAAEG,SAAS,CAAC,CAC3DE,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EACAM,mBAAmBA,CAACC,WAAyB;IAC3C,OAAO,IAAI,CAACd,IAAI,CACbY,KAAK,CAAU,IAAI,CAACX,UAAU,GAAG,uBAAuB,EAAEa,WAAW,CAAC,CACtER,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EACAQ,uBAAuBA,CAACC,SAAiB;IACvC,OAAO,IAAI,CAAChB,IAAI,CACbY,KAAK,CAAU,IAAI,CAACX,UAAU,GAAG,2BAA2B,EAAE;MAC7DgB,EAAE,EAAED;KACL,CAAC,CACDV,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EACAW,kBAAkBA,CAACC,UAAsB;IACvC,OAAO,IAAI,CAACnB,IAAI,CACbY,KAAK,CAAS,IAAI,CAACX,UAAU,GAAG,sBAAsB,EAAEkB,UAAU,CAAC,CACnEb,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EACAa,sBAAsBA,CAACC,QAAgB;IACrC,OAAO,IAAI,CAACrB,IAAI,CACbY,KAAK,CAAS,IAAI,CAACX,UAAU,GAAG,0BAA0B,EAAE;MAC3DgB,EAAE,EAAEI;KACL,CAAC,CACDf,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAe,aAAaA,CAACZ,SAAmB;IAC/B,OAAO,IAAI,CAACD,YAAY,CAACC,SAAS,CAAC;EACrC;EAEA;;;;;;;;EAQAa,gBAAgBA,CAACC,QAAgB;IAC/B,OAAO,IAAI,CAACxB,IAAI,CACbY,KAAK,CAAS,IAAI,CAACX,UAAU,GAAG,WAAWuB,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC1DlB,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAAC,QAAAkB,CAAA,G;qBAzGU3B,gBAAgB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBhC,gBAAgB;IAAAiC,OAAA,EAAhBjC,gBAAgB,CAAAkC,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}