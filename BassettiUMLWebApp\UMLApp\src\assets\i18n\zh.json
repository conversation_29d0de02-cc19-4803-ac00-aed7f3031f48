{"toolbar": {"language": "语言", "hello": "你好,", "user": "用户手册", "about": "关于", "logout": "登出", "savingMsg": "保存中...", "clearDiagram": "清除图表", "downloadDiagram": "下载图表", "versionHistory": "版本历史", "releaseNew": "发布新版本", "download-current": "当前图表", "download-all": "所有图表", "download-only-images": "仅图片", "download-with-details": "报告", "gridView": "网格布局", "onGridView": "启用网格布局", "offGridView": "停用网格布局", "lastModified": "最后修改"}, "lang": {"default": "默认语言 ({{code}})", "specific": "{{lang}} (默认)"}, "errors": {"unhandled": {"content": "发生了一个未处理的错误，请使用以下详细信息联系您的管理员。", "header": "未处理的错误"}, "0": {"content": "Uml App API 服务不可用或配置的 URL 无效。请使用以下详细信息联系您的管理员。", "header": "未找到 API 服务"}, "400": {"default": {"content": "Uml App API 服务上的请求构建不正确或参数已更改。请使用以下详细信息联系您的管理员。", "header": "错误的请求"}}, "401": {"default": {"content": "由于某些原因，无法连接到 Uml App API 服务。请使用以下详细信息联系您的管理员。", "header": "服务不可用"}}, "403": {"default": {"content": "您正在尝试访问的资源受到保护，您无权查看它。请使用以下详细信息联系您的管理员。", "header": "访问被拒绝"}}, "404": {"default": {"content": "未找到请求的资源或请求的 URL 不存在。请使用以下详细信息联系您的管理员。", "header": "资源未找到"}}, "500": {"default": {"content": "服务器遇到内部错误或超时问题，无法提供请求的资源。请使用以下详细信息联系您的管理员。", "header": "内部服务器错误"}}, "lockedProject": {"header": "项目已锁定！", "content": "该项目当前已被锁定，锁定者为 "}}, "errorMessage": {"details": "细节"}, "window": {"close": "关闭", "cancel": "取消", "save": "保存"}, "dashboard": {"title": "项目", "btnText": "+ 新建项目", "editProject": "编辑项目", "newProject": "新建项目", "deleteProject": "删除项目", "noProject": "没有可用的项目", "loading": "正在加载项目...", "error": {"loadingProjects": "加载项目时出错。请重试。"}, "shareProject": "分享项目", "filter": {"projectType": {"title": "项目类型", "placeholder": "项目类型"}, "productLine": {"title": "产品线", "placeholder": "产品线"}, "selfProject": {"title": "仅我的项目"}, "clearFilter": "清除所有过滤器"}, "search": {"placeholder": "按名称、描述、类型、产品线或所有者搜索..."}, "table": {"siNo": "编号", "name": "名称", "description": "项目描述", "type": "项目类型", "productLine": "产品线", "action": "操作", "filter": "筛选", "owner": "所有者", "lastModified": "最后修改", "open": "打开", "edit": "编辑", "share": "分享", "delete": "删除"}}, "diagram": {"components": "组件", "library": "库", "properties": "属性", "propertyDescription": "请选择任何库元素以显示属性", "selectedDiagram": "已选图表", "add": "添加", "edit": "编辑", "delete": "删除", "openMenu": "打开菜单", "createDiagram": "创建图表", "editDiagram": "编辑图表", "create": "创建", "diagramPlaceholderText": "图表名称", "save": "保存", "method": "方法", "attribute": "属性", "literal": "文字的", "search": "在图书馆中搜索...", "notFound": "未找到节点", "downloadNotAllowedMsg": "无法导出，因为图表为空", "zoomIn": "放大", "zoomOut": "缩小", "diagramsList": "图表列表"}, "folder": {"header": "创建新文件夹", "placeholder": "输入文件夹名称"}, "class": {"header": "创建新类", "placeholder": "输入类名称"}, "enumeration": {"header": "创建新枚举", "placeholder": "输入枚举名称"}, "attribute": {"header": "创建新属性", "placeholder": "输入属性名称"}, "method": {"header": "创建新方法", "placeholder": "输入方法名称"}, "literal": {"header": "创建新字面值", "placeholder": "输入字面值名称"}, "dialog": {"yes": "是", "no": "否", "title": "您确定要删除吗？", "create": "创建", "save": "保存", "projectName": "项目名称", "description": "描述", "productLine": "产品线", "search": "搜索产品线", "noResults": "未找到数据", "type": "类型", "deleteTitle": "您是否要从所有图表中删除链接?", "releaseVersion": "发布版本", "release": "发布", "versionName": "版本名称"}, "shareDialog": {"title": "分享项目", "accessTitle": "有访问权限的人员", "addPeople": "添加人员", "copyBtn": "复制链接", "access": "访问", "email": "电子邮件", "0": "管理员", "1": "编辑者", "2": "查看者", "send": "发送", "removeAccess": "移除访问权限"}, "snackBar": {"deleteProjectMsg": "项目删除成功", "deleteTempClassMsg": "模板类删除成功", "deleteTempEnumMsg": "模板枚举删除成功", "deleteAttributeMsg": "属性删除成功", "deleteLiteralMsg": "枚举文字删除成功", "diagramDeleteInfo": "您没有图表。请添加一个。", "diagramDeleteMsg": "图表删除成功", "diagramUpdateMsg": "图表更新成功", "projectUpdatedMsg": "项目更新成功", "projectCreationMsg": "项目创建成功", "deleteFolderMsg": "文件夹删除成功", "createFolderMsg": "文件夹创建成功", "projectShareMsg": "项目共享成功", "accessChangedMsg": "访问权限更改成功", "commentDeleteMsg": "评论删除成功", "permissionRemovedMsg": "项目权限已成功删除", "linkCopiedMsg": "链接已复制到剪贴板", "linkToLinkAlreadyExists": "已经有另一个关联类链接到此链接"}, "property": {"name": "名称", "color": "颜色", "type": "类型", "description": "描述", "volumetry": "体积测定", "tag": "标签", "fromComment": "来自评论", "toComment": "到评论"}, "about": {"version": "版本", "releaseDate": "发布日期", "descriptionMsg": "UML Web 应用程序允许用户通过拖放类、枚举和包等组件来创建和管理 UML 图表，并提供链接、调整大小和自定义的选项。用户可以将项目组织到文件夹中，激活网格对齐，并将图表下载为 PDF。它提供了一个直观的界面, 用于设计和管理专业的 UML 模型。"}, "colorPicker": {"customColor": "自定义颜色", "apply": "应用", "primaryColors": "主要颜色", "extendedColors": "扩展颜色", "selectColor": "选择颜色", "colors": {"red": "红色", "green": "绿色", "blue": "蓝色", "lightBlue": "浅蓝色", "skyBlue": "天蓝色", "yellow": "黄色", "darkGray": "深灰色", "gray": "灰色", "lightGray": "浅灰色", "darkRed": "深红色", "lightPink": "浅粉色", "pink": "粉色", "darkGreen": "深绿色", "mintGreen": "薄荷绿", "brightGreen": "亮绿色", "olive": "橄榄色", "orange": "橙色", "lightOrange": "浅橙色", "brown": "棕色", "salmon": "鲑鱼色", "coral": "珊瑚色", "cyan": "青色", "maroon": "栗色", "purple": "紫色"}}}