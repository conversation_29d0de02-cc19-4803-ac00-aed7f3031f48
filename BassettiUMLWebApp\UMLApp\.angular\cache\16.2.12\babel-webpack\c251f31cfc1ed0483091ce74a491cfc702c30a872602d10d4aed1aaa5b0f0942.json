{"ast": null, "code": "import { catchError } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class LiteralApiService {\n  constructor(http) {\n    this.http = http;\n    this.backendUrl = environment.backEndUrl;\n  }\n  createEnumerationLiteral(literalObj) {\n    return this.http.post(this.backendUrl + `/EnumerationLiteral`, literalObj).pipe(catchError(error => {\n      console.error('Error for creating literal:', error);\n      throw error;\n    }));\n  }\n  deleteEnumLiterals(enumLiteralIds) {\n    return this.http.post(this.backendUrl + `/EnumerationLiteral/deleteMultiple`, enumLiteralIds).pipe(catchError(error => {\n      console.error('Error for deleting literal:', error);\n      throw error;\n    }));\n  }\n  updateEnumerationLiteral(literalObj) {\n    return this.http.patch(this.backendUrl + `/EnumerationLiteral`, literalObj).pipe(catchError(error => {\n      console.error('Error for updating literal:', error);\n      throw error;\n    }));\n  }\n  /**\n   * Alias methods to match DatabaseSyncService interface\n   */\n  createLiteral(literalObj) {\n    return this.createEnumerationLiteral(literalObj);\n  }\n  updateLiteral(literalObj) {\n    return this.updateEnumerationLiteral(literalObj);\n  }\n  deleteLiteral(enumLiteralIds) {\n    return this.deleteEnumLiterals(enumLiteralIds);\n  }\n  /**\n   * Undo literal deletion\n   *\n   * @param {number} idLiteral literal id\n   * @returns {Observable<Literal>}\n   *\n   * @memberOf LiteralApiService\n   */\n  undoLiteralDelete(idLiteral) {\n    return this.http.patch(this.backendUrl + `/EnumerationLiteral/${idLiteral}`, {}).pipe(catchError(error => {\n      console.error('Error undo literal deletion:', error);\n      throw error;\n    }));\n  }\n  static #_ = this.ɵfac = function LiteralApiService_Factory(t) {\n    return new (t || LiteralApiService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LiteralApiService,\n    factory: LiteralApiService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["catchError", "environment", "LiteralApiService", "constructor", "http", "backendUrl", "backEndUrl", "createEnumerationLiteral", "literalObj", "post", "pipe", "error", "console", "deleteEnumLiterals", "enumLiteralIds", "updateEnumerationLiteral", "patch", "createLiteral", "updateLiteral", "deleteLiteral", "undoLiteralDelete", "idLiteral", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\api\\literal-api.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { catchError, Observable } from 'rxjs';\r\nimport { Literal, LiteralDTO } from 'src/app/shared/model/literal';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LiteralApiService {\r\n  private backendUrl: string = environment.backEndUrl;\r\n  constructor(private http: HttpClient) {}\r\n  createEnumerationLiteral(literalObj: LiteralDTO): Observable<LiteralDTO> {\r\n    return this.http\r\n      .post<LiteralDTO>(this.backendUrl + `/EnumerationLiteral`, literalObj)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error for creating literal:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n  deleteEnumLiterals(enumLiteralIds: number[]): Observable<void> {\r\n    return this.http\r\n      .post<void>(\r\n        this.backendUrl + `/EnumerationLiteral/deleteMultiple`,\r\n        enumLiteralIds\r\n      )\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error for deleting literal:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n  updateEnumerationLiteral(literalObj: Literal): Observable<Literal> {\r\n    return this.http\r\n      .patch<Literal>(this.backendUrl + `/EnumerationLiteral`, literalObj)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error for updating literal:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Alias methods to match DatabaseSyncService interface\r\n   */\r\n  createLiteral(literalObj: LiteralDTO): Observable<LiteralDTO> {\r\n    return this.createEnumerationLiteral(literalObj);\r\n  }\r\n\r\n  updateLiteral(literalObj: Literal): Observable<Literal> {\r\n    return this.updateEnumerationLiteral(literalObj);\r\n  }\r\n\r\n  deleteLiteral(enumLiteralIds: number[]): Observable<void> {\r\n    return this.deleteEnumLiterals(enumLiteralIds);\r\n  }\r\n\r\n  /**\r\n   * Undo literal deletion\r\n   *\r\n   * @param {number} idLiteral literal id\r\n   * @returns {Observable<Literal>}\r\n   *\r\n   * @memberOf LiteralApiService\r\n   */\r\n  undoLiteralDelete(idLiteral: number): Observable<Literal> {\r\n    return this.http\r\n      .patch<Literal>(this.backendUrl + `/EnumerationLiteral/${idLiteral}`, {})\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Error undo literal deletion:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,UAAU,QAAoB,MAAM;AAE7C,SAASC,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,iBAAiB;EAE5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IADhB,KAAAC,UAAU,GAAWJ,WAAW,CAACK,UAAU;EACZ;EACvCC,wBAAwBA,CAACC,UAAsB;IAC7C,OAAO,IAAI,CAACJ,IAAI,CACbK,IAAI,CAAa,IAAI,CAACJ,UAAU,GAAG,qBAAqB,EAAEG,UAAU,CAAC,CACrEE,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EACAE,kBAAkBA,CAACC,cAAwB;IACzC,OAAO,IAAI,CAACV,IAAI,CACbK,IAAI,CACH,IAAI,CAACJ,UAAU,GAAG,oCAAoC,EACtDS,cAAc,CACf,CACAJ,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EACAI,wBAAwBA,CAACP,UAAmB;IAC1C,OAAO,IAAI,CAACJ,IAAI,CACbY,KAAK,CAAU,IAAI,CAACX,UAAU,GAAG,qBAAqB,EAAEG,UAAU,CAAC,CACnEE,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAM,aAAaA,CAACT,UAAsB;IAClC,OAAO,IAAI,CAACD,wBAAwB,CAACC,UAAU,CAAC;EAClD;EAEAU,aAAaA,CAACV,UAAmB;IAC/B,OAAO,IAAI,CAACO,wBAAwB,CAACP,UAAU,CAAC;EAClD;EAEAW,aAAaA,CAACL,cAAwB;IACpC,OAAO,IAAI,CAACD,kBAAkB,CAACC,cAAc,CAAC;EAChD;EAEA;;;;;;;;EAQAM,iBAAiBA,CAACC,SAAiB;IACjC,OAAO,IAAI,CAACjB,IAAI,CACbY,KAAK,CAAU,IAAI,CAACX,UAAU,GAAG,uBAAuBgB,SAAS,EAAE,EAAE,EAAE,CAAC,CACxEX,IAAI,CACHV,UAAU,CAAEW,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAAC,QAAAW,CAAA,G;qBArEUpB,iBAAiB,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjBzB,iBAAiB;IAAA0B,OAAA,EAAjB1B,iBAAiB,CAAA2B,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}