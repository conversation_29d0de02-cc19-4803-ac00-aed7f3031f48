import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  effect,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { Lang } from 'src/app/shared/model/common';
import { AccessType, ProjectDetails } from 'src/app/shared/model/project';
import { VersionHistory } from 'src/app/shared/model/version-history';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AccessService } from '../../services/access/access.service';
import { AppService } from '../../services/app.service';
import { DiagramService } from '../../services/diagram/diagram.service';
import { LanguageService } from '../../services/language/language.service';
import {
  LoaderService,
  LoaderType,
} from '../../services/loader/loader.service';
import { NavbarService } from '../../services/navbar/navbar.service';
import { ProjectService } from '../../services/project/project.service';
import { SearchBarService } from '../../services/search-bar/search-bar.service';
import {
  UndoRedoService,
  UndoRedoState,
} from '../../services/undo-redo/undo-redo.service';
import { UserService } from '../../services/user/user.service';
import { VersionHistoryService } from '../../services/versionHistory/version-history.service';
import { AboutModalComponent } from '../about-modal/about-modal.component';

@Component({
  selector: 'app-main-nav',
  templateUrl: './main-nav.component.html',
  styleUrls: ['./main-nav.component.scss'],
})
export class MainNavComponent implements OnInit, OnDestroy, AfterViewInit {
  public languages: Lang[] = [];
  public profilePicture: string = '';
  public title: string = 'UML Web Application';
  currentDiagramName: string = '';
  currentProject!: ProjectDetails;
  public userName: string = '';
  hasDeleteAccess: boolean = false;
  isGridLayout: boolean = false;
  isLoading = false;
  loaderType: LoaderType = 'default';
  noOfDiagram: number = 0;
  private destroy$ = new Subject<void>();
  lastModifiedDate: Date | undefined = new Date();
  showVersionHistory: boolean = false;
  selectedVersion: VersionHistory | null = null;
  undoRedoState: UndoRedoState = {
    canUndo: false,
    canRedo: false,
    undoText: '',
    redoText: '',
    isProcessing: false,
  };
  constructor(
    public router: Router,
    private appService: AppService,
    private accessService: AccessService,
    private diagramService: DiagramService,
    private diagramUtils: DiagramUtils,
    private userService: UserService,
    private languageService: LanguageService,
    private projectService: ProjectService,
    public loaderService: LoaderService,
    private navbarService: NavbarService,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private versionHistoryService: VersionHistoryService,
    private searchBarService: SearchBarService,
    private undoRedoService: UndoRedoService
  ) {
    effect(() => {
      this.lastModifiedDate = this.appService.lastModifiedDate();
      this.selectedVersion = this.versionHistoryService.selectedVersion();
      this.showVersionHistory = this.navbarService.showVersionHistory();
    });
  }

  ngOnInit(): void {
    this.languageService.languages.subscribe((langs: Lang[]) => {
      this.languages = langs;
    });
    this.appService.getThemeClass();

    // Subscribe to user changes
    this.userService.userChanges().subscribe((user) => {
      if (user && Object.keys(user).length > 0) {
        // Set profile picture if available
        if (user.profilePicture) {
          this.profilePicture = user.profilePicture;
        }
        // Set user name
        if (user.name) {
          this.userName = user.name.split(' ')[0];
        }
      }
    });

    // Initialize user data
    this.userService.initializeUser();

    this.accessService.accessTypeChanges().subscribe((accessType) => {
      if (accessType == AccessType.Viewer) {
        this.hasDeleteAccess = false;
      } else {
        this.hasDeleteAccess = true;
      }
    });

    this.projectService.currentProjectChanges().subscribe((project) => {
      if (project) {
        this.currentProject = project;
        if (project.diagrams.length > 0) {
          this.currentDiagramName = project.diagrams[0].name;
          this.title = `${project.name} - ${this.currentDiagramName}`;
        } else this.title = `${project.name}`;
      }
    });
    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {
      if (diagrams) this.noOfDiagram = diagrams.length;
    });
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) {
        this.currentDiagramName = diagram.name;
        this.title = `${this.currentProject?.name} - ${this.currentDiagramName}`;
      } else {
        this.title = `${this.currentProject?.name}`;
      }
    });

    // Subscribe to loading state
    this.loaderService.isLoading$
      .pipe(takeUntil(this.destroy$))
      .subscribe((status) => {
        // Use setTimeout to break change detection cycle
        setTimeout(() => {
          this.isLoading = status;
          // Manually trigger change detection
          this.cdr.detectChanges();
        });
      });

    // Subscribe to loader type
    this.loaderService.loaderType$
      .pipe(takeUntil(this.destroy$))
      .subscribe((type) => {
        // Use setTimeout to break change detection cycle
        setTimeout(() => {
          this.loaderType = type;
          // Manually trigger change detection
          this.cdr.detectChanges();
        });
      });

    // Subscribe to undo/redo state changes
    this.undoRedoService.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        this.undoRedoState = state;
        this.cdr.detectChanges();
      });
  }
  ngAfterViewInit(): void {
    this.title = 'UML Web Application';
    this.cdr.detectChanges();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  goBack() {
    if (this.router.url.includes('/editor')) {
      this.title = 'UML Web Application';
      // Reset search term when navigating back to dashboard
      this.searchBarService.resetSearch();
      this.router.navigate(['/dashboard']);
    }
  }

  isEditorRoute(): boolean {
    return this.router.url.includes('/editor');
  }

  openAboutModal(): void {
    this.dialog.open(AboutModalComponent, {
      width: '42%',
      maxWidth: '70vw',
      panelClass: 'about-modal',
    });
  }

  /**
   * Performs the logout operation.
   * @memberof MainNavComponent
   */
  onLogout() {
    if (this.router.url.includes('editor')) {
      this.projectService.handleProjectUnlock(this.currentProject.id!);
    }
    this.userService.logout();
  }

  onImageError() {
    // Set to empty string to show the default user icon
    this.profilePicture = '';
  }

  onDiagramClear() {
    this.diagramService.triggerDelete();
  }
  downloadCurrent() {
    this.diagramService.triggerCurrentDiagramDownload();
  }
  downloadAllDiagrams(isForOnlyImage: boolean) {
    this.diagramService.triggerAllDiagramDownload(isForOnlyImage);
  }
  changeLanguage(langCode: string) {
    this.languageService.changeLanguage(langCode);
  }
  shouldShowTooltip(name: string, length: number): boolean {
    return name.length > length;
  }
  createNewProject() {
    this.projectService.openProjectDialog();
  }

  openVersionHistory() {
    this.navbarService.setShowVersionHistory(true);
  }

  closeVersionHistory(): void {
    // If you're using a service with signals as mentioned earlier
    this.navbarService.setShowVersionHistory(false);
  }

  /**
   * Handle undo operation
   */
  onUndo(): void {
    if (this.undoRedoState.canUndo && !this.undoRedoState.isProcessing) {
      this.undoRedoService.undo();
    }
  }

  /**
   * Handle redo operation
   */
  onRedo(): void {
    if (this.undoRedoState.canRedo && !this.undoRedoState.isProcessing) {
      this.undoRedoService.redo();
    }
  }
}
