{"ast": null, "code": "import _asyncToGenerator from \"D:/GitHub/Bassetti/devint-BASSETTI-GROUP-APP/BassettiUMLWebApp/UMLApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Subject, fromEvent } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../gojs/gojsCommon/gojs-common.service\";\nimport * as i2 from \"../property/property.service\";\nimport * as i3 from \"../treeNode/tree-node.service\";\nimport * as i4 from \"../../../shared/utils/diagram-utils\";\nexport class UndoRedoService {\n  constructor(gojsCommonService, propertyService, treeNodeService, diagramUtils, ngZone) {\n    this.gojsCommonService = gojsCommonService;\n    this.propertyService = propertyService;\n    this.treeNodeService = treeNodeService;\n    this.diagramUtils = diagramUtils;\n    this.ngZone = ngZone;\n    this._diagram = null;\n    this._stateSubject = new BehaviorSubject({\n      canUndo: false,\n      canRedo: false,\n      undoText: '',\n      redoText: '',\n      isProcessing: false\n    });\n    this._operationSubject = new Subject();\n    this._pendingOperations = new Map();\n    this._activeTransactions = new Map();\n    this._destroy$ = new Subject();\n    this._isInitialized = false;\n    this.initializeService();\n  }\n  /**\n   * Observable for undo/redo state changes\n   */\n  get state$() {\n    return this._stateSubject.asObservable();\n  }\n  /**\n   * Observable for undo/redo operations\n   */\n  get operations$() {\n    return this._operationSubject.asObservable();\n  }\n  /**\n   * Initialize the service and set up diagram listeners\n   */\n  initializeService() {\n    if (this._isInitialized) return;\n    this.gojsCommonService.gojsDiagramChanges().pipe(takeUntil(this._destroy$)).subscribe(diagram => {\n      if (diagram) {\n        this.setDiagram(diagram);\n      }\n    });\n    this.setupKeyboardShortcuts();\n    this._isInitialized = true;\n  }\n  /**\n   * Set up keyboard shortcuts for undo/redo\n   */\n  setupKeyboardShortcuts() {\n    fromEvent(document, 'keydown').pipe(filter(event => (event.ctrlKey || event.metaKey) && !event.shiftKey), filter(event => event.key === 'z' || event.key === 'Z'), takeUntil(this._destroy$)).subscribe(event => {\n      event.preventDefault();\n      this.undo();\n    });\n    fromEvent(document, 'keydown').pipe(filter(event => (event.ctrlKey || event.metaKey) && event.shiftKey), filter(event => event.key === 'z' || event.key === 'Z'), takeUntil(this._destroy$)).subscribe(event => {\n      event.preventDefault();\n      this.redo();\n    });\n    fromEvent(document, 'keydown').pipe(filter(event => (event.ctrlKey || event.metaKey) && !event.shiftKey), filter(event => event.key === 'y' || event.key === 'Y'), takeUntil(this._destroy$)).subscribe(event => {\n      event.preventDefault();\n      this.redo();\n    });\n  }\n  /**\n   * Set the current diagram and configure undo manager\n   */\n  setDiagram(diagram) {\n    if (this._diagram) {\n      this.removeListeners();\n    }\n    this._diagram = diagram;\n    this.setupUndoManager();\n    this.addListeners();\n    this.updateState();\n  }\n  /**\n   * Configure GoJS UndoManager with optimal settings\n   */\n  setupUndoManager() {\n    if (!this._diagram) return;\n    const undoManager = this._diagram.undoManager;\n    undoManager.isEnabled = true;\n    undoManager.maxHistoryLength = 100; // Increased for better user experience\n    // Configure what should be included in undo/redo\n    undoManager.includesModel = true;\n    undoManager.includesSelection = false; // Don't include selection changes\n    undoManager.includesLayout = true; // Include layout changes\n  }\n  /**\n   * Add event listeners for model changes and undo/redo operations\n   */\n  addListeners() {\n    if (!this._diagram) return;\n    // Listen for model changes to update state\n    this._diagram.addModelChangedListener(this.onModelChanged.bind(this));\n    // Listen for undo manager changes\n    this._diagram.undoManager.addChangedListener(this.onUndoManagerChanged.bind(this));\n  }\n  /**\n   * Remove event listeners\n   */\n  removeListeners() {\n    if (!this._diagram) return;\n    this._diagram.removeModelChangedListener(this.onModelChanged.bind(this));\n    this._diagram.undoManager.removeChangedListener(this.onUndoManagerChanged.bind(this));\n  }\n  /**\n   * Handle model changes\n   */\n  onModelChanged(event) {\n    if (event.isTransactionFinished) {\n      this.updateState();\n      // Handle specific undo/redo events\n      if (event.propertyName === 'FinishedUndo') {\n        this.handleUndoOperation(event);\n      } else if (event.propertyName === 'FinishedRedo') {\n        this.handleRedoOperation(event);\n      }\n    }\n  }\n  /**\n   * Handle undo manager changes\n   */\n  onUndoManagerChanged(event) {\n    this.ngZone.run(() => {\n      this.updateState();\n    });\n  }\n  /**\n   * Update the current undo/redo state\n   */\n  updateState() {\n    if (!this._diagram) return;\n    const undoManager = this._diagram.undoManager;\n    const state = {\n      canUndo: undoManager.canUndo(),\n      canRedo: undoManager.canRedo(),\n      undoText: this.getUndoText(),\n      redoText: this.getRedoText(),\n      isProcessing: this._stateSubject.value.isProcessing\n    };\n    this._stateSubject.next(state);\n  }\n  /**\n   * Get descriptive text for undo operation\n   */\n  getUndoText() {\n    if (!this._diagram?.undoManager.canUndo()) return '';\n    const transaction = this._diagram.undoManager.transactionToUndo;\n    return transaction?.name || 'Undo';\n  }\n  /**\n   * Get descriptive text for redo operation\n   */\n  getRedoText() {\n    if (!this._diagram?.undoManager.canRedo()) return '';\n    const transaction = this._diagram.undoManager.transactionToRedo;\n    return transaction?.name || 'Redo';\n  }\n  /**\n   * Perform undo operation\n   */\n  undo() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this._diagram?.undoManager.canUndo() || _this._stateSubject.value.isProcessing) return;\n      _this.setProcessingState(true);\n      try {\n        _this._diagram.undoManager.undo();\n        yield _this.refreshAngularState();\n      } catch (error) {\n        console.error('Error during undo operation:', error);\n      } finally {\n        _this.setProcessingState(false);\n      }\n    })();\n  }\n  /**\n   * Perform redo operation\n   */\n  redo() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2._diagram?.undoManager.canRedo() || _this2._stateSubject.value.isProcessing) return;\n      _this2.setProcessingState(true);\n      try {\n        _this2._diagram.undoManager.redo();\n        yield _this2.refreshAngularState();\n      } catch (error) {\n        console.error('Error during redo operation:', error);\n      } finally {\n        _this2.setProcessingState(false);\n      }\n    })();\n  }\n  /**\n   * Set processing state\n   */\n  setProcessingState(isProcessing) {\n    const currentState = this._stateSubject.value;\n    this._stateSubject.next({\n      ...currentState,\n      isProcessing\n    });\n  }\n  /**\n   * Start a new transaction with a descriptive name\n   */\n  startTransaction(name) {\n    if (!this._diagram) return;\n    this._diagram.startTransaction(name);\n  }\n  /**\n   * Commit the current transaction\n   */\n  commitTransaction(name) {\n    if (!this._diagram) return;\n    this._diagram.commitTransaction(name);\n  }\n  /**\n   * Rollback the current transaction\n   */\n  rollbackTransaction() {\n    if (!this._diagram) return;\n    this._diagram.rollbackTransaction();\n  }\n  /**\n   * Execute an operation within a transaction\n   */\n  executeInTransaction(name, operation) {\n    if (!this._diagram) throw new Error('No diagram available');\n    this.startTransaction(name);\n    try {\n      const result = operation();\n      this.commitTransaction(name);\n      return result;\n    } catch (error) {\n      this.rollbackTransaction();\n      throw error;\n    }\n  }\n  /**\n   * Handle undo operation and refresh Angular state\n   */\n  handleUndoOperation(event) {\n    this._operationSubject.next({\n      type: 'undo',\n      operation: event\n    });\n  }\n  /**\n   * Handle redo operation and refresh Angular state\n   */\n  handleRedoOperation(event) {\n    this._operationSubject.next({\n      type: 'redo',\n      operation: event\n    });\n  }\n  /**\n   * Refresh Angular component state after undo/redo\n   */\n  refreshAngularState() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        _this3.ngZone.run(() => {\n          // Clear property panel\n          _this3.propertyService.setPropertyData(null);\n          // Refresh tree view\n          _this3.treeNodeService.refreshTreeView();\n          // Force change detection\n          setTimeout(() => {\n            _this3.updateState();\n            resolve();\n          }, 0);\n        });\n      });\n    })();\n  }\n  /**\n   * Clear undo/redo history\n   */\n  clearHistory() {\n    if (!this._diagram) return;\n    this._diagram.undoManager.clear();\n    this.updateState();\n  }\n  /**\n   * Get current undo/redo state\n   */\n  getCurrentState() {\n    return this._stateSubject.value;\n  }\n  /**\n   * Check if undo is available\n   */\n  canUndo() {\n    return this._diagram?.undoManager.canUndo() || false;\n  }\n  /**\n   * Check if redo is available\n   */\n  canRedo() {\n    return this._diagram?.undoManager.canRedo() || false;\n  }\n  /**\n   * Cleanup resources\n   */\n  ngOnDestroy() {\n    this._destroy$.next();\n    this._destroy$.complete();\n    this.removeListeners();\n  }\n  static #_ = this.ɵfac = function UndoRedoService_Factory(t) {\n    return new (t || UndoRedoService)(i0.ɵɵinject(i1.GojsCommonService), i0.ɵɵinject(i2.PropertyService), i0.ɵɵinject(i3.TreeNodeService), i0.ɵɵinject(i4.DiagramUtils), i0.ɵɵinject(i0.NgZone));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UndoRedoService,\n    factory: UndoRedoService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "fromEvent", "filter", "takeUntil", "UndoRedoService", "constructor", "gojsCommonService", "propertyService", "treeNodeService", "diagramUtils", "ngZone", "_diagram", "_stateSubject", "canUndo", "canRedo", "undoText", "redoText", "isProcessing", "_operationSubject", "_pendingOperations", "Map", "_activeTransactions", "_destroy$", "_isInitialized", "initializeService", "state$", "asObservable", "operations$", "gojsDiagramChanges", "pipe", "subscribe", "diagram", "setDiagram", "setupKeyboardShortcuts", "document", "event", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "key", "preventDefault", "undo", "redo", "removeListeners", "setupUndoManager", "addListeners", "updateState", "undoManager", "isEnabled", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "includesModel", "includesSelection", "includesLayout", "addModelChangedListener", "onModelChanged", "bind", "addChangedListener", "onUndoManagerChanged", "removeModelChangedListener", "removeChangedListener", "isTransactionFinished", "propertyName", "handleUndoOperation", "handleRedoOperation", "run", "state", "getUndoText", "getRedoText", "value", "next", "transaction", "transactionToUndo", "name", "transactionToRedo", "_this", "_asyncToGenerator", "setProcessingState", "refreshAngularState", "error", "console", "_this2", "currentState", "startTransaction", "commitTransaction", "rollbackTransaction", "executeInTransaction", "operation", "Error", "result", "type", "_this3", "Promise", "resolve", "setPropertyData", "refreshTreeView", "setTimeout", "clearHistory", "clear", "getCurrentState", "ngOnDestroy", "complete", "_", "i0", "ɵɵinject", "i1", "GojsCommonService", "i2", "PropertyService", "i3", "TreeNodeService", "i4", "DiagramUtils", "NgZone", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\undo-redo\\undo-redo.service.ts"], "sourcesContent": ["import { Injectable, NgZone } from '@angular/core';\nimport { BehaviorSubject, Observable, Subject, fromEvent } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport * as go from 'gojs';\nimport { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';\nimport { PropertyService } from '../property/property.service';\nimport { TreeNodeService } from '../treeNode/tree-node.service';\nimport { DiagramUtils } from '../../../shared/utils/diagram-utils';\n\nexport interface UndoRedoState {\n  canUndo: boolean;\n  canRedo: boolean;\n  undoText: string;\n  redoText: string;\n  isProcessing: boolean;\n}\n\nexport interface UndoRedoOperation {\n  id: string;\n  type: 'database' | 'ui' | 'both';\n  operation: () => Promise<void> | void;\n  rollback: () => Promise<void> | void;\n  description: string;\n  timestamp: number;\n}\n\nexport interface TransactionContext {\n  id: string;\n  name: string;\n  operations: UndoRedoOperation[];\n  isActive: boolean;\n  startTime: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UndoRedoService {\n  private _diagram: go.Diagram | null = null;\n  private _stateSubject = new BehaviorSubject<UndoRedoState>({\n    canUndo: false,\n    canRedo: false,\n    undoText: '',\n    redoText: '',\n    isProcessing: false\n  });\n\n  private _operationSubject = new Subject<{ type: 'undo' | 'redo', operation: any }>();\n  private _pendingOperations: Map<string, UndoRedoOperation> = new Map();\n  private _activeTransactions: Map<string, TransactionContext> = new Map();\n  private _destroy$ = new Subject<void>();\n  private _isInitialized = false;\n\n  constructor(\n    private gojsCommonService: GojsCommonService,\n    private propertyService: PropertyService,\n    private treeNodeService: TreeNodeService,\n    private diagramUtils: DiagramUtils,\n    private ngZone: NgZone\n  ) {\n    this.initializeService();\n  }\n\n  /**\n   * Observable for undo/redo state changes\n   */\n  get state$(): Observable<UndoRedoState> {\n    return this._stateSubject.asObservable();\n  }\n\n  /**\n   * Observable for undo/redo operations\n   */\n  get operations$(): Observable<{ type: 'undo' | 'redo', operation: any }> {\n    return this._operationSubject.asObservable();\n  }\n\n  /**\n   * Initialize the service and set up diagram listeners\n   */\n  private initializeService(): void {\n    if (this._isInitialized) return;\n\n    this.gojsCommonService.gojsDiagramChanges()\n      .pipe(takeUntil(this._destroy$))\n      .subscribe(diagram => {\n        if (diagram) {\n          this.setDiagram(diagram);\n        }\n      });\n\n    this.setupKeyboardShortcuts();\n    this._isInitialized = true;\n  }\n\n  /**\n   * Set up keyboard shortcuts for undo/redo\n   */\n  private setupKeyboardShortcuts(): void {\n    fromEvent<KeyboardEvent>(document, 'keydown')\n      .pipe(\n        filter(event => (event.ctrlKey || event.metaKey) && !event.shiftKey),\n        filter(event => event.key === 'z' || event.key === 'Z'),\n        takeUntil(this._destroy$)\n      )\n      .subscribe(event => {\n        event.preventDefault();\n        this.undo();\n      });\n\n    fromEvent<KeyboardEvent>(document, 'keydown')\n      .pipe(\n        filter(event => (event.ctrlKey || event.metaKey) && event.shiftKey),\n        filter(event => event.key === 'z' || event.key === 'Z'),\n        takeUntil(this._destroy$)\n      )\n      .subscribe(event => {\n        event.preventDefault();\n        this.redo();\n      });\n\n    fromEvent<KeyboardEvent>(document, 'keydown')\n      .pipe(\n        filter(event => (event.ctrlKey || event.metaKey) && !event.shiftKey),\n        filter(event => event.key === 'y' || event.key === 'Y'),\n        takeUntil(this._destroy$)\n      )\n      .subscribe(event => {\n        event.preventDefault();\n        this.redo();\n      });\n  }\n\n  /**\n   * Set the current diagram and configure undo manager\n   */\n  setDiagram(diagram: go.Diagram): void {\n    if (this._diagram) {\n      this.removeListeners();\n    }\n\n    this._diagram = diagram;\n    this.setupUndoManager();\n    this.addListeners();\n    this.updateState();\n  }\n\n  /**\n   * Configure GoJS UndoManager with optimal settings\n   */\n  private setupUndoManager(): void {\n    if (!this._diagram) return;\n\n    const undoManager = this._diagram.undoManager;\n    undoManager.isEnabled = true;\n    undoManager.maxHistoryLength = 100; // Increased for better user experience\n    \n    // Configure what should be included in undo/redo\n    undoManager.includesModel = true;\n    undoManager.includesSelection = false; // Don't include selection changes\n    undoManager.includesLayout = true; // Include layout changes\n  }\n\n  /**\n   * Add event listeners for model changes and undo/redo operations\n   */\n  private addListeners(): void {\n    if (!this._diagram) return;\n\n    // Listen for model changes to update state\n    this._diagram.addModelChangedListener(this.onModelChanged.bind(this));\n    \n    // Listen for undo manager changes\n    this._diagram.undoManager.addChangedListener(this.onUndoManagerChanged.bind(this));\n  }\n\n  /**\n   * Remove event listeners\n   */\n  private removeListeners(): void {\n    if (!this._diagram) return;\n\n    this._diagram.removeModelChangedListener(this.onModelChanged.bind(this));\n    this._diagram.undoManager.removeChangedListener(this.onUndoManagerChanged.bind(this));\n  }\n\n  /**\n   * Handle model changes\n   */\n  private onModelChanged(event: go.ChangedEvent): void {\n    if (event.isTransactionFinished) {\n      this.updateState();\n      \n      // Handle specific undo/redo events\n      if (event.propertyName === 'FinishedUndo') {\n        this.handleUndoOperation(event);\n      } else if (event.propertyName === 'FinishedRedo') {\n        this.handleRedoOperation(event);\n      }\n    }\n  }\n\n  /**\n   * Handle undo manager changes\n   */\n  private onUndoManagerChanged(event: go.ChangedEvent): void {\n    this.ngZone.run(() => {\n      this.updateState();\n    });\n  }\n\n  /**\n   * Update the current undo/redo state\n   */\n  private updateState(): void {\n    if (!this._diagram) return;\n\n    const undoManager = this._diagram.undoManager;\n    const state: UndoRedoState = {\n      canUndo: undoManager.canUndo(),\n      canRedo: undoManager.canRedo(),\n      undoText: this.getUndoText(),\n      redoText: this.getRedoText(),\n      isProcessing: this._stateSubject.value.isProcessing\n    };\n\n    this._stateSubject.next(state);\n  }\n\n  /**\n   * Get descriptive text for undo operation\n   */\n  private getUndoText(): string {\n    if (!this._diagram?.undoManager.canUndo()) return '';\n    \n    const transaction = this._diagram.undoManager.transactionToUndo;\n    return transaction?.name || 'Undo';\n  }\n\n  /**\n   * Get descriptive text for redo operation\n   */\n  private getRedoText(): string {\n    if (!this._diagram?.undoManager.canRedo()) return '';\n    \n    const transaction = this._diagram.undoManager.transactionToRedo;\n    return transaction?.name || 'Redo';\n  }\n\n  /**\n   * Perform undo operation\n   */\n  async undo(): Promise<void> {\n    if (!this._diagram?.undoManager.canUndo() || this._stateSubject.value.isProcessing) return;\n\n    this.setProcessingState(true);\n    \n    try {\n      this._diagram.undoManager.undo();\n      await this.refreshAngularState();\n    } catch (error) {\n      console.error('Error during undo operation:', error);\n    } finally {\n      this.setProcessingState(false);\n    }\n  }\n\n  /**\n   * Perform redo operation\n   */\n  async redo(): Promise<void> {\n    if (!this._diagram?.undoManager.canRedo() || this._stateSubject.value.isProcessing) return;\n\n    this.setProcessingState(true);\n    \n    try {\n      this._diagram.undoManager.redo();\n      await this.refreshAngularState();\n    } catch (error) {\n      console.error('Error during redo operation:', error);\n    } finally {\n      this.setProcessingState(false);\n    }\n  }\n\n  /**\n   * Set processing state\n   */\n  private setProcessingState(isProcessing: boolean): void {\n    const currentState = this._stateSubject.value;\n    this._stateSubject.next({\n      ...currentState,\n      isProcessing\n    });\n  }\n\n  /**\n   * Start a new transaction with a descriptive name\n   */\n  startTransaction(name: string): void {\n    if (!this._diagram) return;\n    this._diagram.startTransaction(name);\n  }\n\n  /**\n   * Commit the current transaction\n   */\n  commitTransaction(name?: string): void {\n    if (!this._diagram) return;\n    this._diagram.commitTransaction(name);\n  }\n\n  /**\n   * Rollback the current transaction\n   */\n  rollbackTransaction(): void {\n    if (!this._diagram) return;\n    this._diagram.rollbackTransaction();\n  }\n\n  /**\n   * Execute an operation within a transaction\n   */\n  executeInTransaction<T>(name: string, operation: () => T): T {\n    if (!this._diagram) throw new Error('No diagram available');\n\n    this.startTransaction(name);\n    try {\n      const result = operation();\n      this.commitTransaction(name);\n      return result;\n    } catch (error) {\n      this.rollbackTransaction();\n      throw error;\n    }\n  }\n\n  /**\n   * Handle undo operation and refresh Angular state\n   */\n  private handleUndoOperation(event: go.ChangedEvent): void {\n    this._operationSubject.next({ type: 'undo', operation: event });\n  }\n\n  /**\n   * Handle redo operation and refresh Angular state\n   */\n  private handleRedoOperation(event: go.ChangedEvent): void {\n    this._operationSubject.next({ type: 'redo', operation: event });\n  }\n\n  /**\n   * Refresh Angular component state after undo/redo\n   */\n  private async refreshAngularState(): Promise<void> {\n    return new Promise<void>((resolve) => {\n      this.ngZone.run(() => {\n        // Clear property panel\n        this.propertyService.setPropertyData(null);\n        \n        // Refresh tree view\n        this.treeNodeService.refreshTreeView();\n        \n        // Force change detection\n        setTimeout(() => {\n          this.updateState();\n          resolve();\n        }, 0);\n      });\n    });\n  }\n\n  /**\n   * Clear undo/redo history\n   */\n  clearHistory(): void {\n    if (!this._diagram) return;\n    this._diagram.undoManager.clear();\n    this.updateState();\n  }\n\n  /**\n   * Get current undo/redo state\n   */\n  getCurrentState(): UndoRedoState {\n    return this._stateSubject.value;\n  }\n\n  /**\n   * Check if undo is available\n   */\n  canUndo(): boolean {\n    return this._diagram?.undoManager.canUndo() || false;\n  }\n\n  /**\n   * Check if redo is available\n   */\n  canRedo(): boolean {\n    return this._diagram?.undoManager.canRedo() || false;\n  }\n\n  /**\n   * Cleanup resources\n   */\n  ngOnDestroy(): void {\n    this._destroy$.next();\n    this._destroy$.complete();\n    this.removeListeners();\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,EAAcC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACtE,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;AAmClD,OAAM,MAAOC,eAAe;EAgB1BC,YACUC,iBAAoC,EACpCC,eAAgC,EAChCC,eAAgC,EAChCC,YAA0B,EAC1BC,MAAc;IAJd,KAAAJ,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IApBR,KAAAC,QAAQ,GAAsB,IAAI;IAClC,KAAAC,aAAa,GAAG,IAAIb,eAAe,CAAgB;MACzDc,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE;KACf,CAAC;IAEM,KAAAC,iBAAiB,GAAG,IAAIlB,OAAO,EAA6C;IAC5E,KAAAmB,kBAAkB,GAAmC,IAAIC,GAAG,EAAE;IAC9D,KAAAC,mBAAmB,GAAoC,IAAID,GAAG,EAAE;IAChE,KAAAE,SAAS,GAAG,IAAItB,OAAO,EAAQ;IAC/B,KAAAuB,cAAc,GAAG,KAAK;IAS5B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEA;;;EAGA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACb,aAAa,CAACc,YAAY,EAAE;EAC1C;EAEA;;;EAGA,IAAIC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACT,iBAAiB,CAACQ,YAAY,EAAE;EAC9C;EAEA;;;EAGQF,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACD,cAAc,EAAE;IAEzB,IAAI,CAACjB,iBAAiB,CAACsB,kBAAkB,EAAE,CACxCC,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACmB,SAAS,CAAC,CAAC,CAC/BQ,SAAS,CAACC,OAAO,IAAG;MACnB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACC,UAAU,CAACD,OAAO,CAAC;;IAE5B,CAAC,CAAC;IAEJ,IAAI,CAACE,sBAAsB,EAAE;IAC7B,IAAI,CAACV,cAAc,GAAG,IAAI;EAC5B;EAEA;;;EAGQU,sBAAsBA,CAAA;IAC5BhC,SAAS,CAAgBiC,QAAQ,EAAE,SAAS,CAAC,CAC1CL,IAAI,CACH3B,MAAM,CAACiC,KAAK,IAAI,CAACA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAAK,CAACF,KAAK,CAACG,QAAQ,CAAC,EACpEpC,MAAM,CAACiC,KAAK,IAAIA,KAAK,CAACI,GAAG,KAAK,GAAG,IAAIJ,KAAK,CAACI,GAAG,KAAK,GAAG,CAAC,EACvDpC,SAAS,CAAC,IAAI,CAACmB,SAAS,CAAC,CAC1B,CACAQ,SAAS,CAACK,KAAK,IAAG;MACjBA,KAAK,CAACK,cAAc,EAAE;MACtB,IAAI,CAACC,IAAI,EAAE;IACb,CAAC,CAAC;IAEJxC,SAAS,CAAgBiC,QAAQ,EAAE,SAAS,CAAC,CAC1CL,IAAI,CACH3B,MAAM,CAACiC,KAAK,IAAI,CAACA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAAKF,KAAK,CAACG,QAAQ,CAAC,EACnEpC,MAAM,CAACiC,KAAK,IAAIA,KAAK,CAACI,GAAG,KAAK,GAAG,IAAIJ,KAAK,CAACI,GAAG,KAAK,GAAG,CAAC,EACvDpC,SAAS,CAAC,IAAI,CAACmB,SAAS,CAAC,CAC1B,CACAQ,SAAS,CAACK,KAAK,IAAG;MACjBA,KAAK,CAACK,cAAc,EAAE;MACtB,IAAI,CAACE,IAAI,EAAE;IACb,CAAC,CAAC;IAEJzC,SAAS,CAAgBiC,QAAQ,EAAE,SAAS,CAAC,CAC1CL,IAAI,CACH3B,MAAM,CAACiC,KAAK,IAAI,CAACA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAAK,CAACF,KAAK,CAACG,QAAQ,CAAC,EACpEpC,MAAM,CAACiC,KAAK,IAAIA,KAAK,CAACI,GAAG,KAAK,GAAG,IAAIJ,KAAK,CAACI,GAAG,KAAK,GAAG,CAAC,EACvDpC,SAAS,CAAC,IAAI,CAACmB,SAAS,CAAC,CAC1B,CACAQ,SAAS,CAACK,KAAK,IAAG;MACjBA,KAAK,CAACK,cAAc,EAAE;MACtB,IAAI,CAACE,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEA;;;EAGAV,UAAUA,CAACD,OAAmB;IAC5B,IAAI,IAAI,CAACpB,QAAQ,EAAE;MACjB,IAAI,CAACgC,eAAe,EAAE;;IAGxB,IAAI,CAAChC,QAAQ,GAAGoB,OAAO;IACvB,IAAI,CAACa,gBAAgB,EAAE;IACvB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA;;;EAGQF,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;IAEpB,MAAMoC,WAAW,GAAG,IAAI,CAACpC,QAAQ,CAACoC,WAAW;IAC7CA,WAAW,CAACC,SAAS,GAAG,IAAI;IAC5BD,WAAW,CAACE,gBAAgB,GAAG,GAAG,CAAC,CAAC;IAEpC;IACAF,WAAW,CAACG,aAAa,GAAG,IAAI;IAChCH,WAAW,CAACI,iBAAiB,GAAG,KAAK,CAAC,CAAC;IACvCJ,WAAW,CAACK,cAAc,GAAG,IAAI,CAAC,CAAC;EACrC;EAEA;;;EAGQP,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAClC,QAAQ,EAAE;IAEpB;IACA,IAAI,CAACA,QAAQ,CAAC0C,uBAAuB,CAAC,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAErE;IACA,IAAI,CAAC5C,QAAQ,CAACoC,WAAW,CAACS,kBAAkB,CAAC,IAAI,CAACC,oBAAoB,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;EACpF;EAEA;;;EAGQZ,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;IAEpB,IAAI,CAACA,QAAQ,CAAC+C,0BAA0B,CAAC,IAAI,CAACJ,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE,IAAI,CAAC5C,QAAQ,CAACoC,WAAW,CAACY,qBAAqB,CAAC,IAAI,CAACF,oBAAoB,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;EACvF;EAEA;;;EAGQD,cAAcA,CAACnB,KAAsB;IAC3C,IAAIA,KAAK,CAACyB,qBAAqB,EAAE;MAC/B,IAAI,CAACd,WAAW,EAAE;MAElB;MACA,IAAIX,KAAK,CAAC0B,YAAY,KAAK,cAAc,EAAE;QACzC,IAAI,CAACC,mBAAmB,CAAC3B,KAAK,CAAC;OAChC,MAAM,IAAIA,KAAK,CAAC0B,YAAY,KAAK,cAAc,EAAE;QAChD,IAAI,CAACE,mBAAmB,CAAC5B,KAAK,CAAC;;;EAGrC;EAEA;;;EAGQsB,oBAAoBA,CAACtB,KAAsB;IACjD,IAAI,CAACzB,MAAM,CAACsD,GAAG,CAAC,MAAK;MACnB,IAAI,CAAClB,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEA;;;EAGQA,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;IAEpB,MAAMoC,WAAW,GAAG,IAAI,CAACpC,QAAQ,CAACoC,WAAW;IAC7C,MAAMkB,KAAK,GAAkB;MAC3BpD,OAAO,EAAEkC,WAAW,CAAClC,OAAO,EAAE;MAC9BC,OAAO,EAAEiC,WAAW,CAACjC,OAAO,EAAE;MAC9BC,QAAQ,EAAE,IAAI,CAACmD,WAAW,EAAE;MAC5BlD,QAAQ,EAAE,IAAI,CAACmD,WAAW,EAAE;MAC5BlD,YAAY,EAAE,IAAI,CAACL,aAAa,CAACwD,KAAK,CAACnD;KACxC;IAED,IAAI,CAACL,aAAa,CAACyD,IAAI,CAACJ,KAAK,CAAC;EAChC;EAEA;;;EAGQC,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACvD,QAAQ,EAAEoC,WAAW,CAAClC,OAAO,EAAE,EAAE,OAAO,EAAE;IAEpD,MAAMyD,WAAW,GAAG,IAAI,CAAC3D,QAAQ,CAACoC,WAAW,CAACwB,iBAAiB;IAC/D,OAAOD,WAAW,EAAEE,IAAI,IAAI,MAAM;EACpC;EAEA;;;EAGQL,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACxD,QAAQ,EAAEoC,WAAW,CAACjC,OAAO,EAAE,EAAE,OAAO,EAAE;IAEpD,MAAMwD,WAAW,GAAG,IAAI,CAAC3D,QAAQ,CAACoC,WAAW,CAAC0B,iBAAiB;IAC/D,OAAOH,WAAW,EAAEE,IAAI,IAAI,MAAM;EACpC;EAEA;;;EAGM/B,IAAIA,CAAA;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MACR,IAAI,CAACD,KAAI,CAAC/D,QAAQ,EAAEoC,WAAW,CAAClC,OAAO,EAAE,IAAI6D,KAAI,CAAC9D,aAAa,CAACwD,KAAK,CAACnD,YAAY,EAAE;MAEpFyD,KAAI,CAACE,kBAAkB,CAAC,IAAI,CAAC;MAE7B,IAAI;QACFF,KAAI,CAAC/D,QAAQ,CAACoC,WAAW,CAACN,IAAI,EAAE;QAChC,MAAMiC,KAAI,CAACG,mBAAmB,EAAE;OACjC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;OACrD,SAAS;QACRJ,KAAI,CAACE,kBAAkB,CAAC,KAAK,CAAC;;IAC/B;EACH;EAEA;;;EAGMlC,IAAIA,CAAA;IAAA,IAAAsC,MAAA;IAAA,OAAAL,iBAAA;MACR,IAAI,CAACK,MAAI,CAACrE,QAAQ,EAAEoC,WAAW,CAACjC,OAAO,EAAE,IAAIkE,MAAI,CAACpE,aAAa,CAACwD,KAAK,CAACnD,YAAY,EAAE;MAEpF+D,MAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAC;MAE7B,IAAI;QACFI,MAAI,CAACrE,QAAQ,CAACoC,WAAW,CAACL,IAAI,EAAE;QAChC,MAAMsC,MAAI,CAACH,mBAAmB,EAAE;OACjC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;OACrD,SAAS;QACRE,MAAI,CAACJ,kBAAkB,CAAC,KAAK,CAAC;;IAC/B;EACH;EAEA;;;EAGQA,kBAAkBA,CAAC3D,YAAqB;IAC9C,MAAMgE,YAAY,GAAG,IAAI,CAACrE,aAAa,CAACwD,KAAK;IAC7C,IAAI,CAACxD,aAAa,CAACyD,IAAI,CAAC;MACtB,GAAGY,YAAY;MACfhE;KACD,CAAC;EACJ;EAEA;;;EAGAiE,gBAAgBA,CAACV,IAAY;IAC3B,IAAI,CAAC,IAAI,CAAC7D,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,CAACuE,gBAAgB,CAACV,IAAI,CAAC;EACtC;EAEA;;;EAGAW,iBAAiBA,CAACX,IAAa;IAC7B,IAAI,CAAC,IAAI,CAAC7D,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,CAACwE,iBAAiB,CAACX,IAAI,CAAC;EACvC;EAEA;;;EAGAY,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACzE,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,CAACyE,mBAAmB,EAAE;EACrC;EAEA;;;EAGAC,oBAAoBA,CAAIb,IAAY,EAAEc,SAAkB;IACtD,IAAI,CAAC,IAAI,CAAC3E,QAAQ,EAAE,MAAM,IAAI4E,KAAK,CAAC,sBAAsB,CAAC;IAE3D,IAAI,CAACL,gBAAgB,CAACV,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMgB,MAAM,GAAGF,SAAS,EAAE;MAC1B,IAAI,CAACH,iBAAiB,CAACX,IAAI,CAAC;MAC5B,OAAOgB,MAAM;KACd,CAAC,OAAOV,KAAK,EAAE;MACd,IAAI,CAACM,mBAAmB,EAAE;MAC1B,MAAMN,KAAK;;EAEf;EAEA;;;EAGQhB,mBAAmBA,CAAC3B,KAAsB;IAChD,IAAI,CAACjB,iBAAiB,CAACmD,IAAI,CAAC;MAAEoB,IAAI,EAAE,MAAM;MAAEH,SAAS,EAAEnD;IAAK,CAAE,CAAC;EACjE;EAEA;;;EAGQ4B,mBAAmBA,CAAC5B,KAAsB;IAChD,IAAI,CAACjB,iBAAiB,CAACmD,IAAI,CAAC;MAAEoB,IAAI,EAAE,MAAM;MAAEH,SAAS,EAAEnD;IAAK,CAAE,CAAC;EACjE;EAEA;;;EAGc0C,mBAAmBA,CAAA;IAAA,IAAAa,MAAA;IAAA,OAAAf,iBAAA;MAC/B,OAAO,IAAIgB,OAAO,CAAQC,OAAO,IAAI;QACnCF,MAAI,CAAChF,MAAM,CAACsD,GAAG,CAAC,MAAK;UACnB;UACA0B,MAAI,CAACnF,eAAe,CAACsF,eAAe,CAAC,IAAI,CAAC;UAE1C;UACAH,MAAI,CAAClF,eAAe,CAACsF,eAAe,EAAE;UAEtC;UACAC,UAAU,CAAC,MAAK;YACdL,MAAI,CAAC5C,WAAW,EAAE;YAClB8C,OAAO,EAAE;UACX,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;;;EAGAI,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACrF,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,CAACoC,WAAW,CAACkD,KAAK,EAAE;IACjC,IAAI,CAACnD,WAAW,EAAE;EACpB;EAEA;;;EAGAoD,eAAeA,CAAA;IACb,OAAO,IAAI,CAACtF,aAAa,CAACwD,KAAK;EACjC;EAEA;;;EAGAvD,OAAOA,CAAA;IACL,OAAO,IAAI,CAACF,QAAQ,EAAEoC,WAAW,CAAClC,OAAO,EAAE,IAAI,KAAK;EACtD;EAEA;;;EAGAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACH,QAAQ,EAAEoC,WAAW,CAACjC,OAAO,EAAE,IAAI,KAAK;EACtD;EAEA;;;EAGAqF,WAAWA,CAAA;IACT,IAAI,CAAC7E,SAAS,CAAC+C,IAAI,EAAE;IACrB,IAAI,CAAC/C,SAAS,CAAC8E,QAAQ,EAAE;IACzB,IAAI,CAACzD,eAAe,EAAE;EACxB;EAAC,QAAA0D,CAAA,G;qBApXUjG,eAAe,EAAAkG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAU,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAf7G,eAAe;IAAA8G,OAAA,EAAf9G,eAAe,CAAA+G,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}