{"toolbar": {"hello": "Hello,", "user": "User menu", "about": "About", "logout": "Logout", "language": "Languages", "savingMsg": "Saving...", "clearDiagram": "Clear Diagram", "downloadDiagram": "Download Diagram", "download-current": "Current Diagram", "download-all": "All Diagrams", "download-only-images": "Images", "download-with-details": "Report", "onGridView": "Activate Grid Layout", "offGridView": "Deactivate Grid Layout", "lastModified": "Last Modified", "versionHistory": "Version History", "releaseNew": "Release New Version"}, "lang": {"default": "Default language ({{code}})", "specific": "{{lang}} (default)"}, "errors": {"unhandled": {"content": "An unhandled error has occurred, please contact your administrator with the following details.", "header": "Unhandled error"}, "0": {"content": "The Uml App API service is not available or the configured URL is not valid. Please contact your administrator with the following details.", "header": "API service not found"}, "400": {"default": {"content": "A request on the Uml App API service is not correctly build or the parameters have changed. Please contact your administrator with the following details.", "header": "Bad request"}}, "401": {"default": {"content": "The connection to Uml App API service is not available for some reasons. Please contact your administrator with the following details.", "header": "Service unavailable"}}, "403": {"default": {"content": "The resource you are trying to access is protected and you are not authorized to view it. Please contact your administrator with the following details.", "header": "Access forbidden"}}, "404": {"default": {"content": "The requested resource was not found or the request URL does not exist. Please contact your administrator with the following details.", "header": "Resource not found"}}, "500": {"default": {"content": "The server has encountered an internal error or a timeout problem and cannot provide the requested resource. Please contact your administrator with the following details.", "header": "Internal server error"}}, "lockedProject": {"header": "Project locked!", "content": "This project is currently locked by "}}, "errorMessage": {"details": "Details"}, "window": {"close": "Close", "cancel": "Cancel", "save": "Save"}, "dashboard": {"title": "Projects", "btnText": "+ New Project", "editProject": "Edit Project", "newProject": "New Project", "deleteProject": "Delete Project", "noProject": "No projects available", "loading": "Loading projects...", "error": {"loadingProjects": "Error loading projects. Please try again."}, "shareProject": "Share Project", "filter": {"projectType": {"title": "Project Type", "placeholder": "Project Type"}, "productLine": {"title": "Product Line", "placeholder": "Product Line"}, "selfProject": {"title": "Only My Projects"}, "clearFilter": "Clear all filter"}, "search": {"placeholder": "Search by name, description, type, product line, or owner..."}, "table": {"siNo": "No.", "name": "Name", "description": "Project Description", "type": "Project Type", "productLine": "Product Line", "action": "Action", "filter": "Filter", "owner": "Owner", "lastModified": "Last Modified", "open": "Open", "edit": "Edit", "share": "Share", "delete": "Delete"}}, "diagram": {"components": "Components", "library": "Library", "properties": "Properties", "propertyDescription": "Please select any library element to show the properties", "selectedDiagram": "Selected Diagram", "add": "Add", "edit": "Edit", "delete": "Delete", "openMenu": "Open Menu", "createDiagram": "Create Diagram", "editDiagram": "Edit Diagram", "diagramPlaceholderText": "Diagram name", "create": "Create", "save": "Save", "method": "Method", "attribute": "Attribute", "literal": "Literal", "search": "Search in Library...", "notFound": "No Node found", "downloadNotAllowedMsg": "Exportation is not possible because diagram is blank", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "diagramsList": "Diagrams"}, "folder": {"header": "Create New Folder", "placeholder": "Enter folder name"}, "class": {"header": "Create New Class", "placeholder": "Enter class name"}, "enumeration": {"header": "Create New Enumeration", "placeholder": "Enter enumeration name"}, "attribute": {"header": "Create New Attribute", "placeholder": "Enter attribute name"}, "method": {"header": "Create New Method", "placeholder": "Enter method name"}, "literal": {"header": "Create New Literal", "placeholder": "Enter literal name"}, "dialog": {"yes": "Yes", "no": "No", "title": "Do you want to delete?", "create": "Create", "save": "Save", "projectName": "Project Name", "description": "Description", "productLine": "Product Line", "search": "Search Product Line", "noResults": "No Data Found", "type": "Type", "deleteTitle": "Do you want to delete the link from all diagrams?"}, "shareDialog": {"title": "Share Project", "accessTitle": "People with access", "addPeople": "Add People", "copyBtn": "Copy Link", "access": "Access", "email": "Email", "0": "Admin", "1": "Editor", "2": "Viewer", "send": "Send", "removeAccess": "Remove Access"}, "snackBar": {"deleteProjectMsg": "Project Deleted Successfully", "deleteTempClassMsg": "Template Class Deleted Successfully", "deleteTempEnumMsg": "Template Enumeration Deleted Successfully", "deleteAttributeMsg": "Attribute Deleted Successfully", "deleteLiteralMsg": "Enumeration Literal Deleted Successfully", "diagramDeleteInfo": "You have no diagram. Please add one.", "diagramDeleteMsg": "Diagram Deleted Successfully", "diagramUpdateMsg": "Diagram Updated Successfully", "projectUpdatedMsg": "Project updated Successfully", "projectCreationMsg": "Project Created Successfully", "deleteFolderMsg": "Folder Deleted Successfully", "createFolderMsg": "Folder Created Successfully", "projectShareMsg": "Project Shared Successfully", "accessChangedMsg": "Access Changed Successfully", "commentDeleteMsg": "Comment Deleted Successfully", "permissionRemovedMsg": "Project Permission has been removed successfully", "linkCopiedMsg": "Link copied to clipboard", "linkToLinkAlreadyExists": "Already another associative class is linked with this linked"}, "property": {"name": "Name", "color": "Color", "type": "Type", "description": "Description", "volumetry": "Volumetry", "tag": "Tag", "fromComment": "From Comment", "toComment": "To Comment"}, "about": {"version": "Version", "releaseDate": "Release Date", "descriptionMsg": "The UML Web Application allows users to create and manage UML diagrams by dragging and dropping components like classes, enums, and packages, with options to link, resize, and customize them. Users can organize projects in folders, activate grid alignment, and download diagrams as PDFs. It offers an intuitive interface for designing and managing professional UML models."}, "colorPicker": {"customColor": "Custom Color", "apply": "Apply", "primaryColors": "Primary colors", "extendedColors": "Extended colors", "selectColor": "Select Color", "colors": {"red": "Red", "green": "Green", "blue": "Blue", "lightBlue": "Light Blue", "skyBlue": "Sky Blue", "yellow": "Yellow", "darkGray": "Dark Gray", "gray": "<PERSON>", "lightGray": "Light Gray", "darkRed": "Dark Red", "lightPink": "Light Pink", "pink": "Pink", "darkGreen": "Dark Green", "mintGreen": "Mint Green", "brightGreen": "Bright Green", "olive": "<PERSON>", "orange": "Orange", "lightOrange": "Light Orange", "brown": "<PERSON>", "salmon": "Salmon", "coral": "Coral", "cyan": "<PERSON><PERSON>", "maroon": "Maroon", "purple": "Purple"}}}