{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, effect, HostListener, ViewChild } from '@angular/core';\nimport * as go from 'gojs';\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\nimport { LibraryTreeComponent } from 'src/app/core/components/library-tree/library-tree.component';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { environment } from 'src/environments/environment';\nexport let DiagramEditorComponent = class DiagramEditorComponent {\n  constructor(diagramService, projectService, propertyService, route, router, linkService, accessService, goJsService, diagramUtils, loaderService, treeNodeService, navbarService, dialog, appService, versionHistoryService, undoRedoService, transactionManagerService) {\n    this.diagramService = diagramService;\n    this.projectService = projectService;\n    this.propertyService = propertyService;\n    this.route = route;\n    this.router = router;\n    this.linkService = linkService;\n    this.accessService = accessService;\n    this.goJsService = goJsService;\n    this.diagramUtils = diagramUtils;\n    this.loaderService = loaderService;\n    this.treeNodeService = treeNodeService;\n    this.navbarService = navbarService;\n    this.dialog = dialog;\n    this.appService = appService;\n    this.versionHistoryService = versionHistoryService;\n    this.undoRedoService = undoRedoService;\n    this.transactionManagerService = transactionManagerService;\n    this.$ = go.GraphObject.make;\n    this.isComponentPanelExpanded = false;\n    this.isLibraryPanelExpanded = true;\n    this.diagrams = [];\n    this.selectedDiagramId = -1;\n    this.hasEditAccessOnly = false;\n    this.attributeTypes = [];\n    this._downloadSub = null;\n    this._downloadAllSub = null;\n    this._currentProjectSub = null;\n    this._colorSelectionSub = null;\n    this._diagramSub = null;\n    this._currentProjDiagramsSub = null;\n    this._deleteDiagramSub = null;\n    this._propertyDataSub = null;\n    this._attributeTypeSub = null;\n    this.showVersionHistory = false;\n    this.isLoading$ = this.loaderService.isLoading$;\n    this._diagramLayoutSub = null;\n    this.selectedNodeTag = TreeNodeTag.Project;\n    effect(() => {\n      this.showVersionHistory = this.navbarService.showVersionHistory();\n    });\n  }\n  ngOnInit() {\n    this.appService.setIsInitProject(true);\n    go.Diagram.licenseKey = environment.licenseKey;\n    this.projectId = parseInt(this.route.snapshot.paramMap.get('id') ?? '');\n    // Check if a specific diagram ID is provided in the route\n    const diagramIdParam = this.route.snapshot.paramMap.get('idDiagram');\n    const initialDiagramId = diagramIdParam ? parseInt(diagramIdParam) : undefined;\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${initialDiagramId}`;\n    if (localStorage.getItem('reloaded') || localStorage.getItem('copyUrl')) {\n      // Open the project with the specific diagram if provided\n      this.projectService.openProject(this.projectId, true, initialDiagramId);\n      this.linkService.initLinkTypes();\n      localStorage.removeItem('reloaded');\n      localStorage.removeItem('copyUrl');\n    }\n    this.configureDiagramProperties();\n    this.initProject();\n    this.accessService.accessTypeChanges().subscribe(response => {\n      this.hasEditAccessOnly = response != AccessType.Viewer;\n    });\n    this.subscribeToDiagramService();\n    this._attributeTypeSub = this.diagramUtils.getAttributeTypes().subscribe(options => {\n      this.attributeTypes = options;\n    });\n  }\n  unloadHandler(event) {\n    event.preventDefault();\n    this.projectService.handleProjectUnlock(this.projectId);\n    localStorage.setItem('reloaded', 'true');\n  }\n  /**\n   * Configures properties for the main diagram.\n   * @memberof DiagramEditorComponent\n   */\n  configureDiagramProperties() {\n    this.diagram = this.$(go.Diagram, 'diagramDiv', {\n      initialAutoScale: go.AutoScale.Uniform,\n      allowCopy: false,\n      'animationManager.isEnabled': false,\n      'linkingTool.isEnabled': true,\n      'draggingTool.isGridSnapEnabled': true,\n      'linkingTool.isUnconnectedLinkValid': false,\n      'linkingTool.portGravity': 20,\n      'relinkingTool.isUnconnectedLinkValid': false,\n      'relinkingTool.portGravity': 10,\n      'linkReshapingTool.handleArchetype': this.$(go.Shape, 'Diamond', {\n        desiredSize: new go.Size(7, 7),\n        fill: 'lightblue',\n        stroke: '#0069B4'\n      }),\n      mouseDrop: e => this.goJsService.handleDropCompletion(e, null),\n      'commandHandler.archetypeGroupData': {\n        isGroup: true,\n        text: 'Group',\n        horiz: false\n      },\n      'undoManager.isEnabled': true,\n      'relinkingTool.isEnabled': true\n    });\n    this.diagram.grid = new go.Panel('Grid', {\n      gridCellSize: new go.Size(10, 10)\n    }).add(new go.Shape('LineH', {\n      stroke: 'lightgray',\n      strokeWidth: 0.5,\n      interval: 1\n    }), new go.Shape('LineH', {\n      stroke: 'gray',\n      strokeWidth: 0.5,\n      interval: 5\n    }), new go.Shape('LineH', {\n      stroke: 'gray',\n      strokeWidth: 1.0,\n      interval: 10\n    }), new go.Shape('LineV', {\n      stroke: 'lightgray',\n      strokeWidth: 0.5,\n      interval: 1\n    }), new go.Shape('LineV', {\n      stroke: 'gray',\n      strokeWidth: 0.5,\n      interval: 5\n    }), new go.Shape('LineV', {\n      stroke: 'gray',\n      strokeWidth: 1.0,\n      interval: 10\n    }));\n  }\n  initProject() {\n    this._diagramSub = this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram && diagram.id) {\n        this.currentDiagram = diagram;\n        this.selectedDiagramId = this.currentDiagram.id;\n        this.diagramService.getDiagramDetails(diagram.id);\n      }\n    });\n    this._currentProjDiagramsSub = this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      if (diagrams.length > 0) {\n        this.diagrams = diagrams;\n      }\n    });\n    this._currentProjectSub = this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) {\n        this.linkService.setProjectLinks(project);\n        this.goJsService.initDiagram(this.diagram);\n        this.diagramService.getPaletteDiagramDetails();\n      }\n    });\n  }\n  onDiagramSelectionChange(diagramId) {\n    this.selectedDiagramId = diagramId;\n    let activeDiagram = this.diagrams.find(d => d.id === diagramId);\n    // If not found in main diagrams, search in project folders\n    if (!activeDiagram && this.project && this.project.folders) {\n      const foundDiagram = this.findDiagramInFolders(this.project.folders, diagramId);\n      if (foundDiagram) {\n        activeDiagram = foundDiagram;\n      }\n    }\n    if (activeDiagram) {\n      this.diagramUtils.setActiveDiagram(activeDiagram);\n    }\n    this.propertyService.setPropertyData(null);\n    // Update the URL to include the selected diagram ID without reloading the page\n    this.router.navigate([`/editor/${this.projectId}/diagram/${diagramId}`], {\n      replaceUrl: true // Replace the current URL instead of adding a new history entry\n    });\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${diagramId}`;\n  }\n  /**\n   * Subscribes to various diagram service subjects to handle diagram actions.\n   */\n  subscribeToDiagramService() {\n    this._deleteDiagramSub = this.diagramService.deleteDiagramEvent.subscribe(() => {\n      this.onDeleteDiagram();\n    });\n    this._downloadSub = this.diagramService.downloadDiagramEvent.subscribe(() => {\n      this.diagramService.initiateDiagramDownload(true, false);\n    });\n    this._downloadAllSub = this.diagramService.downloadAllDiagramEvent.subscribe(isForOnlyImage => {\n      this.diagramService.initiateDiagramDownload(false, isForOnlyImage);\n      this.onDiagramSelectionChange(this.currentDiagram.id);\n    });\n    this._propertyDataSub = this.propertyService.propertyDataChanges().subscribe(propertyData => {\n      if (propertyData) this.propertyData = propertyData;\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      if (this.diagram) this.diagram.allowDrop = diagrams.length > 0;\n    });\n  }\n  onDeleteDiagram() {\n    const dialogRef = this.dialog.open(DialogConfirmationComponent, {\n      width: '320px',\n      data: {\n        title: 'dialog.title',\n        reject: 'dialog.no',\n        confirm: 'dialog.yes'\n      }\n    });\n    dialogRef.afterClosed().subscribe(isConfirm => {\n      if (isConfirm) {\n        if (this.selectedDiagramId) {\n          this.diagramService.deleteDiagram(this.selectedDiagramId);\n          this.treeNodeService.deleteDiagram(`atTag${GojsNodeCategory.Diagram}_${this.selectedDiagramId}`);\n          this.diagrams = this.diagrams.filter(diagram => diagram.id !== this.selectedDiagramId);\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\n          if (this.diagrams.length > 0) {\n            this.currentDiagram = this.diagrams[0];\n            this.selectedDiagramId = this.currentDiagram.id;\n            this.diagramUtils.setActiveDiagram(this.currentDiagram);\n            // Update the URL to reflect the new active diagram\n            this.router.navigate([`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`], {\n              replaceUrl: true\n            });\n          } else {\n            this.diagramUtils.setActiveDiagram(null);\n            // If there are no diagrams left, navigate to a placeholder URL\n            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {\n              replaceUrl: true\n            });\n          }\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.linkService.clearLinks();\n    this.linkService.clearLinkToLinks();\n    this.versionHistoryService.setSelectedVersion(null);\n    this.projectService.handleProjectUnlock(this.projectId);\n    if (this._downloadSub) this._downloadSub.unsubscribe();\n    if (this._downloadAllSub) this._downloadAllSub.unsubscribe();\n    if (this._colorSelectionSub) this._colorSelectionSub.unsubscribe();\n    if (this._diagramSub) this._diagramSub.unsubscribe();\n    if (this._currentProjDiagramsSub) this._currentProjDiagramsSub.unsubscribe();\n    if (this._deleteDiagramSub) this._deleteDiagramSub.unsubscribe();\n    if (this._propertyDataSub) this._propertyDataSub.unsubscribe();\n    if (this._currentProjectSub) this._currentProjectSub.unsubscribe();\n    if (this._attributeTypeSub) this._attributeTypeSub.unsubscribe();\n    if (this._diagramLayoutSub) this._diagramLayoutSub.unsubscribe();\n    this.propertyService.setPropertyData(null);\n  }\n  /**\n   * Subscribes to color selection changes and updates the component state accordingly.\n   */\n  onColorPickerSelection(isClicked) {\n    if (isClicked) {\n      this.isComponentPanelExpanded = false;\n      this.isLibraryPanelExpanded = false;\n    }\n  }\n  /**\n   * Subscribes to property data updates and handles updates to the diagram and palette.\n   */\n  onUpdateProperties(updatedNode, treeNode) {\n    this.diagramService.getUpdatedProperties(updatedNode, treeNode);\n  }\n  onCreateFolder(event) {\n    event.stopPropagation();\n    this.goJsService.createNewFolder('New Folder', this.projectId);\n    if (this.libraryTreeComponent.dataSource && this.libraryTreeComponent.dataSource.data.length > 0) {\n      this.libraryTreeComponent.treeControl.expand(this.libraryTreeComponent.dataSource.data[0]);\n    }\n  }\n  // Function to open version history\n  openVersionHistory() {\n    this.navbarService.setShowVersionHistory(true);\n    this.accessService.setProjectAccess(AccessType.Viewer);\n  }\n  // Function to close version history\n  closeVersionHistory() {\n    this.navbarService.setShowVersionHistory(false);\n    // this.accessService.setProjectAccess(this.project.accessType);\n  }\n  /**\n   * Recursively searches for a diagram in project folders by ID\n   * @param folders - Array of folders to search in\n   * @param diagramId - ID of the diagram to find\n   * @returns The found diagram or null if not found\n   */\n  findDiagramInFolders(folders, diagramId) {\n    for (const folder of folders) {\n      // Search in current folder's diagrams\n      if (folder.diagrams) {\n        const foundDiagram = folder.diagrams.find(d => d.id === diagramId);\n        if (foundDiagram) {\n          return foundDiagram;\n        }\n      }\n      // Recursively search in child folders\n      if (folder.childFolders && folder.childFolders.length > 0) {\n        const foundInChild = this.findDiagramInFolders(folder.childFolders, diagramId);\n        if (foundInChild) {\n          return foundInChild;\n        }\n      }\n    }\n    return null;\n  }\n};\n__decorate([ViewChild(LibraryTreeComponent)], DiagramEditorComponent.prototype, \"libraryTreeComponent\", void 0);\n__decorate([HostListener('window:beforeunload', ['$event'])], DiagramEditorComponent.prototype, \"unloadHandler\", null);\nDiagramEditorComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  templateUrl: './diagram-editor.component.html',\n  styleUrls: ['./diagram-editor.component.scss']\n})], DiagramEditorComponent);", "map": {"version": 3, "names": ["Component", "effect", "HostListener", "ViewChild", "go", "DialogConfirmationComponent", "LibraryTreeComponent", "GojsNodeCategory", "AccessType", "TreeNodeTag", "environment", "DiagramEditorComponent", "constructor", "diagramService", "projectService", "propertyService", "route", "router", "linkService", "accessService", "goJsService", "diagramUtils", "loaderService", "treeNodeService", "navbarService", "dialog", "appService", "versionHistoryService", "undoRedoService", "transactionManagerService", "$", "GraphObject", "make", "isComponentPanelExpanded", "isLibraryPanelExpanded", "diagrams", "selectedDiagramId", "hasEditAccessOnly", "attributeTypes", "_downloadSub", "_downloadAllSub", "_currentProjectSub", "_colorSelectionSub", "_diagramSub", "_currentProjDiagramsSub", "_deleteDiagramSub", "_propertyDataSub", "_attributeTypeSub", "showVersionHistory", "isLoading$", "_diagramLayoutSub", "selectedNodeTag", "Project", "ngOnInit", "setIsInitProject", "Diagram", "licenseKey", "projectId", "parseInt", "snapshot", "paramMap", "get", "diagramIdParam", "initialDiagramId", "undefined", "localStorage", "getItem", "openProject", "initLinkTypes", "removeItem", "configureDiagramProperties", "initProject", "accessTypeChanges", "subscribe", "response", "Viewer", "subscribeToDiagramService", "getAttributeTypes", "options", "unload<PERSON><PERSON><PERSON>", "event", "preventDefault", "handleProjectUnlock", "setItem", "diagram", "initialAutoScale", "AutoScale", "Uniform", "allowCopy", "<PERSON><PERSON><PERSON>", "desiredSize", "Size", "fill", "stroke", "mouseDrop", "e", "handleDropCompletion", "isGroup", "text", "horiz", "grid", "Panel", "gridCellSize", "add", "strokeWidth", "interval", "activeDiagramChanges", "id", "currentDiagram", "getDiagramDetails", "currentProjectDiagramsChanges", "length", "currentProjectChanges", "project", "setProjectLinks", "initDiagram", "getPaletteDiagramDetails", "onDiagramSelectionChange", "diagramId", "activeDiagram", "find", "d", "folders", "foundDiagram", "findDiagramInFolders", "setActiveDiagram", "setPropertyData", "navigate", "replaceUrl", "deleteDiagramEvent", "onDeleteDiagram", "downloadDiagramEvent", "initiateDiagramDownload", "downloadAllDiagramEvent", "isForOnlyImage", "propertyDataChanges", "propertyData", "allowDrop", "dialogRef", "open", "width", "data", "title", "reject", "confirm", "afterClosed", "isConfirm", "deleteDiagram", "filter", "setCurrentProjectDiagrams", "ngOnDestroy", "clearLinks", "clearLinkToLinks", "setSelectedVersion", "unsubscribe", "onColorPickerSelection", "isClicked", "onUpdateProperties", "updatedNode", "treeNode", "getUpdatedProperties", "onCreateFolder", "stopPropagation", "createNewFolder", "libraryTreeComponent", "dataSource", "treeControl", "expand", "openVersionHistory", "setShowVersionHistory", "setProjectAccess", "closeVersionHistory", "folder", "childFolders", "foundInChild", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\pages\\home\\diagram-editor\\diagram-editor.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  effect,\r\n  HostListener,\r\n  On<PERSON><PERSON><PERSON>,\r\n  OnInit,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport * as go from 'gojs';\r\nimport { Subscription } from 'rxjs';\r\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\r\nimport { LibraryTreeComponent } from 'src/app/core/components/library-tree/library-tree.component';\r\nimport { AccessService } from 'src/app/core/services/access/access.service';\r\nimport { AppService } from 'src/app/core/services/app.service';\r\nimport { CardinalityService } from 'src/app/core/services/cardinality/cardinality.service';\r\nimport { DiagramService } from 'src/app/core/services/diagram/diagram.service';\r\nimport { GojsService } from 'src/app/core/services/gojs/gojs.service';\r\nimport { LoaderService } from 'src/app/core/services/loader/loader.service';\r\nimport { NavbarService } from 'src/app/core/services/navbar/navbar.service';\r\nimport { ProjectService } from 'src/app/core/services/project/project.service';\r\nimport { PropertyService } from 'src/app/core/services/property/property.service';\r\nimport { TreeNodeService } from 'src/app/core/services/treeNode/tree-node.service';\r\nimport { VersionHistoryService } from 'src/app/core/services/versionHistory/version-history.service';\r\nimport { AttributeOption } from 'src/app/shared/model/attribute';\r\nimport { FolderDTO } from 'src/app/shared/model/class';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { ConfirmDialogData } from 'src/app/shared/model/dialog';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType, ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './diagram-editor.component.html',\r\n  styleUrls: ['./diagram-editor.component.scss'],\r\n})\r\nexport class DiagramEditorComponent implements OnInit, OnDestroy {\r\n  private $ = go.GraphObject.make;\r\n  isComponentPanelExpanded = false;\r\n  isLibraryPanelExpanded = true;\r\n  diagram!: go.Diagram;\r\n  diagrams: Diagram[] = [];\r\n  selectedDiagramId: number = -1;\r\n  project!: ProjectDetails;\r\n  projectId!: number;\r\n  hasEditAccessOnly: boolean = false;\r\n  propertyData!:\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsLinkNode\r\n    | GojsFolderNode;\r\n\r\n  attributeTypes: AttributeOption[] = [];\r\n  private currentDiagram!: Diagram;\r\n  private _downloadSub: Subscription | null = null;\r\n  private _downloadAllSub: Subscription | null = null;\r\n  private _currentProjectSub: Subscription | null = null;\r\n  private _colorSelectionSub: Subscription | null = null;\r\n  private _diagramSub: Subscription | null = null;\r\n  private _currentProjDiagramsSub: Subscription | null = null;\r\n  private _deleteDiagramSub: Subscription | null = null;\r\n  private _propertyDataSub: Subscription | null = null;\r\n  private _attributeTypeSub: Subscription | null = null;\r\n  showVersionHistory = false;\r\n  currentProjectDetails!: ProjectDetails;\r\n  isLoading$ = this.loaderService.isLoading$;\r\n  private _diagramLayoutSub: Subscription | null = null;\r\n  @ViewChild(LibraryTreeComponent)\r\n  libraryTreeComponent!: LibraryTreeComponent;\r\n  selectedNodeTag: string = TreeNodeTag.Project;\r\n  constructor(\r\n    private diagramService: DiagramService,\r\n    private projectService: ProjectService,\r\n    private propertyService: PropertyService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private linkService: CardinalityService,\r\n    private accessService: AccessService,\r\n    private goJsService: GojsService,\r\n    private diagramUtils: DiagramUtils,\r\n    private loaderService: LoaderService,\r\n    private treeNodeService: TreeNodeService,\r\n    private navbarService: NavbarService,\r\n    private dialog: MatDialog,\r\n    private appService: AppService,\r\n    private versionHistoryService: VersionHistoryService,\r\n    private undoRedoService: UndoRedoService,\r\n    private transactionManagerService: TransactionManagerService\r\n  ) {\r\n    effect(() => {\r\n      this.showVersionHistory = this.navbarService.showVersionHistory();\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.appService.setIsInitProject(true);\r\n    go.Diagram.licenseKey = environment.licenseKey;\r\n    this.projectId = parseInt(this.route.snapshot.paramMap.get('id') ?? '');\r\n\r\n    // Check if a specific diagram ID is provided in the route\r\n    const diagramIdParam = this.route.snapshot.paramMap.get('idDiagram');\r\n    const initialDiagramId = diagramIdParam\r\n      ? parseInt(diagramIdParam)\r\n      : undefined;\r\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${initialDiagramId}`;\r\n\r\n    if (localStorage.getItem('reloaded') || localStorage.getItem('copyUrl')) {\r\n      // Open the project with the specific diagram if provided\r\n      this.projectService.openProject(this.projectId, true, initialDiagramId);\r\n      this.linkService.initLinkTypes();\r\n      localStorage.removeItem('reloaded');\r\n      localStorage.removeItem('copyUrl');\r\n    }\r\n\r\n    this.configureDiagramProperties();\r\n    this.initProject();\r\n    this.accessService.accessTypeChanges().subscribe((response) => {\r\n      this.hasEditAccessOnly = response != AccessType.Viewer;\r\n    });\r\n    this.subscribeToDiagramService();\r\n    this._attributeTypeSub = this.diagramUtils\r\n      .getAttributeTypes()\r\n      .subscribe((options) => {\r\n        this.attributeTypes = options;\r\n      });\r\n  }\r\n\r\n  @HostListener('window:beforeunload', ['$event'])\r\n  unloadHandler(event: BeforeUnloadEvent): void {\r\n    event.preventDefault();\r\n    this.projectService.handleProjectUnlock(this.projectId);\r\n    localStorage.setItem('reloaded', 'true');\r\n  }\r\n  /**\r\n   * Configures properties for the main diagram.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  configureDiagramProperties(): void {\r\n    this.diagram = this.$(go.Diagram, 'diagramDiv', {\r\n      initialAutoScale: go.AutoScale.Uniform,\r\n      allowCopy: false,\r\n      'animationManager.isEnabled': false,\r\n      'linkingTool.isEnabled': true, // invoked explicitly by drawLink function, below\r\n      'draggingTool.isGridSnapEnabled': true,\r\n      'linkingTool.isUnconnectedLinkValid': false,\r\n      'linkingTool.portGravity': 20,\r\n      'relinkingTool.isUnconnectedLinkValid': false,\r\n      'relinkingTool.portGravity': 10,\r\n      'linkReshapingTool.handleArchetype': this.$(go.Shape, 'Diamond', {\r\n        desiredSize: new go.Size(7, 7),\r\n        fill: 'lightblue',\r\n        stroke: '#0069B4',\r\n      }),\r\n      mouseDrop: (e: go.InputEvent) =>\r\n        this.goJsService.handleDropCompletion(e, null),\r\n      'commandHandler.archetypeGroupData': {\r\n        isGroup: true,\r\n        text: 'Group',\r\n        horiz: false,\r\n      },\r\n      'undoManager.isEnabled': true,\r\n      'relinkingTool.isEnabled': true,\r\n    });\r\n    this.diagram.grid = new go.Panel('Grid', {\r\n      gridCellSize: new go.Size(10, 10),\r\n    }).add(\r\n      new go.Shape('LineH', {\r\n        stroke: 'lightgray',\r\n        strokeWidth: 0.5,\r\n        interval: 1,\r\n      }),\r\n      new go.Shape('LineH', { stroke: 'gray', strokeWidth: 0.5, interval: 5 }),\r\n      new go.Shape('LineH', { stroke: 'gray', strokeWidth: 1.0, interval: 10 }),\r\n      new go.Shape('LineV', {\r\n        stroke: 'lightgray',\r\n        strokeWidth: 0.5,\r\n        interval: 1,\r\n      }),\r\n      new go.Shape('LineV', { stroke: 'gray', strokeWidth: 0.5, interval: 5 }),\r\n      new go.Shape('LineV', { stroke: 'gray', strokeWidth: 1.0, interval: 10 })\r\n    );\r\n  }\r\n  private initProject() {\r\n    this._diagramSub = this.diagramUtils\r\n      .activeDiagramChanges()\r\n      .subscribe((diagram) => {\r\n        if (diagram && diagram.id) {\r\n          this.currentDiagram = diagram;\r\n          this.selectedDiagramId = this.currentDiagram.id!;\r\n          this.diagramService.getDiagramDetails(diagram.id!);\r\n        }\r\n      });\r\n\r\n    this._currentProjDiagramsSub = this.diagramUtils\r\n      .currentProjectDiagramsChanges()\r\n      .subscribe((diagrams) => {\r\n        if (diagrams.length > 0) {\r\n          this.diagrams = diagrams;\r\n        }\r\n      });\r\n\r\n    this._currentProjectSub = this.projectService\r\n      .currentProjectChanges()\r\n      .subscribe((project) => {\r\n        if (project) {\r\n          this.linkService.setProjectLinks(project);\r\n          this.goJsService.initDiagram(this.diagram);\r\n          this.diagramService.getPaletteDiagramDetails();\r\n        }\r\n      });\r\n  }\r\n\r\n  onDiagramSelectionChange(diagramId: number) {\r\n    this.selectedDiagramId = diagramId;\r\n    let activeDiagram = this.diagrams.find((d) => d.id === diagramId);\r\n\r\n    // If not found in main diagrams, search in project folders\r\n    if (!activeDiagram && this.project && this.project.folders) {\r\n      const foundDiagram = this.findDiagramInFolders(\r\n        this.project.folders,\r\n        diagramId\r\n      );\r\n      if (foundDiagram) {\r\n        activeDiagram = foundDiagram;\r\n      }\r\n    }\r\n\r\n    if (activeDiagram) {\r\n      this.diagramUtils.setActiveDiagram(activeDiagram);\r\n    }\r\n    this.propertyService.setPropertyData(null);\r\n\r\n    // Update the URL to include the selected diagram ID without reloading the page\r\n    this.router.navigate([`/editor/${this.projectId}/diagram/${diagramId}`], {\r\n      replaceUrl: true, // Replace the current URL instead of adding a new history entry\r\n    });\r\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${diagramId}`;\r\n  }\r\n\r\n  /**\r\n   * Subscribes to various diagram service subjects to handle diagram actions.\r\n   */\r\n  private subscribeToDiagramService() {\r\n    this._deleteDiagramSub = this.diagramService.deleteDiagramEvent.subscribe(\r\n      () => {\r\n        this.onDeleteDiagram();\r\n      }\r\n    );\r\n    this._downloadSub = this.diagramService.downloadDiagramEvent.subscribe(\r\n      () => {\r\n        this.diagramService.initiateDiagramDownload(true, false);\r\n      }\r\n    );\r\n    this._downloadAllSub =\r\n      this.diagramService.downloadAllDiagramEvent.subscribe(\r\n        (isForOnlyImage) => {\r\n          this.diagramService.initiateDiagramDownload(false, isForOnlyImage);\r\n          this.onDiagramSelectionChange(this.currentDiagram.id!);\r\n        }\r\n      );\r\n    this._propertyDataSub = this.propertyService\r\n      .propertyDataChanges()\r\n      .subscribe((propertyData) => {\r\n        if (propertyData) this.propertyData = propertyData;\r\n      });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      if (this.diagram) this.diagram.allowDrop = diagrams.length > 0;\r\n    });\r\n  }\r\n\r\n  onDeleteDiagram() {\r\n    const dialogRef = this.dialog.open<\r\n      DialogConfirmationComponent,\r\n      ConfirmDialogData,\r\n      boolean\r\n    >(DialogConfirmationComponent, {\r\n      width: '320px',\r\n      data: {\r\n        title: 'dialog.title',\r\n        reject: 'dialog.no',\r\n        confirm: 'dialog.yes',\r\n      },\r\n    });\r\n    dialogRef.afterClosed().subscribe((isConfirm) => {\r\n      if (isConfirm) {\r\n        if (this.selectedDiagramId) {\r\n          this.diagramService.deleteDiagram(this.selectedDiagramId);\r\n          this.treeNodeService.deleteDiagram(\r\n            `atTag${GojsNodeCategory.Diagram}_${this.selectedDiagramId}`\r\n          );\r\n          this.diagrams = this.diagrams.filter(\r\n            (diagram: Diagram) => diagram.id !== this.selectedDiagramId\r\n          );\r\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\r\n          if (this.diagrams.length > 0) {\r\n            this.currentDiagram = this.diagrams[0];\r\n            this.selectedDiagramId = this.currentDiagram.id!;\r\n            this.diagramUtils.setActiveDiagram(this.currentDiagram);\r\n\r\n            // Update the URL to reflect the new active diagram\r\n            this.router.navigate(\r\n              [`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],\r\n              {\r\n                replaceUrl: true,\r\n              }\r\n            );\r\n          } else {\r\n            this.diagramUtils.setActiveDiagram(null);\r\n\r\n            // If there are no diagrams left, navigate to a placeholder URL\r\n            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {\r\n              replaceUrl: true,\r\n            });\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.linkService.clearLinks();\r\n    this.linkService.clearLinkToLinks();\r\n    this.versionHistoryService.setSelectedVersion(null);\r\n    this.projectService.handleProjectUnlock(this.projectId);\r\n    if (this._downloadSub) this._downloadSub.unsubscribe();\r\n    if (this._downloadAllSub) this._downloadAllSub.unsubscribe();\r\n    if (this._colorSelectionSub) this._colorSelectionSub.unsubscribe();\r\n    if (this._diagramSub) this._diagramSub.unsubscribe();\r\n    if (this._currentProjDiagramsSub)\r\n      this._currentProjDiagramsSub.unsubscribe();\r\n    if (this._deleteDiagramSub) this._deleteDiagramSub.unsubscribe();\r\n    if (this._propertyDataSub) this._propertyDataSub.unsubscribe();\r\n    if (this._currentProjectSub) this._currentProjectSub.unsubscribe();\r\n    if (this._attributeTypeSub) this._attributeTypeSub.unsubscribe();\r\n    if (this._diagramLayoutSub) this._diagramLayoutSub.unsubscribe();\r\n    this.propertyService.setPropertyData(null);\r\n  }\r\n\r\n  /**\r\n   * Subscribes to color selection changes and updates the component state accordingly.\r\n   */\r\n  onColorPickerSelection(isClicked: boolean) {\r\n    if (isClicked) {\r\n      this.isComponentPanelExpanded = false;\r\n      this.isLibraryPanelExpanded = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Subscribes to property data updates and handles updates to the diagram and palette.\r\n   */\r\n  onUpdateProperties(\r\n    updatedNode:\r\n      | GojsDiagramClassNode\r\n      | GojsDiagramEnumerationNode\r\n      | GojsDiagramAttributeNode\r\n      | GojsDiagramLiteralNode\r\n      | GojsLinkNode\r\n      | GojsFolderNode,\r\n    treeNode: TreeNode | null\r\n  ): void {\r\n    this.diagramService.getUpdatedProperties(updatedNode, treeNode);\r\n  }\r\n\r\n  onCreateFolder(event: Event) {\r\n    event.stopPropagation();\r\n    this.goJsService.createNewFolder('New Folder', this.projectId);\r\n    if (\r\n      this.libraryTreeComponent.dataSource &&\r\n      this.libraryTreeComponent.dataSource.data.length > 0\r\n    ) {\r\n      this.libraryTreeComponent.treeControl.expand(\r\n        this.libraryTreeComponent.dataSource.data[0]\r\n      );\r\n    }\r\n  }\r\n\r\n  // Function to open version history\r\n  openVersionHistory() {\r\n    this.navbarService.setShowVersionHistory(true);\r\n    this.accessService.setProjectAccess(AccessType.Viewer);\r\n  }\r\n\r\n  // Function to close version history\r\n  closeVersionHistory() {\r\n    this.navbarService.setShowVersionHistory(false);\r\n    // this.accessService.setProjectAccess(this.project.accessType);\r\n  }\r\n\r\n  /**\r\n   * Recursively searches for a diagram in project folders by ID\r\n   * @param folders - Array of folders to search in\r\n   * @param diagramId - ID of the diagram to find\r\n   * @returns The found diagram or null if not found\r\n   */\r\n  private findDiagramInFolders(\r\n    folders: FolderDTO[],\r\n    diagramId: number\r\n  ): Diagram | null {\r\n    for (const folder of folders) {\r\n      // Search in current folder's diagrams\r\n      if (folder.diagrams) {\r\n        const foundDiagram = folder.diagrams.find((d) => d.id === diagramId);\r\n        if (foundDiagram) {\r\n          return foundDiagram;\r\n        }\r\n      }\r\n\r\n      // Recursively search in child folders\r\n      if (folder.childFolders && folder.childFolders.length > 0) {\r\n        const foundInChild = this.findDiagramInFolders(\r\n          folder.childFolders,\r\n          diagramId\r\n        );\r\n        if (foundInChild) {\r\n          return foundInChild;\r\n        }\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,MAAM,EACNC,YAAY,EAGZC,SAAS,QACJ,eAAe;AAGtB,OAAO,KAAKC,EAAE,MAAM,MAAM;AAE1B,SAASC,2BAA2B,QAAQ,2EAA2E;AACvH,SAASC,oBAAoB,QAAQ,6DAA6D;AAgBlG,SAOEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAwB,8BAA8B;AACzE,SAAmBC,WAAW,QAAQ,+BAA+B;AAErE,SAASC,WAAW,QAAQ,8BAA8B;AAOnD,WAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAoCjCC,YACUC,cAA8B,EAC9BC,cAA8B,EAC9BC,eAAgC,EAChCC,KAAqB,EACrBC,MAAc,EACdC,WAA+B,EAC/BC,aAA4B,EAC5BC,WAAwB,EACxBC,YAA0B,EAC1BC,aAA4B,EAC5BC,eAAgC,EAChCC,aAA4B,EAC5BC,MAAiB,EACjBC,UAAsB,EACtBC,qBAA4C,EAC5CC,eAAgC,EAChCC,yBAAoD;IAhBpD,KAAAhB,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,yBAAyB,GAAzBA,yBAAyB;IApD3B,KAAAC,CAAC,GAAG1B,EAAE,CAAC2B,WAAW,CAACC,IAAI;IAC/B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,sBAAsB,GAAG,IAAI;IAE7B,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,iBAAiB,GAAW,CAAC,CAAC;IAG9B,KAAAC,iBAAiB,GAAY,KAAK;IASlC,KAAAC,cAAc,GAAsB,EAAE;IAE9B,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,eAAe,GAAwB,IAAI;IAC3C,KAAAC,kBAAkB,GAAwB,IAAI;IAC9C,KAAAC,kBAAkB,GAAwB,IAAI;IAC9C,KAAAC,WAAW,GAAwB,IAAI;IACvC,KAAAC,uBAAuB,GAAwB,IAAI;IACnD,KAAAC,iBAAiB,GAAwB,IAAI;IAC7C,KAAAC,gBAAgB,GAAwB,IAAI;IAC5C,KAAAC,iBAAiB,GAAwB,IAAI;IACrD,KAAAC,kBAAkB,GAAG,KAAK;IAE1B,KAAAC,UAAU,GAAG,IAAI,CAAC3B,aAAa,CAAC2B,UAAU;IAClC,KAAAC,iBAAiB,GAAwB,IAAI;IAGrD,KAAAC,eAAe,GAAW1C,WAAW,CAAC2C,OAAO;IAoB3CnD,MAAM,CAAC,MAAK;MACV,IAAI,CAAC+C,kBAAkB,GAAG,IAAI,CAACxB,aAAa,CAACwB,kBAAkB,EAAE;IACnE,CAAC,CAAC;EACJ;EAEAK,QAAQA,CAAA;IACN,IAAI,CAAC3B,UAAU,CAAC4B,gBAAgB,CAAC,IAAI,CAAC;IACtClD,EAAE,CAACmD,OAAO,CAACC,UAAU,GAAG9C,WAAW,CAAC8C,UAAU;IAC9C,IAAI,CAACC,SAAS,GAAGC,QAAQ,CAAC,IAAI,CAAC1C,KAAK,CAAC2C,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAEvE;IACA,MAAMC,cAAc,GAAG,IAAI,CAAC9C,KAAK,CAAC2C,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;IACpE,MAAME,gBAAgB,GAAGD,cAAc,GACnCJ,QAAQ,CAACI,cAAc,CAAC,GACxBE,SAAS;IACb,IAAI,CAACb,eAAe,GAAG,QAAQ5C,gBAAgB,CAACgD,OAAO,IAAIQ,gBAAgB,EAAE;IAE7E,IAAIE,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MACvE;MACA,IAAI,CAACpD,cAAc,CAACqD,WAAW,CAAC,IAAI,CAACV,SAAS,EAAE,IAAI,EAAEM,gBAAgB,CAAC;MACvE,IAAI,CAAC7C,WAAW,CAACkD,aAAa,EAAE;MAChCH,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;MACnCJ,YAAY,CAACI,UAAU,CAAC,SAAS,CAAC;;IAGpC,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACpD,aAAa,CAACqD,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC5D,IAAI,CAACrC,iBAAiB,GAAGqC,QAAQ,IAAIlE,UAAU,CAACmE,MAAM;IACxD,CAAC,CAAC;IACF,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAAC1B,YAAY,CACvCwD,iBAAiB,EAAE,CACnBJ,SAAS,CAAEK,OAAO,IAAI;MACrB,IAAI,CAACxC,cAAc,GAAGwC,OAAO;IAC/B,CAAC,CAAC;EACN;EAGAC,aAAaA,CAACC,KAAwB;IACpCA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACnE,cAAc,CAACoE,mBAAmB,CAAC,IAAI,CAACzB,SAAS,CAAC;IACvDQ,YAAY,CAACkB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;EAC1C;EACA;;;;EAIAb,0BAA0BA,CAAA;IACxB,IAAI,CAACc,OAAO,GAAG,IAAI,CAACtD,CAAC,CAAC1B,EAAE,CAACmD,OAAO,EAAE,YAAY,EAAE;MAC9C8B,gBAAgB,EAAEjF,EAAE,CAACkF,SAAS,CAACC,OAAO;MACtCC,SAAS,EAAE,KAAK;MAChB,4BAA4B,EAAE,KAAK;MACnC,uBAAuB,EAAE,IAAI;MAC7B,gCAAgC,EAAE,IAAI;MACtC,oCAAoC,EAAE,KAAK;MAC3C,yBAAyB,EAAE,EAAE;MAC7B,sCAAsC,EAAE,KAAK;MAC7C,2BAA2B,EAAE,EAAE;MAC/B,mCAAmC,EAAE,IAAI,CAAC1D,CAAC,CAAC1B,EAAE,CAACqF,KAAK,EAAE,SAAS,EAAE;QAC/DC,WAAW,EAAE,IAAItF,EAAE,CAACuF,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9BC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT,CAAC;MACFC,SAAS,EAAGC,CAAgB,IAC1B,IAAI,CAAC3E,WAAW,CAAC4E,oBAAoB,CAACD,CAAC,EAAE,IAAI,CAAC;MAChD,mCAAmC,EAAE;QACnCE,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;OACR;MACD,uBAAuB,EAAE,IAAI;MAC7B,yBAAyB,EAAE;KAC5B,CAAC;IACF,IAAI,CAACf,OAAO,CAACgB,IAAI,GAAG,IAAIhG,EAAE,CAACiG,KAAK,CAAC,MAAM,EAAE;MACvCC,YAAY,EAAE,IAAIlG,EAAE,CAACuF,IAAI,CAAC,EAAE,EAAE,EAAE;KACjC,CAAC,CAACY,GAAG,CACJ,IAAInG,EAAE,CAACqF,KAAK,CAAC,OAAO,EAAE;MACpBI,MAAM,EAAE,WAAW;MACnBW,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE;KACX,CAAC,EACF,IAAIrG,EAAE,CAACqF,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAC,CAAE,CAAC,EACxE,IAAIrG,EAAE,CAACqF,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAE,CAAE,CAAC,EACzE,IAAIrG,EAAE,CAACqF,KAAK,CAAC,OAAO,EAAE;MACpBI,MAAM,EAAE,WAAW;MACnBW,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE;KACX,CAAC,EACF,IAAIrG,EAAE,CAACqF,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAC,CAAE,CAAC,EACxE,IAAIrG,EAAE,CAACqF,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAE,CAAE,CAAC,CAC1E;EACH;EACQlC,WAAWA,CAAA;IACjB,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACtB,YAAY,CACjCqF,oBAAoB,EAAE,CACtBjC,SAAS,CAAEW,OAAO,IAAI;MACrB,IAAIA,OAAO,IAAIA,OAAO,CAACuB,EAAE,EAAE;QACzB,IAAI,CAACC,cAAc,GAAGxB,OAAO;QAC7B,IAAI,CAAChD,iBAAiB,GAAG,IAAI,CAACwE,cAAc,CAACD,EAAG;QAChD,IAAI,CAAC9F,cAAc,CAACgG,iBAAiB,CAACzB,OAAO,CAACuB,EAAG,CAAC;;IAEtD,CAAC,CAAC;IAEJ,IAAI,CAAC/D,uBAAuB,GAAG,IAAI,CAACvB,YAAY,CAC7CyF,6BAA6B,EAAE,CAC/BrC,SAAS,CAAEtC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,CAAC4E,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAAC5E,QAAQ,GAAGA,QAAQ;;IAE5B,CAAC,CAAC;IAEJ,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAAC3B,cAAc,CAC1CkG,qBAAqB,EAAE,CACvBvC,SAAS,CAAEwC,OAAO,IAAI;MACrB,IAAIA,OAAO,EAAE;QACX,IAAI,CAAC/F,WAAW,CAACgG,eAAe,CAACD,OAAO,CAAC;QACzC,IAAI,CAAC7F,WAAW,CAAC+F,WAAW,CAAC,IAAI,CAAC/B,OAAO,CAAC;QAC1C,IAAI,CAACvE,cAAc,CAACuG,wBAAwB,EAAE;;IAElD,CAAC,CAAC;EACN;EAEAC,wBAAwBA,CAACC,SAAiB;IACxC,IAAI,CAAClF,iBAAiB,GAAGkF,SAAS;IAClC,IAAIC,aAAa,GAAG,IAAI,CAACpF,QAAQ,CAACqF,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACd,EAAE,KAAKW,SAAS,CAAC;IAEjE;IACA,IAAI,CAACC,aAAa,IAAI,IAAI,CAACN,OAAO,IAAI,IAAI,CAACA,OAAO,CAACS,OAAO,EAAE;MAC1D,MAAMC,YAAY,GAAG,IAAI,CAACC,oBAAoB,CAC5C,IAAI,CAACX,OAAO,CAACS,OAAO,EACpBJ,SAAS,CACV;MACD,IAAIK,YAAY,EAAE;QAChBJ,aAAa,GAAGI,YAAY;;;IAIhC,IAAIJ,aAAa,EAAE;MACjB,IAAI,CAAClG,YAAY,CAACwG,gBAAgB,CAACN,aAAa,CAAC;;IAEnD,IAAI,CAACxG,eAAe,CAAC+G,eAAe,CAAC,IAAI,CAAC;IAE1C;IACA,IAAI,CAAC7G,MAAM,CAAC8G,QAAQ,CAAC,CAAC,WAAW,IAAI,CAACtE,SAAS,YAAY6D,SAAS,EAAE,CAAC,EAAE;MACvEU,UAAU,EAAE,IAAI,CAAE;KACnB,CAAC;IACF,IAAI,CAAC7E,eAAe,GAAG,QAAQ5C,gBAAgB,CAACgD,OAAO,IAAI+D,SAAS,EAAE;EACxE;EAEA;;;EAGQ1C,yBAAyBA,CAAA;IAC/B,IAAI,CAAC/B,iBAAiB,GAAG,IAAI,CAAChC,cAAc,CAACoH,kBAAkB,CAACxD,SAAS,CACvE,MAAK;MACH,IAAI,CAACyD,eAAe,EAAE;IACxB,CAAC,CACF;IACD,IAAI,CAAC3F,YAAY,GAAG,IAAI,CAAC1B,cAAc,CAACsH,oBAAoB,CAAC1D,SAAS,CACpE,MAAK;MACH,IAAI,CAAC5D,cAAc,CAACuH,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC;IAC1D,CAAC,CACF;IACD,IAAI,CAAC5F,eAAe,GAClB,IAAI,CAAC3B,cAAc,CAACwH,uBAAuB,CAAC5D,SAAS,CAClD6D,cAAc,IAAI;MACjB,IAAI,CAACzH,cAAc,CAACuH,uBAAuB,CAAC,KAAK,EAAEE,cAAc,CAAC;MAClE,IAAI,CAACjB,wBAAwB,CAAC,IAAI,CAACT,cAAc,CAACD,EAAG,CAAC;IACxD,CAAC,CACF;IACH,IAAI,CAAC7D,gBAAgB,GAAG,IAAI,CAAC/B,eAAe,CACzCwH,mBAAmB,EAAE,CACrB9D,SAAS,CAAE+D,YAAY,IAAI;MAC1B,IAAIA,YAAY,EAAE,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpD,CAAC,CAAC;IACJ,IAAI,CAACnH,YAAY,CAACyF,6BAA6B,EAAE,CAACrC,SAAS,CAAEtC,QAAQ,IAAI;MACvE,IAAI,IAAI,CAACiD,OAAO,EAAE,IAAI,CAACA,OAAO,CAACqD,SAAS,GAAGtG,QAAQ,CAAC4E,MAAM,GAAG,CAAC;IAChE,CAAC,CAAC;EACJ;EAEAmB,eAAeA,CAAA;IACb,MAAMQ,SAAS,GAAG,IAAI,CAACjH,MAAM,CAACkH,IAAI,CAIhCtI,2BAA2B,EAAE;MAC7BuI,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QACJC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,WAAW;QACnBC,OAAO,EAAE;;KAEZ,CAAC;IACFN,SAAS,CAACO,WAAW,EAAE,CAACxE,SAAS,CAAEyE,SAAS,IAAI;MAC9C,IAAIA,SAAS,EAAE;QACb,IAAI,IAAI,CAAC9G,iBAAiB,EAAE;UAC1B,IAAI,CAACvB,cAAc,CAACsI,aAAa,CAAC,IAAI,CAAC/G,iBAAiB,CAAC;UACzD,IAAI,CAACb,eAAe,CAAC4H,aAAa,CAChC,QAAQ5I,gBAAgB,CAACgD,OAAO,IAAI,IAAI,CAACnB,iBAAiB,EAAE,CAC7D;UACD,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACiH,MAAM,CACjChE,OAAgB,IAAKA,OAAO,CAACuB,EAAE,KAAK,IAAI,CAACvE,iBAAiB,CAC5D;UACD,IAAI,CAACf,YAAY,CAACgI,yBAAyB,CAAC,IAAI,CAAClH,QAAQ,CAAC;UAC1D,IAAI,IAAI,CAACA,QAAQ,CAAC4E,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,CAACH,cAAc,GAAG,IAAI,CAACzE,QAAQ,CAAC,CAAC,CAAC;YACtC,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACwE,cAAc,CAACD,EAAG;YAChD,IAAI,CAACtF,YAAY,CAACwG,gBAAgB,CAAC,IAAI,CAACjB,cAAc,CAAC;YAEvD;YACA,IAAI,CAAC3F,MAAM,CAAC8G,QAAQ,CAClB,CAAC,WAAW,IAAI,CAACtE,SAAS,YAAY,IAAI,CAACmD,cAAc,CAACD,EAAE,EAAE,CAAC,EAC/D;cACEqB,UAAU,EAAE;aACb,CACF;WACF,MAAM;YACL,IAAI,CAAC3G,YAAY,CAACwG,gBAAgB,CAAC,IAAI,CAAC;YAExC;YACA,IAAI,CAAC5G,MAAM,CAAC8G,QAAQ,CAAC,CAAC,WAAW,IAAI,CAACtE,SAAS,YAAY,CAAC,EAAE;cAC5DuE,UAAU,EAAE;aACb,CAAC;;;;IAIV,CAAC,CAAC;EACJ;EAEAsB,WAAWA,CAAA;IACT,IAAI,CAACpI,WAAW,CAACqI,UAAU,EAAE;IAC7B,IAAI,CAACrI,WAAW,CAACsI,gBAAgB,EAAE;IACnC,IAAI,CAAC7H,qBAAqB,CAAC8H,kBAAkB,CAAC,IAAI,CAAC;IACnD,IAAI,CAAC3I,cAAc,CAACoE,mBAAmB,CAAC,IAAI,CAACzB,SAAS,CAAC;IACvD,IAAI,IAAI,CAAClB,YAAY,EAAE,IAAI,CAACA,YAAY,CAACmH,WAAW,EAAE;IACtD,IAAI,IAAI,CAAClH,eAAe,EAAE,IAAI,CAACA,eAAe,CAACkH,WAAW,EAAE;IAC5D,IAAI,IAAI,CAAChH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACgH,WAAW,EAAE;IAClE,IAAI,IAAI,CAAC/G,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC+G,WAAW,EAAE;IACpD,IAAI,IAAI,CAAC9G,uBAAuB,EAC9B,IAAI,CAACA,uBAAuB,CAAC8G,WAAW,EAAE;IAC5C,IAAI,IAAI,CAAC7G,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAAC6G,WAAW,EAAE;IAChE,IAAI,IAAI,CAAC5G,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAAC4G,WAAW,EAAE;IAC9D,IAAI,IAAI,CAACjH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACiH,WAAW,EAAE;IAClE,IAAI,IAAI,CAAC3G,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAAC2G,WAAW,EAAE;IAChE,IAAI,IAAI,CAACxG,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACwG,WAAW,EAAE;IAChE,IAAI,CAAC3I,eAAe,CAAC+G,eAAe,CAAC,IAAI,CAAC;EAC5C;EAEA;;;EAGA6B,sBAAsBA,CAACC,SAAkB;IACvC,IAAIA,SAAS,EAAE;MACb,IAAI,CAAC3H,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACC,sBAAsB,GAAG,KAAK;;EAEvC;EAEA;;;EAGA2H,kBAAkBA,CAChBC,WAMkB,EAClBC,QAAyB;IAEzB,IAAI,CAAClJ,cAAc,CAACmJ,oBAAoB,CAACF,WAAW,EAAEC,QAAQ,CAAC;EACjE;EAEAE,cAAcA,CAACjF,KAAY;IACzBA,KAAK,CAACkF,eAAe,EAAE;IACvB,IAAI,CAAC9I,WAAW,CAAC+I,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC1G,SAAS,CAAC;IAC9D,IACE,IAAI,CAAC2G,oBAAoB,CAACC,UAAU,IACpC,IAAI,CAACD,oBAAoB,CAACC,UAAU,CAACxB,IAAI,CAAC9B,MAAM,GAAG,CAAC,EACpD;MACA,IAAI,CAACqD,oBAAoB,CAACE,WAAW,CAACC,MAAM,CAC1C,IAAI,CAACH,oBAAoB,CAACC,UAAU,CAACxB,IAAI,CAAC,CAAC,CAAC,CAC7C;;EAEL;EAEA;EACA2B,kBAAkBA,CAAA;IAChB,IAAI,CAAChJ,aAAa,CAACiJ,qBAAqB,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACtJ,aAAa,CAACuJ,gBAAgB,CAAClK,UAAU,CAACmE,MAAM,CAAC;EACxD;EAEA;EACAgG,mBAAmBA,CAAA;IACjB,IAAI,CAACnJ,aAAa,CAACiJ,qBAAqB,CAAC,KAAK,CAAC;IAC/C;EACF;EAEA;;;;;;EAMQ7C,oBAAoBA,CAC1BF,OAAoB,EACpBJ,SAAiB;IAEjB,KAAK,MAAMsD,MAAM,IAAIlD,OAAO,EAAE;MAC5B;MACA,IAAIkD,MAAM,CAACzI,QAAQ,EAAE;QACnB,MAAMwF,YAAY,GAAGiD,MAAM,CAACzI,QAAQ,CAACqF,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACd,EAAE,KAAKW,SAAS,CAAC;QACpE,IAAIK,YAAY,EAAE;UAChB,OAAOA,YAAY;;;MAIvB;MACA,IAAIiD,MAAM,CAACC,YAAY,IAAID,MAAM,CAACC,YAAY,CAAC9D,MAAM,GAAG,CAAC,EAAE;QACzD,MAAM+D,YAAY,GAAG,IAAI,CAAClD,oBAAoB,CAC5CgD,MAAM,CAACC,YAAY,EACnBvD,SAAS,CACV;QACD,IAAIwD,YAAY,EAAE;UAChB,OAAOA,YAAY;;;;IAIzB,OAAO,IAAI;EACb;CACD;AAjWCC,UAAA,EADC5K,SAAS,CAACG,oBAAoB,CAAC,C,mEACY;AA4D5CyK,UAAA,EADC7K,YAAY,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,0DAK/C;AAlGUS,sBAAsB,GAAAoK,UAAA,EALlC/K,SAAS,CAAC;EACTgL,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,C,EACWvK,sBAAsB,CAmYlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}